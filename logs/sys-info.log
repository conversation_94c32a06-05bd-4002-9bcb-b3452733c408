14:40:29.501 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
14:40:29.580 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 99117 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
14:40:29.581 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:40:33.944 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
14:40:35.724 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
14:40:35.727 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:40:35.728 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
14:40:35.825 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:40:37.062 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
14:40:38.177 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
14:40:41.201 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
14:40:45.949 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:40:45.972 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:40:45.972 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:40:45.974 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:40:45.975 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:40:45.975 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:40:45.975 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:40:45.976 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3d20df63
14:40:46.392 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.187:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.12, logger: org.ktorm.logging.Slf4jLoggerAdapter@2ecdfbb2, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@738ea51
14:40:49.182 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
14:40:49.196 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:40:49.209 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 20.629 seconds (process running for 22.678)
14:41:03.810 [tomcat-handler-0] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:41:03.994 [tomcat-handler-0] INFO  c.r.f.d.DynamicDataSourceContextHolder - [setDataSourceType,26] - 切换到MASTER数据源
14:41:16.091 [tomcat-handler-2] INFO  c.r.f.w.s.UserDetailsServiceImpl - [loadUserByUsername,43] - 登录用户：crcc24|1 不存在.
14:41:16.230 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[crcc24|1][Error][用户不存在/密码错误]
14:41:50.160 [tomcat-handler-4] INFO  c.r.f.w.s.UserDetailsServiceImpl - [loadUserByUsername,43] - 登录用户：sdfdmin 不存在.
14:41:50.172 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[sdfdmin][Error][用户不存在/密码错误]
14:41:59.112 [tomcat-handler-6] INFO  c.r.f.w.s.UserDetailsServiceImpl - [loadUserByUsername,43] - 登录用户：sdfdmin 不存在.
14:41:59.124 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[sdfdmin][Error][用户不存在/密码错误]
14:42:22.783 [tomcat-handler-8] INFO  c.r.f.w.s.UserDetailsServiceImpl - [loadUserByUsername,43] - 登录用户：sdfdmin 不存在.
14:42:22.796 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[sdfdmin][Error][用户不存在/密码错误]
14:42:55.794 [tomcat-handler-10] INFO  c.r.f.w.s.UserDetailsServiceImpl - [loadUserByUsername,43] - 登录用户：sdfamin 不存在.
14:42:55.806 [schedule-pool-5] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[sdfamin][Error][用户不存在/密码错误]
14:43:02.985 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[sdfadmin][Success][登录成功]
14:57:35.440 [Thread-6] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:57:35.458 [Thread-6] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-8199"]
14:57:35.720 [Thread-6] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:57:35.721 [Thread-6] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:57:35.722 [Thread-6] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:57:35.724 [Thread-6] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:57:35.737 [Thread-6] INFO  c.a.d.p.DruidDataSource - [close,2170] - {dataSource-1} closing ...
14:57:35.749 [Thread-6] INFO  c.a.d.p.DruidDataSource - [close,2243] - {dataSource-1} closed
14:57:36.514 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 99117 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
14:57:36.515 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:57:39.180 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
14:57:39.710 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
14:57:39.711 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:57:39.711 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
14:57:39.733 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:57:40.063 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
14:57:40.563 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
14:57:41.681 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-2} inited
14:57:43.176 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:57:43.177 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:57:43.177 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:57:43.178 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:57:43.178 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:57:43.178 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:57:43.178 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:57:43.178 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@a39e5e5
14:57:43.283 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.187:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.12, logger: org.ktorm.logging.Slf4jLoggerAdapter@3fa0d75c, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@24f0987
14:57:44.176 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
14:57:44.177 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:57:44.181 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 7.969 seconds (process running for 1037.627)
15:03:31.419 [tomcat-handler-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:05:39.398 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:05:39.747 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
15:05:39.748 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:05:39.749 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
15:05:39.757 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:05:39.813 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2170] - {dataSource-2} closing ...
15:05:39.823 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2243] - {dataSource-2} closed
15:05:52.998 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
15:05:53.176 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 1534 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
15:05:53.181 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
15:05:59.250 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
15:06:01.567 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
15:06:01.572 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:06:01.573 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
15:06:01.691 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:06:03.602 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
15:06:04.944 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
15:06:08.835 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
15:06:16.267 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:06:16.306 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:06:16.307 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:06:16.311 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:06:16.315 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:06:16.316 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:06:16.316 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:06:16.317 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@50369cb9
15:06:17.054 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.187:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.12, logger: org.ktorm.logging.Slf4jLoggerAdapter@2ecbdb70, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@78954bd0
15:06:23.035 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
15:06:23.061 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:06:23.092 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 31.949 seconds (process running for 35.488)
15:06:34.249 [tomcat-handler-0] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:22:09.246 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:22:09.442 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
15:22:09.442 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:22:09.442 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
15:22:09.446 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:22:09.459 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2170] - {dataSource-1} closing ...
15:22:09.464 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2243] - {dataSource-1} closed
15:22:13.602 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
15:22:13.644 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 2713 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
15:22:13.646 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
15:22:15.693 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
15:22:16.495 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
15:22:16.496 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:22:16.497 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
15:22:16.543 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:22:17.160 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
15:22:17.598 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
15:22:19.036 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
15:22:20.998 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:22:21.010 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:22:21.011 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:22:21.011 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:22:21.012 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:22:21.012 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:22:21.012 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:22:21.012 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@64deab86
15:22:21.268 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.187:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.12, logger: org.ktorm.logging.Slf4jLoggerAdapter@78f837e9, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@48357d91
15:22:22.658 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
15:22:22.673 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:22:22.680 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 9.427 seconds (process running for 10.225)
15:22:43.591 [tomcat-handler-0] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:45:13.386 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:45:13.591 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
15:45:13.591 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
15:45:13.592 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
15:45:13.593 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
15:45:13.607 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2170] - {dataSource-1} closing ...
15:45:13.610 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2243] - {dataSource-1} closed
