14:08:45.349 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
14:08:45.395 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 96276 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目/back)
14:08:45.395 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:08:47.390 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
14:08:48.230 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
14:08:48.231 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:08:48.232 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
14:08:48.281 [restartedMain] INFO  o.a.c.c.C.[.[.[/sdfundapi] - [log,173] - Initializing Spring embedded WebApplicationContext
14:08:48.899 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
14:08:49.372 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
14:08:50.891 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
14:08:52.915 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:08:52.927 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:08:52.927 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:08:52.928 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:08:52.929 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:08:52.929 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:08:52.929 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:08:52.929 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@44da35c4
14:08:53.194 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.187:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.12, logger: org.ktorm.logging.Slf4jLoggerAdapter@50b1f534, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@6e71e2c0
14:08:54.720 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
14:08:54.728 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:08:54.735 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 9.707 seconds (process running for 10.594)
14:18:33.919 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:18:34.240 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:18:34.240 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:18:34.241 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:18:34.243 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:18:34.264 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2170] - {dataSource-1} closing ...
14:18:34.281 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2243] - {dataSource-1} closed
14:18:41.252 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
14:18:41.336 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 96960 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目/back)
14:18:41.338 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:18:46.543 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
14:18:48.851 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
14:18:48.856 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:18:48.857 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
14:18:48.953 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:18:50.039 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
14:18:51.034 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
14:18:53.309 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
14:18:56.198 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:18:56.216 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:18:56.216 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:18:56.217 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:18:56.217 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:18:56.218 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:18:56.218 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:18:56.218 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@13654666
14:18:56.522 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.187:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.12, logger: org.ktorm.logging.Slf4jLoggerAdapter@3b9b3f7b, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@79acac8d
14:18:58.123 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
14:18:58.131 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:18:58.140 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 17.493 seconds (process running for 19.322)
14:33:39.190 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:33:39.570 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:33:39.571 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:33:39.572 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:33:39.575 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:33:39.613 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2170] - {dataSource-1} closing ...
14:33:39.634 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2243] - {dataSource-1} closed
14:33:46.020 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
14:33:46.248 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 97920 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目/back)
14:33:46.252 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
14:33:51.706 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
14:33:53.754 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
14:33:53.760 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:33:53.761 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
14:33:53.921 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:33:55.369 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
14:33:56.440 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
14:34:00.301 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1010] - {dataSource-1} inited
14:34:05.467 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:34:05.498 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:34:05.499 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:34:05.501 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:34:05.504 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:34:05.504 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:34:05.505 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:34:05.506 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@285e4708
14:34:06.148 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.187:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.12, logger: org.ktorm.logging.Slf4jLoggerAdapter@774f5a8e, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@2b56e231
14:34:10.449 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
14:34:10.485 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:34:10.518 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 25.923 seconds (process running for 27.942)
14:36:04.724 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:36:04.871 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
14:36:04.872 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
14:36:04.872 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
14:36:04.872 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
14:36:04.878 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2170] - {dataSource-1} closing ...
14:36:04.881 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2243] - {dataSource-1} closed
