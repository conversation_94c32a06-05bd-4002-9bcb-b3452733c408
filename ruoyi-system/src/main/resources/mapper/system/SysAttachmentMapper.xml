<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysAttachmentMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.SysAttachment">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="sourceId" column="source_id" jdbcType="VARCHAR"/>
            <result property="sourceType" column="source_type" jdbcType="VARCHAR"/>
            <result property="primaryType" column="primary_type" jdbcType="VARCHAR"/>
            <result property="subType" column="sub_type" jdbcType="VARCHAR"/>
            <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
            <result property="filePath" column="file_path" jdbcType="VARCHAR"/>
            <result property="fileExt" column="file_ext" jdbcType="VARCHAR"/>
            <result property="fileSize" column="file_size" jdbcType="INTEGER"/>
            <result property="seqNo" column="seq_no" jdbcType="INTEGER"/>
            <result property="createdTime" column="created_time" jdbcType="DATE"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,source_id,source_type,
        primary_type,sub_type,file_name,
        file_path,file_ext,file_size,
        seq_no,created_time,created_by
    </sql>
</mapper>
