<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.fund.FundContractInfoMapper">

    <resultMap type="com.ruoyi.system.domain.fund.FundContractInfo" id="FundContractInfoResult">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="projId" column="proj_id" jdbcType="VARCHAR"/>
        <result property="childId" column="child_id" jdbcType="VARCHAR"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="parta" column="parta" jdbcType="VARCHAR"/>
        <result property="partb" column="partb" jdbcType="VARCHAR"/>
        <result property="amount" column="amount" jdbcType="DECIMAL"/>
        <result property="signDate" column="sign_date" jdbcType="DATE"/>
        <result property="validatyBegin" column="validaty_begin" jdbcType="DATE"/>
        <result property="validatyEnd" column="validaty_end" jdbcType="DATE"/>
        <result property="content" column="content"  jdbcType="CLOB"/>
        <result property="seqNo" column="seq_no" jdbcType="INTEGER"/>
        <result property="amount2019" column="amount2019" jdbcType="DECIMAL"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="DATE"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,proj_id,child_id,
        code,name,parta,partb,amount,
        sign_date,validaty_begin,validaty_end,content,
        seq_no,amount_2019,create_by,create_time,
        update_by,update_time
    </sql>
</mapper>
