<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.fund.FundPaybillMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.fund.FundPaybill">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="year" column="year" jdbcType="INTEGER"/>
            <result property="serialNumber" column="serial_number" jdbcType="VARCHAR"/>
            <result property="projId" column="proj_id" jdbcType="VARCHAR"/>
            <result property="projName" column="proj_name" jdbcType="VARCHAR"/>
            <result property="projSn" column="proj_sn" jdbcType="VARCHAR"/>
            <result property="projType" column="proj_type" jdbcType="VARCHAR"/>
            <result property="childId" column="child_id" jdbcType="VARCHAR"/>
            <result property="childName" column="child_name" jdbcType="VARCHAR"/>
            <result property="projAmount" column="proj_amount" jdbcType="DECIMAL"/>
            <result property="planYear" column="plan_year" jdbcType="DECIMAL"/>
            <result property="planAmount" column="plan_amount" jdbcType="DECIMAL"/>
            <result property="payTypeid" column="pay_typeid" jdbcType="INTEGER"/>
            <result property="payTypename" column="pay_typename" jdbcType="VARCHAR"/>
            <result property="payPurposeid" column="pay_purposeid" jdbcType="INTEGER"/>
            <result property="payPurposename" column="pay_purposename" jdbcType="VARCHAR"/>
            <result property="leaderName" column="leader_name" jdbcType="VARCHAR"/>
            <result property="leaderMobile" column="leader_mobile" jdbcType="VARCHAR"/>
            <result property="contractId" column="contract_id" jdbcType="VARCHAR"/>
            <result property="contractName" column="contract_name" jdbcType="VARCHAR"/>
            <result property="contractAmount" column="contract_amount" jdbcType="DECIMAL"/>
            <result property="applyOrgid" column="apply_orgid" jdbcType="VARCHAR"/>
            <result property="applyOrgname" column="apply_orgname" jdbcType="VARCHAR"/>
            <result property="applyUserid" column="apply_userid" jdbcType="VARCHAR"/>
            <result property="applyUsername" column="apply_username" jdbcType="VARCHAR"/>
            <result property="applyDate" column="apply_date" jdbcType="DATE"/>
            <result property="applyAmount" column="apply_amount" jdbcType="DECIMAL"/>
            <result property="checkAmount" column="check_amount" jdbcType="DECIMAL"/>
            <result property="applyType" column="apply_type" jdbcType="INTEGER"/>
            <result property="applyReason" column="apply_reason" jdbcType="VARCHAR"/>
            <result property="hasFinance" column="has_finance" jdbcType="VARCHAR"/>
            <result property="financeFile" column="finance_file" jdbcType="VARCHAR"/>
            <result property="projPayed" column="proj_payed" jdbcType="DECIMAL"/>
            <result property="contractPayed" column="contract_payed" jdbcType="DECIMAL"/>
            <result property="auditOrgid" column="audit_orgid" jdbcType="VARCHAR"/>
            <result property="auditUserid" column="audit_userid" jdbcType="VARCHAR"/>
            <result property="hqdpProjcode" column="hqdp_projcode" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="DATE"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,year,serial_number,
        proj_id,proj_name,proj_sn,
        proj_type,child_id,child_name,
        proj_amount,plan_year,plan_amount,
        pay_typeid,pay_typename,pay_purposeid,
        pay_purposename,leader_name,leader_mobile,
        contract_id,contract_name,contract_amount,
        apply_orgid,apply_orgname,apply_userid,
        apply_username,apply_date,apply_amount,
        check_amount,apply_type,apply_reason,
        has_finance,finance_file,proj_payed,
        contract_payed,audit_orgid,audit_userid,
        hqdp_projcode,status,del_flag,
        create_by,create_time,update_by,
        update_time
    </sql>
</mapper>
