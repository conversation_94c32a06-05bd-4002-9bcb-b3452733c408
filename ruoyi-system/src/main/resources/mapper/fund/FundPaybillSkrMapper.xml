<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.fund.FundPaybillSkrMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.fund.FundPaybillSkr">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="billId" column="bill_id" jdbcType="VARCHAR"/>
            <result property="accountId" column="account_id" jdbcType="VARCHAR"/>
            <result property="bankName" column="bank_name" jdbcType="VARCHAR"/>
            <result property="stationCode" column="station_code" jdbcType="VARCHAR"/>
            <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
            <result property="accountNumber" column="account_number" jdbcType="VARCHAR"/>
            <result property="amount" column="amount" jdbcType="DECIMAL"/>
            <result property="seqNo" column="seq_no" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="payDate" column="pay_date" jdbcType="TIMESTAMP"/>
            <result property="payVoucher" column="pay_voucher" jdbcType="VARCHAR"/>
            <result property="payAmount" column="pay_amount" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,bill_id,account_id,
        bank_name,station_code,account_name,
        account_number,amount,seq_no,
        remark,pay_date,pay_voucher,
        pay_amount
    </sql>
</mapper>
