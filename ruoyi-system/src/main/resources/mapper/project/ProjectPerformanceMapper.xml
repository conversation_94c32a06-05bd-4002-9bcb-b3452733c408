<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.project.ProjectPerformanceMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.project.ProjectPerformance">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="projId" column="proj_id" jdbcType="VARCHAR"/>
            <result property="indexLevel1" column="index_level1" jdbcType="VARCHAR"/>
            <result property="indexLevel2" column="index_level2" jdbcType="VARCHAR"/>
            <result property="indexLevel3" column="index_level3" jdbcType="VARCHAR"/>
            <result property="indexTarget" column="index_target" jdbcType="VARCHAR"/>
            <result property="indexSort" column="index_sort" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="DATE"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="DATE"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,proj_id,index_level1,
        index_level2,index_level3,index_target,
        index_sort,create_time,create_by,
        update_time,update_by
    </sql>
</mapper>
