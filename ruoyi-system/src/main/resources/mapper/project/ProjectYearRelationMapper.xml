<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.project.ProjectYearRelationMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.project.ProjectYearRelation">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="year" column="year" jdbcType="INTEGER"/>
            <result property="projId" column="proj_id" jdbcType="VARCHAR"/>
            <result property="projType" column="proj_type" jdbcType="INTEGER"/>
            <result property="seqNo" column="seq_no" jdbcType="INTEGER"/>
            <result property="updatedTime" column="updated_time" jdbcType="DATE"/>
            <result property="isRecycle" column="is_recycle" jdbcType="VARCHAR"/>
            <result property="isClose" column="is_close" jdbcType="VARCHAR"/>
            <result property="impProj" column="imp_proj" jdbcType="VARCHAR"/>
            <result property="impCode" column="imp_code" jdbcType="INTEGER"/>
            <result property="impName" column="imp_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,year,proj_id,
        proj_type,seq_no,updated_time,
        is_recycle,is_close,imp_proj,
        imp_code,imp_name
    </sql>
</mapper>
