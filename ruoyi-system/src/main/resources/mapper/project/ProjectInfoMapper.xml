<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.project.ProjectInfoMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.project.ProjectInfo">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="seqid" column="seqid" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="formalName" column="formal_name" jdbcType="VARCHAR"/>
        <result property="applyOrgid" column="apply_orgid" jdbcType="BIGINT"/>
        <result property="applyOrgname" column="apply_orgname" jdbcType="VARCHAR"/>
        <result property="applyTime" column="apply_time" jdbcType="DATE"/>
        <result property="submitOrgid" column="submit_orgid" jdbcType="BIGINT"/>
        <result property="submitTime" column="submit_time" jdbcType="DATE"/>
        <result property="submitOrgname" column="submit_orgname" jdbcType="VARCHAR"/>
        <result property="useOrgname" column="use_orgname" jdbcType="VARCHAR"/>
        <result property="assessOrgid" column="assess_orgid" jdbcType="BIGINT"/>
        <result property="assessOrgname" column="assess_orgname" jdbcType="VARCHAR"/>
        <result property="typeId" column="type_id" jdbcType="VARCHAR"/>
        <result property="typeName" column="type_name" jdbcType="VARCHAR"/>
        <result property="natureCode" column="nature_code" jdbcType="VARCHAR"/>
        <result property="natureName" column="nature_name" jdbcType="VARCHAR"/>
        <result property="leader" column="leader" jdbcType="VARCHAR"/>
        <result property="leaderTel" column="leader_tel" jdbcType="VARCHAR"/>
        <result property="handler" column="handler" jdbcType="VARCHAR"/>
        <result property="handlerTel" column="handler_tel" jdbcType="VARCHAR"/>
        <result property="estAmount" column="est_amount" jdbcType="DECIMAL"/>
        <result property="libAmount" column="lib_amount" jdbcType="DECIMAL"/>
        <result property="checkAmount" column="check_amount" jdbcType="DECIMAL"/>
        <result property="contractAmount" column="contract_amount" jdbcType="DECIMAL"/>
        <result property="libState" column="lib_state" jdbcType="INTEGER"/>
        <result property="libReason" column="lib_reason" jdbcType="VARCHAR"/>
        <result property="checkState" column="check_state" jdbcType="INTEGER"/>
        <result property="checkReason" column="check_reason" jdbcType="VARCHAR"/>
        <result property="necessity" column="necessity" jdbcType="VARCHAR"/>
        <result property="basis" column="basis" jdbcType="VARCHAR"/>
        <result property="mainCnt" column="main_cnt" jdbcType="VARCHAR"/>
        <result property="actionPlan" column="action_plan" jdbcType="VARCHAR"/>
        <result property="buildBegin" column="build_begin" jdbcType="DATE"/>
        <result property="buildEnd" column="build_end" jdbcType="DATE"/>
        <result property="fundBegin" column="fund_begin" jdbcType="DATE"/>
        <result property="fundEnd" column="fund_end" jdbcType="DATE"/>
        <result property="isSzhyq" column="is_szhyq" jdbcType="VARCHAR"/>
        <result property="isJxhgl" column="is_jxhgl" jdbcType="VARCHAR"/>
        <result property="isZfgm" column="is_zfgm" jdbcType="VARCHAR"/>
        <result property="goal" column="goal" jdbcType="VARCHAR"/>
        <result property="basketCode" column="basket_code" jdbcType="INTEGER"/>
        <result property="basketName" column="basket_name" jdbcType="VARCHAR"/>
        <result property="baseProjid" column="base_projid" jdbcType="VARCHAR"/>
        <result property="state" column="state" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="DATE"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="DATE"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="cooperateOrgid" column="cooperate_orgid" jdbcType="VARCHAR"/>
        <result property="purpose" column="purpose" jdbcType="VARCHAR"/>
        <result property="goalDetail" column="goal_detail" jdbcType="VARCHAR"/>
    </resultMap>


    <sql id="Base_Column_List">
        id,seqid,name,
        formal_name,apply_orgid,apply_orgname,apply_time,
        submit_orgid,submit_orgname,submit_time,use_orgname,
        assess_orgid,assess_orgname,type_id,type_name,
        nature_code,nature_name,leader,
        leader_tel,handler,handler_tel,
        est_amount,lib_amount,check_amount,
        contract_amount,lib_state,lib_reason,
        check_state,check_reason,necessity,
        basis,main_cnt,action_plan,
        build_begin,build_end,fund_begin,
        fund_end,is_szhyq,is_jxhgl,
        is_zfgm,goal,basket_code,
        basket_name,base_projid,del_flag,
        create_time,create_by,update_time,
        update_by,cooperate_orgid,purpose,
        goal_detail,state
    </sql>

    <resultMap id="BaseResultMapVo" type="com.ruoyi.system.domain.vo.project.ProjectInfoVo" extends="BaseResultMap">
        <association property="currentYearRelation" javaType="com.ruoyi.system.domain.project.ProjectYearRelation">
            <id property="id" column="y_id" jdbcType="VARCHAR"/>
            <result property="year" column="y_year" jdbcType="INTEGER"/>
            <result property="projId" column="y_proj_id" jdbcType="VARCHAR"/>
            <result property="projType" column="proj_type" jdbcType="INTEGER"/>
            <result property="seqNo" column="seq_no" jdbcType="INTEGER"/>
            <result property="updatedTime" column="updated_time" jdbcType="DATE"/>
            <result property="isRecycle" column="is_recycle" jdbcType="VARCHAR"/>
            <result property="isClose" column="is_close" jdbcType="VARCHAR"/>
            <result property="impProj" column="imp_proj" jdbcType="VARCHAR"/>
            <result property="impCode" column="imp_code" jdbcType="INTEGER"/>
            <result property="impName" column="imp_name" jdbcType="VARCHAR"/>
        </association>
        <association property="fundPlan" javaType="com.ruoyi.system.domain.project.ProjectPlan">
            <id property="id" column="p_id" jdbcType="VARCHAR"/>
            <result property="projId" column="p_proj_id" jdbcType="VARCHAR"/>
            <result property="year" column="p_year" jdbcType="INTEGER"/>
            <result property="planState" column="p_plan_state" jdbcType="INTEGER"/>
            <result property="planType" column="p_plan_type" jdbcType="VARCHAR"/>
            <result property="declareAmount" column="p_declare_amount" jdbcType="DECIMAL"/>
            <result property="declareUserid" column="p_declare_userid" jdbcType="VARCHAR"/>
            <result property="declareTime" column="p_declare_time" jdbcType="DATE"/>
            <result property="earlyAmount" column="p_early_amount" jdbcType="DECIMAL"/>
            <result property="formalAmount" column="p_formal_amount" jdbcType="DECIMAL"/>
            <result property="adjustAmount" column="p_adjust_amount" jdbcType="DECIMAL"/>
            <result property="describe" column="p_describe" jdbcType="VARCHAR"/>
            <result property="basketId" column="p_basket_id" jdbcType="INTEGER"/>
            <result property="basketName" column="p_basket_name" jdbcType="VARCHAR"/>
            <result property="applyAdjust" column="p_apply_adjust" jdbcType="DECIMAL"/>
            <result property="applyReason" column="p_apply_reason" jdbcType="VARCHAR"/>
            <result property="applyState" column="p_apply_state" jdbcType="INTEGER"/>
            <result property="applyUserid" column="p_apply_userid" jdbcType="VARCHAR"/>
            <result property="applyTime" column="p_apply_time" jdbcType="DATE"/>
            <result property="applyRefused" column="p_apply_refused" jdbcType="VARCHAR"/>
            <result property="planDetail" column="p_plan_detail" jdbcType="VARCHAR"/>
        </association>
        <association property="projectSn" javaType="com.ruoyi.system.domain.project.ProjectSn">
            <id property="id" column="s_id" jdbcType="VARCHAR"/>
            <result property="projId" column="s_proj_id" jdbcType="VARCHAR"/>
            <result property="tempYear" column="temp_year" jdbcType="INTEGER"/>
            <result property="tempSeqid" column="temp_seqid" jdbcType="INTEGER"/>
            <result property="tempSn" column="temp_sn" jdbcType="VARCHAR"/>
            <result property="tempTime" column="temp_time" jdbcType="DATE"/>
            <result property="formalYear" column="formal_year" jdbcType="INTEGER"/>
            <result property="formalSeqid" column="formal_seqid" jdbcType="INTEGER"/>
            <result property="formalSn" column="formal_sn" jdbcType="VARCHAR"/>
            <result property="formalTime" column="formal_time" jdbcType="DATE"/>
        </association>
    </resultMap>
    <sql id="Manage_Info_List">
        a.id y_id,a.year y_year,a.proj_id y_proj_id,
        a.proj_type,a.seq_no,a.updated_time,
        a.is_recycle,a.is_close,a.imp_proj,
        a.imp_code,a.imp_name,
        b.id,b.seqid,b.name,
        b.formal_name,b.apply_orgid,b.apply_orgname,b.apply_time,
        b.submit_orgid,b.submit_orgname,b.submit_time,b.use_orgname,
        b.assess_orgid,b.assess_orgname,b.type_id,b.type_name,
        b.nature_code,b.nature_name,b.leader,
        b.leader_tel,b.handler,b.handler_tel,
        b.est_amount,b.lib_amount,b.check_amount,
        b.contract_amount,b.lib_state,b.lib_reason,
        b.check_state,b.check_reason,b.necessity,
        b.basis,b.main_cnt,b.action_plan,
        b.build_begin,b.build_end,b.fund_begin,
        b.fund_end,b.is_szhyq,b.is_jxhgl,
        b.is_zfgm,b.goal,b.basket_code,
        b.basket_name,b.base_projid,b.del_flag,
        b.create_time,b.create_by,b.update_time,
        b.update_by,b.cooperate_orgid,b.purpose,
        b.goal_detail,b.state,
        c.id p_id,c.proj_id p_proj_id,c.year p_year,
        c.plan_state p_plan_state,c.plan_type p_plan_type,c.declare_amount p_declare_amount,
        c.declare_userid p_declare_userid,c.declare_time p_declare_time,c.early_amount p_early_amount,
        c.formal_amount p_formal_amount,c.adjust_amount p_adjust_amount,c.describe p_describe,
        c.basket_id p_basket_id,c.basket_name p_basket_name,c.apply_adjust p_apply_adjust,
        c.apply_reason p_apply_reason,c.apply_state p_apply_state,c.apply_userid p_apply_userid,
        c.apply_time p_apply_time,c.apply_refused p_apply_refused,c.plan_detail p_plan_detail,
        d.id s_id,d.proj_id s_proj_id,d.temp_year,
        d.temp_seqid,d.temp_sn,d.temp_time,
        d.formal_year,d.formal_seqid,d.formal_sn,
        d.formal_time
    </sql>
    <select id="getProjectManageList" resultMap="BaseResultMapVo">
        select
            <include refid="Manage_Info_List"/>
        from project_year_relation a
        left join project_info b on a.proj_id = b.id
        left join (select * from project_plan where year = #{year}) c on b.id = c.proj_id
        left join project_sn d on b.id = d.proj_id
        where a.year = #{year} and a.is_recycle = '0' and a.is_close = '0' and b.del_flag = '0' and b.state = 1
        <if test="assessOrgid != null and assessOrgid != ''">
            and b.assess_orgid = #{assessOrgid}
        </if>
        <if test="typeId != null and typeId != ''">
            and b.type_id = #{typeId}
        </if>
        <if test="natureCode != null and natureCode != ''">
            and b.nature_code = #{natureCode}
        </if>
        <if test="isSzhyq != null and isSzhyq != ''">
            and b.is_szhyq = #{isSzhyq}
        </if>
        <if test="name != null and name != ''">
            and b.name like CONCAT('%',#{name},'%')
        </if>
        <if test='hasEarlyAmount == "1"'>
            and c.plan_state = 9
        </if>
        <if test='hasAdjustAmount == "1"'>
            and c.adjust_amount is not null
        </if>
        <if test="purposeIds != null and purposeIds.size() > 0">
            <foreach collection="purposeIds" item="item">
                and b.purpose like CONCAT('%"',#{item},'"%')
            </foreach>
        </if>
    </select>

    <resultMap id="reproducibleProjectMap" type="com.ruoyi.system.domain.vo.project.ProjectInfoCopyVo">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="handler" column="handler"/>
        <result property="estAmount" column="est_amount"/>
        <result property="beginDate" column="begin_date"/>
        <result property="endDate" column="end_date"/>
    </resultMap>
    <select id="getReproducibleProject" resultMap="reproducibleProjectMap">
        select a.id,a.name,a.handler,a.est_amount,LEAST(a.build_begin ,a.fund_begin) as begin_date,GREAT(a.build_end ,a.fund_end) as end_date from project_info a
        left join project_year_relation b on a.id = b.proj_id
        where a.basket_code is not null and a.del_flag = '0' and b.is_recycle = '0' and b.is_close = '0'
        <if test="natureCode != null and natureCode != ''">
            and a.nature_code = #{natureCode}
        </if>
        <if test="year != null and year != ''">
            and b.year = #{year}
        </if>
    </select>

    <resultMap id="projectInfoOfPlanMap" type="com.ruoyi.system.domain.vo.project.ProjectInfoOfPlanVo">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="applyOrgid" column="apply_orgid"/>
        <result property="applyOrgname" column="apply_orgname"/>
        <result property="assessOrgid" column="assess_orgid"/>
        <result property="assessOrgname" column="assess_orgname"/>
        <result property="cooperateOrgid" column="cooperate_orgid"/>
        <result property="estAmount" column="est_amount"/>
        <result property="beginDate" column="begin_date"/>
        <result property="endDate" column="end_date"/>
    </resultMap>
    <select id="getProjectInfoOfPlan" resultMap="projectInfoOfPlanMap">
        select a.id,a.name,a.assess_orgid,a.assess_orgname,a.apply_orgname,a.cooperate_orgid,a.est_amount,LEAST(a.build_begin ,a.fund_begin) as begin_date,GREAT(a.build_end ,a.fund_end) as end_date from project_info a
        left join project_year_relation b on a.id = b.proj_id
        where a.basket_code is not null and a.del_flag = '0' and b.is_recycle = '0' and b.is_close = '0' and a.state = 1
        <if test="year != null and year != ''">
            and b.year = #{year}
        </if>
        <if test="name != null and name != ''">
            and a.name like CONCAT('%',#{name},'%')
        </if>
        <if test="assessOrgid != null and assessOrgid != ''">
            and a.assess_orgid = #{assessOrgid}
        </if>
        <if test="projectUnits != null and projectUnits != ''">
            and  (a.apply_orgid = #{projectUnits} OR a.submit_orgid = #{projectUnits} OR (JSON_CONTAINS(JSON_EXTRACT(a.cooperate_orgid, '$.id'),TO_CHAR(#{projectUnits}), '$') = 1))
        </if>
        <if test="typeId != null and typeId != ''">
            and a.type_id = #{typeId}
        </if>
        <if test="purposeIds != null and purposeIds.size() > 0">
            <foreach collection="purposeIds" item="item">
                and a.purpose like CONCAT('%"',#{item},'"%')
            </foreach>
        </if>
    </select>

    <resultMap id="ProjectDetailForUsagePlanMapVo" type="com.ruoyi.system.domain.vo.project.ProjectInfoVo" extends="BaseResultMap">
        <association property="currentYearRelation" javaType="com.ruoyi.system.domain.project.ProjectYearRelation">
            <id property="id" column="y_id" jdbcType="VARCHAR"/>
            <result property="year" column="y_year" jdbcType="INTEGER"/>
            <result property="projId" column="y_proj_id" jdbcType="VARCHAR"/>
            <result property="projType" column="proj_type" jdbcType="INTEGER"/>
            <result property="seqNo" column="seq_no" jdbcType="INTEGER"/>
            <result property="updatedTime" column="updated_time" jdbcType="DATE"/>
            <result property="isRecycle" column="is_recycle" jdbcType="VARCHAR"/>
            <result property="isClose" column="is_close" jdbcType="VARCHAR"/>
            <result property="impProj" column="imp_proj" jdbcType="VARCHAR"/>
            <result property="impCode" column="imp_code" jdbcType="INTEGER"/>
            <result property="impName" column="imp_name" jdbcType="VARCHAR"/>
        </association>
        <association property="fundPlan" javaType="com.ruoyi.system.domain.project.ProjectPlan">
            <id property="id" column="p_id" jdbcType="VARCHAR"/>
            <result property="projId" column="p_proj_id" jdbcType="VARCHAR"/>
            <result property="year" column="p_year" jdbcType="INTEGER"/>
            <result property="planState" column="p_plan_state" jdbcType="INTEGER"/>
            <result property="planType" column="p_plan_type" jdbcType="VARCHAR"/>
            <result property="declareAmount" column="p_declare_amount" jdbcType="DECIMAL"/>
            <result property="declareUserid" column="p_declare_userid" jdbcType="VARCHAR"/>
            <result property="declareTime" column="p_declare_time" jdbcType="DATE"/>
            <result property="earlyAmount" column="p_early_amount" jdbcType="DECIMAL"/>
            <result property="formalAmount" column="p_formal_amount" jdbcType="DECIMAL"/>
            <result property="adjustAmount" column="p_adjust_amount" jdbcType="DECIMAL"/>
            <result property="describe" column="p_describe" jdbcType="VARCHAR"/>
            <result property="basketId" column="p_basket_id" jdbcType="INTEGER"/>
            <result property="basketName" column="p_basket_name" jdbcType="VARCHAR"/>
            <result property="applyAdjust" column="p_apply_adjust" jdbcType="DECIMAL"/>
            <result property="applyReason" column="p_apply_reason" jdbcType="VARCHAR"/>
            <result property="applyState" column="p_apply_state" jdbcType="INTEGER"/>
            <result property="applyUserid" column="p_apply_userid" jdbcType="VARCHAR"/>
            <result property="applyTime" column="p_apply_time" jdbcType="DATE"/>
            <result property="applyRefused" column="p_apply_refused" jdbcType="VARCHAR"/>
            <result property="planDetail" column="p_plan_detail" jdbcType="VARCHAR"/>
        </association>
        <association property="prevFundPlan" javaType="com.ruoyi.system.domain.project.ProjectPlan">
            <id property="id" column="pv_id" jdbcType="VARCHAR"/>
            <result property="projId" column="pv_proj_id" jdbcType="VARCHAR"/>
            <result property="year" column="pv_year" jdbcType="INTEGER"/>
            <result property="planState" column="pv_plan_state" jdbcType="INTEGER"/>
            <result property="planType" column="pv_plan_type" jdbcType="VARCHAR"/>
            <result property="declareAmount" column="pv_declare_amount" jdbcType="DECIMAL"/>
            <result property="declareUserid" column="pv_declare_userid" jdbcType="VARCHAR"/>
            <result property="declareTime" column="pv_declare_time" jdbcType="DATE"/>
            <result property="earlyAmount" column="pv_early_amount" jdbcType="DECIMAL"/>
            <result property="formalAmount" column="pv_formal_amount" jdbcType="DECIMAL"/>
            <result property="adjustAmount" column="pv_adjust_amount" jdbcType="DECIMAL"/>
            <result property="describe" column="pv_describe" jdbcType="VARCHAR"/>
            <result property="basketId" column="pv_basket_id" jdbcType="INTEGER"/>
            <result property="basketName" column="pv_basket_name" jdbcType="VARCHAR"/>
            <result property="applyAdjust" column="pv_apply_adjust" jdbcType="DECIMAL"/>
            <result property="applyReason" column="pv_apply_reason" jdbcType="VARCHAR"/>
            <result property="applyState" column="pv_apply_state" jdbcType="INTEGER"/>
            <result property="applyUserid" column="pv_apply_userid" jdbcType="VARCHAR"/>
            <result property="applyTime" column="pv_apply_time" jdbcType="DATE"/>
            <result property="applyRefused" column="pv_apply_refused" jdbcType="VARCHAR"/>
            <result property="planDetail" column="pv_plan_detail" jdbcType="VARCHAR"/>
        </association>
        <association property="projectSn" javaType="com.ruoyi.system.domain.project.ProjectSn">
            <id property="id" column="s_id" jdbcType="VARCHAR"/>
            <result property="projId" column="s_proj_id" jdbcType="VARCHAR"/>
            <result property="tempYear" column="temp_year" jdbcType="INTEGER"/>
            <result property="tempSeqid" column="temp_seqid" jdbcType="INTEGER"/>
            <result property="tempSn" column="temp_sn" jdbcType="VARCHAR"/>
            <result property="tempTime" column="temp_time" jdbcType="DATE"/>
            <result property="formalYear" column="formal_year" jdbcType="INTEGER"/>
            <result property="formalSeqid" column="formal_seqid" jdbcType="INTEGER"/>
            <result property="formalSn" column="formal_sn" jdbcType="VARCHAR"/>
            <result property="formalTime" column="formal_time" jdbcType="DATE"/>
        </association>
    </resultMap>

    <select id="getProjectDetailForUsagePlan" resultMap="ProjectDetailForUsagePlanMapVo">
        select
            a.id y_id,a.year y_year,a.proj_id y_proj_id,
            a.proj_type,a.seq_no,a.updated_time,
            a.is_recycle,a.is_close,a.imp_proj,
            a.imp_code,a.imp_name,
            b.id,b.seqid,b.name,
            b.formal_name,b.apply_orgid,b.apply_orgname,b.apply_time,
            b.submit_orgid,b.submit_orgname,b.submit_time,b.use_orgname,
            b.assess_orgid,b.assess_orgname,b.type_id,b.type_name,
            b.nature_code,b.nature_name,b.leader,
            b.leader_tel,b.handler,b.handler_tel,
            b.est_amount,b.lib_amount,b.check_amount,
            b.contract_amount,b.lib_state,b.lib_reason,
            b.check_state,b.check_reason,b.necessity,
            b.basis,b.main_cnt,b.action_plan,
            b.build_begin,b.build_end,b.fund_begin,
            b.fund_end,b.is_szhyq,b.is_jxhgl,
            b.is_zfgm,b.goal,b.basket_code,
            b.basket_name,b.base_projid,b.del_flag,
            b.create_time,b.create_by,b.update_time,
            b.update_by,b.cooperate_orgid,b.purpose,
            b.goal_detail,b.state,
            c.id p_id,c.proj_id p_proj_id,c.year p_year,
            c.plan_state p_plan_state,c.plan_type p_plan_type,c.declare_amount p_declare_amount,
            c.declare_userid p_declare_userid,c.declare_time p_declare_time,c.early_amount p_early_amount,
            c.formal_amount p_formal_amount,c.adjust_amount p_adjust_amount,c.describe p_describe,
            c.basket_id p_basket_id,c.basket_name p_basket_name,c.apply_adjust p_apply_adjust,
            c.apply_reason p_apply_reason,c.apply_state p_apply_state,c.apply_userid p_apply_userid,
            c.apply_time p_apply_time,c.apply_refused p_apply_refused,c.plan_detail p_plan_detail,
            d.id pv_id,d.proj_id pv_proj_id,d.year pv_year,
            d.plan_state pv_plan_state,d.plan_type pv_plan_type,d.declare_amount pv_declare_amount,
            d.declare_userid pv_declare_userid,d.declare_time pv_declare_time,d.early_amount pv_early_amount,
            d.formal_amount pv_formal_amount,d.adjust_amount pv_adjust_amount,d.describe pv_describe,
            d.basket_id pv_basket_id,d.basket_name pv_basket_name,d.apply_adjust pv_apply_adjust,
            d.apply_reason pv_apply_reason,d.apply_state pv_apply_state,d.apply_userid pv_apply_userid,
            d.apply_time pv_apply_time,d.apply_refused pv_apply_refused,d.plan_detail pv_plan_detail,
            e.id s_id,e.proj_id s_proj_id,e.temp_year,
            e.temp_seqid,e.temp_sn,e.temp_time,
            e.formal_year,e.formal_seqid,e.formal_sn,
            e.formal_time
        from project_info b
        left join (select * from project_year_relation where year = #{year}) a on a.proj_id = b.id
        left join (select * from project_plan where year = #{year}) c on b.id = c.proj_id
        left join (select * from project_plan where year = #{year} - 1 ) d on b.id = d.proj_id
        left join project_sn e on b.id = e.proj_id
        <where>
            <if test="pIds != null and pIds.size() > 0">
                and b.id in
                <foreach item="id" collection="pIds" separator="," close=")" open="(" index="">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
