<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.project.ProjectImportantMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.project.ProjectImportant">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="year" column="year" jdbcType="INTEGER"/>
            <result property="projId" column="proj_id" jdbcType="VARCHAR"/>
            <result property="projCode" column="proj_code" jdbcType="VARCHAR"/>
            <result property="stepNo" column="step_no" jdbcType="INTEGER"/>
            <result property="stepName" column="step_name" jdbcType="VARCHAR"/>
            <result property="seqNo" column="seq_no" jdbcType="INTEGER"/>
            <result property="yearGoal" column="year_goal" jdbcType="VARCHAR"/>
            <result property="progressIssue" column="progress_issue" jdbcType="VARCHAR"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,year,proj_id,
        proj_code,step_no,step_name,
        seq_no,year_goal,progress_issue,
        update_by,update_time,month_plan,
        month_action
    </sql>

    <resultMap id="reportProjectImportant" type="com.ruoyi.system.domain.vo.project.ProjectImpVo">
        <result property="year" column="year"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="projAmount" column="proj_amount"/>
        <result property="projCount" column="proj_count"/>
        <result property="yearPlan" column="year_plan"/>
        <result property="yearExecuted" column="year_executed"/>
    </resultMap>

    <select id="getReprortProjectImportant" resultMap="reportProjectImportant">
        select
        a.year,
        c.dept_id,c.dept_name,sum(b.contract_amount) as proj_amount,count(1) as proj_count
        from
        project_year_relation a,project_info b,sys_dept c
        where  a.proj_id=b.id  and b.assess_orgid=c.dept_id  and a.imp_proj='1'
        <if test="year != null and year != ''">
            and a.year = #{year}
        </if>
          <if test="deptId != null and deptId != ''">
            and b.assess_orgid = #{deptId}
        </if>
        group by  a.year,c.dept_id,c.dept_name
    </select>

    <resultMap id="reportProjectImportantStep" type="com.ruoyi.system.domain.vo.project.ProjectImpStepVo">
        <result property="year" column="year"/>
        <result property="stepNo" column="step_no"/>
        <result property="stepName" column="step_name"/>
        <result property="projAmount" column="proj_amount"/>
        <result property="projCount" column="proj_count"/>
    </resultMap>

    <select id="getReportProjectImportantStep" resultMap="reportProjectImportantStep">
        select
        a.year,c.step_no,c.step_name,sum(b.contract_amount) as proj_amount
        from
        project_year_relation a,project_info b,project_important c
        where  a.proj_id=b.id  and a.proj_id=c.proj_id and a.year=c.year  and a.imp_proj='1'
        <if test="year != null and year != ''">
            and a.year = #{year}
        </if>
        <if test="deptId != null and deptId != ''">
            and b.assess_orgid = #{deptId}
        </if>
        <if test="stepNo != null and stepNo != ''">
            and c.step_no = #{stepNo}
        </if>
        group by  a.year,c.step_no,c.step_name order by c.step_no
    </select>

    <select id="getReportProjectImportantStep1" resultMap="reportProjectImportantStep">
        select
        a.year,c.step_no,c.step_name,sum(b.contract_amount) as proj_amount,count(1) as proj_count
        from
        project_year_relation a,project_info b,project_important c
        where  a.proj_id=b.id  and a.proj_id=c.proj_id(+) and a.year=c.year(+)  and a.imp_proj='1'
        <if test="year != null and year != ''">
            and a.year = #{year}
        </if>
        <if test="deptId != null and deptId != ''">
            and b.assess_orgid = #{deptId}
        </if>
        <if test="stepNo != null and stepNo != ''">
            and c.step_no = #{stepNo}
        </if>
        group by  a.year,c.step_no,c.step_name  order by c.step_no
    </select>

    <resultMap id="reportProjectImportantOut" type="com.ruoyi.system.domain.vo.project.ProjectImpOutVo">
        <result property="relId" column="rel_id"/>
        <result property="year" column="year"/>
        <result property="projName" column="proj_name"/>
        <result property="projId" column="proj_id"/>
        <result property="projAmount" column="proj_amount"/>
        <result property="deptId" column="dept_id"/>
        <result property="planBegin" column="plan_begin"/>
        <result property="projEnd" column="proj_end"/>
    </resultMap>

    <select id="getReportProjectImportantOut" resultMap="reportProjectImportantOut">
        select
        a.id as rel_id,a.year,a.proj_id,b.name as proj_name,c.dept_id,b.contract_amount as proj_amount,b.build_begin as plan_begin,b.build_end as proj_end
        from
        project_year_relation a,project_info b,sys_dept c
        where  a.proj_id=b.id  and b.assess_orgid=c.dept_id and a.imp_proj='1'
        and NOT EXISTS (SELECT 1 FROM project_important pr
        WHERE pr.proj_id = a.proj_id AND pr.year = a.year)
        <if test="year != null and year != ''">
            and a.year = #{year}
        </if>
        <if test="deptId != null and deptId != ''">
            and b.assess_orgid = #{deptId}
        </if>
    </select>
   <!-- 项目整和计划管理输入视图 -->
    <resultMap id="reportProjectImportantInput" type="com.ruoyi.system.domain.vo.project.ProjectImpInputVo">
        <result property="relId" column="rel_id"/>
        <result property="id" column="id"/>
        <result property="year" column="year"/>
        <result property="projName" column="proj_name"/>
        <result property="projId" column="proj_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="stepNo" column="step_no"/>
        <result property="projCode" column="proj_code"/>
        <result property="yearGoal" column="year_goal"/>
        <result property="monthPlan" column="month_plan"/>
    </resultMap>

    <select id="getReportProjectImportantInput" resultMap="reportProjectImportantInput">
        select
        a.id as rel_id,d.id,a.year,a.proj_id,b.name as proj_name,c.dept_id,d.step_no,d.proj_code,d.year_goal,d.month_plan
        from
        project_year_relation a,project_info b,sys_dept c,project_important d
        where  a.proj_id=b.id  and b.assess_orgid=c.dept_id and a.year=d.year(+) and a.proj_id=d.proj_id(+) and a.imp_proj='1'
        <if test="year != null and year != ''">
            and a.year = #{year}
        </if>
        <if test="deptId != null and deptId != ''">
            and b.assess_orgid = #{deptId}
        </if>
        <if test="stepNo != null and stepNo != ''">
            and d.step_no = #{stepNo}
        </if>
       order by  d.step_no
    </select>


    <!-- 定义查询年代语句 -->
    <select id="getDistinctYears" resultType="java.lang.Integer">
        SELECT DISTINCT a.year
        FROM project_year_relation a,project_info b
        WHERE a.proj_id = b.id AND a.imp_proj = '1'  order by a.year
    </select>
</mapper>
