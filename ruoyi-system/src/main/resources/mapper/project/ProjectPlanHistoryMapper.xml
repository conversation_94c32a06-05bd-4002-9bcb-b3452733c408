<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.project.ProjectPlanHistoryMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.project.ProjectPlanHistory">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="projId" column="proj_id" jdbcType="VARCHAR"/>
            <result property="planId" column="plan_id" jdbcType="VARCHAR"/>
            <result property="updateUserid" column="update_userid" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="DATE"/>
            <result property="updateReason" column="update_reason" jdbcType="VARCHAR"/>
            <result property="dataJson" column="data_json" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,type,proj_id,
        plan_id,update_userid,update_time,
        update_reason,data_json
    </sql>
</mapper>
