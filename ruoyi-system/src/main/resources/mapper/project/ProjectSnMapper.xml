<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.project.ProjectSnMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.project.ProjectSn">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="projId" column="proj_id" jdbcType="VARCHAR"/>
            <result property="tempYear" column="temp_year" jdbcType="INTEGER"/>
            <result property="tempSeqid" column="temp_seqid" jdbcType="INTEGER"/>
            <result property="tempSn" column="temp_sn" jdbcType="VARCHAR"/>
            <result property="tempTime" column="temp_time" jdbcType="DATE"/>
            <result property="formalYear" column="formal_year" jdbcType="INTEGER"/>
            <result property="formalSeqid" column="formal_seqid" jdbcType="INTEGER"/>
            <result property="formalSn" column="formal_sn" jdbcType="VARCHAR"/>
            <result property="formalTime" column="formal_time" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,proj_id,temp_year,
        temp_seqid,temp_sn,temp_time,
        formal_year,formal_seqid,formal_sn,
        formal_time
    </sql>
</mapper>
