<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.project.ProjectPlanMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.project.ProjectPlan">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="projId" column="proj_id" jdbcType="VARCHAR"/>
        <result property="year" column="year" jdbcType="INTEGER"/>
        <result property="planState" column="plan_state" jdbcType="INTEGER"/>
        <result property="planType" column="plan_type" jdbcType="VARCHAR"/>
        <result property="declareAmount" column="declare_amount" jdbcType="DECIMAL"/>
        <result property="declareUserid" column="declare_userid" jdbcType="VARCHAR"/>
        <result property="declareTime" column="declare_time" jdbcType="DATE"/>
        <result property="earlyAmount" column="early_amount" jdbcType="DECIMAL"/>
        <result property="formalAmount" column="formal_amount" jdbcType="DECIMAL"/>
        <result property="adjustAmount" column="adjust_amount" jdbcType="DECIMAL"/>
        <result property="describe" column="describe" jdbcType="VARCHAR"/>
        <result property="basketId" column="basket_id" jdbcType="INTEGER"/>
        <result property="basketName" column="basket_name" jdbcType="VARCHAR"/>
        <result property="applyAdjust" column="apply_adjust" jdbcType="DECIMAL"/>
        <result property="applyReason" column="apply_reason" jdbcType="VARCHAR"/>
        <result property="applyState" column="apply_state" jdbcType="INTEGER"/>
        <result property="applyUserid" column="apply_userid" jdbcType="VARCHAR"/>
        <result property="applyTime" column="apply_time" jdbcType="DATE"/>
        <result property="applyRefused" column="apply_refused" jdbcType="VARCHAR"/>
        <result property="planDetail" column="plan_detail" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,proj_id,year,
        plan_state,plan_type,declare_amount,
        declare_userid,declare_time,early_amount,
        formal_amount,adjust_amount,describe,
        basket_id,basket_name,apply_adjust,
        apply_reason,apply_state,apply_userid,
        apply_time,apply_refused,plan_detail
    </sql>
</mapper>
