<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.project.ProjectPlanOperateMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.project.ProjectPlanOperate">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="projId" column="proj_id" jdbcType="VARCHAR"/>
            <result property="planId" column="plan_id" jdbcType="VARCHAR"/>
            <result property="operateUserid" column="operate_userid" jdbcType="VARCHAR"/>
            <result property="operateTime" column="operate_time" jdbcType="DATE"/>
            <result property="operateType" column="operate_type" jdbcType="VARCHAR"/>
            <result property="operateOpinion" column="operate_opinion" jdbcType="VARCHAR"/>
            <result property="operateReason" column="operate_reason" jdbcType="VARCHAR"/>
            <result property="todoId" column="todo_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,type,proj_id,
        plan_id,operate_userid,operate_time,
        operate_type,operate_opinion,operate_reason,
        todo_id
    </sql>
</mapper>
