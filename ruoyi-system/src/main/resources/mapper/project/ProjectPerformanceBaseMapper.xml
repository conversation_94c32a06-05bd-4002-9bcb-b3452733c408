<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.project.ProjectPerformanceBaseMapper">

    <resultMap type="com.ruoyi.system.domain.project.ProjectPerformanceBase" id="ProjectPerformanceBaseResult">
        <result property="id" column="id"/>
        <result property="indexLevel1" column="index_level1"/>
        <result property="indexLevel2" column="index_level2"/>
        <result property="indexLevel3" column="index_level3"/>
        <result property="natureName" column="nature_name"/>
        <result property="compareArrow" column="compare_arrow"/>
        <result property="measureUnit" column="measure_unit"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="indexSort" column="index_sort"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,index_level1,index_level2,
        index_level3,nature_name,compare_arrow,
        measure_unit,remark,del_flag,
        index_sort
    </sql>
</mapper>
