<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.project.UsagePlanMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.project.UsagePlan">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="year" column="year" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="unit" column="unit" jdbcType="VARCHAR"/>
        <result property="isSnap" column="is_snap" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="DATE"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="DATE"/>
        <result property="snapName" column="SNAP_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,year,title,
        unit,is_snap,del_flag,
        create_by,create_time,update_by,
        update_time,SNAP_NAME,COLUMNS
    </sql>
</mapper>
