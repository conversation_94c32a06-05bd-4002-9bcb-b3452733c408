<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.project.UsagePlanItemMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.project.UsagePlanItem">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="fundId" column="fund_id" jdbcType="VARCHAR"/>
        <result property="itemType" column="item_type" jdbcType="INTEGER"/>
        <result property="sumMode" column="sum_mode" jdbcType="INTEGER"/>
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="seqNo" column="seq_no" jdbcType="INTEGER"/>
        <result property="projId" column="proj_id" jdbcType="VARCHAR"/>
        <result property="projName" column="proj_name" jdbcType="VARCHAR"/>
        <result property="sbOrgid" column="sb_orgid" jdbcType="VARCHAR"/>
        <result property="sbOrgname" column="sb_orgname" jdbcType="VARCHAR"/>
        <result property="assessOrgid" column="assess_orgid" jdbcType="VARCHAR"/>
        <result property="assessOrgname" column="assess_orgname" jdbcType="VARCHAR"/>
        <result property="isImp" column="is_imp" jdbcType="VARCHAR"/>
        <result property="impName" column="imp_name" jdbcType="VARCHAR"/>
        <result property="basketId" column="basket_id" jdbcType="INTEGER"/>
        <result property="basketName" column="basket_name" jdbcType="VARCHAR"/>
        <result property="natureName" column="nature_name" jdbcType="VARCHAR"/>
        <result property="estAmount" column="est_amount" jdbcType="DECIMAL"/>
        <result property="libAmount" column="lib_amount" jdbcType="DECIMAL"/>
        <result property="checkAmount" column="check_amount" jdbcType="DECIMAL"/>
        <result property="contractAmount" column="contract_amount" jdbcType="DECIMAL"/>
        <result property="prevAmount" column="prev_amount" jdbcType="DECIMAL"/>
        <result property="prevAdjust" column="prev_adjust" jdbcType="DECIMAL"/>
        <result property="prevPayed" column="prev_payed" jdbcType="DECIMAL"/>
        <result property="currAmount" column="curr_amount" jdbcType="DECIMAL"/>
        <result property="currEarly" column="curr_early" jdbcType="DECIMAL"/>
        <result property="currFormal" column="curr_formal" jdbcType="DECIMAL"/>
        <result property="currAdjust" column="curr_adjust" jdbcType="DECIMAL"/>
        <result property="currPayed" column="curr_payed" jdbcType="DECIMAL"/>
        <result property="beginDate" column="begin_date" jdbcType="VARCHAR"/>
        <result property="endDate" column="end_date" jdbcType="VARCHAR"/>
        <result property="remark1" column="remark1" jdbcType="VARCHAR"/>
        <result property="remark2" column="remark2" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,fund_id,item_type,
        sum_mode,parent_id,seq_no,
        proj_id,proj_name,sb_orgid,
        sb_orgname,assess_orgid,assess_orgname,
        is_imp,imp_name,basket_id,
        basket_name,nature_name,est_amount,
        lib_amount,check_amount,contract_amount,
        prev_amount,prev_adjust,prev_payed,
        curr_amount,curr_early,curr_formal,
        curr_adjust,curr_payed,begin_date,
        end_date,remark1,remark2,
        create_by,create_time,update_by,
        update_time
    </sql>
</mapper>
