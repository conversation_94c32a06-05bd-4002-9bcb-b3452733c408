package com.ruoyi.system.service.impl.project;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.domain.project.ProjectPerformanceBase;
import com.ruoyi.system.mapper.project.ProjectPerformanceBaseMapper;
import com.ruoyi.system.service.project.ProjectPerformanceBaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @description 针对表【project_performance_base(项目绩效指标库)】的数据库操作Service实现
 * @date 2025-06-09
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ProjectPerformanceBaseServiceImpl extends ServiceImpl<ProjectPerformanceBaseMapper, ProjectPerformanceBase> implements ProjectPerformanceBaseService {

}
