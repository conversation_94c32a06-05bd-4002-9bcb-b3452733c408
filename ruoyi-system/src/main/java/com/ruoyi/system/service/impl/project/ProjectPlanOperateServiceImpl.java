package com.ruoyi.system.service.impl.project;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.CommonConstant;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.event.OperateEvent;
import com.ruoyi.system.domain.project.ProjectPlan;
import com.ruoyi.system.domain.project.ProjectPlanOperate;
import com.ruoyi.system.service.project.ProjectPlanOperateService;
import com.ruoyi.system.mapper.project.ProjectPlanOperateMapper;
import com.ruoyi.system.service.project.ProjectPlanService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 针对表【project_plan_operate(项目(计划）操作记录表)】的数据库操作Service实现
 * @date 2025-06-03 15:04:59
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy, @Autowired})
public class ProjectPlanOperateServiceImpl extends ServiceImpl<ProjectPlanOperateMapper, ProjectPlanOperate> implements ProjectPlanOperateService {

    private final ProjectPlanService projectPlanService;

    @Async
    @EventListener
    public void saveProjectPlanOperate(OperateEvent event) {

        ProjectPlanOperate operate = new ProjectPlanOperate();
        String planId = event.getPlanId();
        if (CommonConstant.ProjectOrPlan.PLAN.equals(event.type) && StringUtils.isBlank(planId)) {
            ProjectPlan projectPlan = projectPlanService.lambdaQuery()
                    .eq(ProjectPlan::getProjId, event.getProjId())
                    .eq(ProjectPlan::getYear, event.getYear())
                    .one();
            planId = projectPlan.getId();
        }
        operate.setType(event.type.name);
        operate.setProjId(event.getProjId());
        operate.setPlanId(planId);
        operate.setOperateUserid(String.valueOf(event.getCurrentOperateUser().getUserId()));
        operate.setOperateTime(new Date());
        operate.setOperateType(event.getOperateType());
        operate.setOperateOpinion(event.getOperateOpinion());
        operate.setOperateReason(event.getOperateReason());
        operate.setTodoId(event.getTodoId());
        this.save(operate);
    }

}




