package com.ruoyi.system.service.project;

import com.ruoyi.system.domain.dto.ProjectPerformanceDto;
import com.ruoyi.system.domain.project.ProjectPerformance;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @description 针对表【project_performance(项目绩效目标)】的数据库操作Service
 * @date 2025-06-03 15:20:05
 */
public interface ProjectPerformanceService extends IService<ProjectPerformance> {

    ProjectPerformanceDto getPerformanceInfo(String pid);

    Boolean saveOrUpdateInfo(ProjectPerformanceDto dto);

}
