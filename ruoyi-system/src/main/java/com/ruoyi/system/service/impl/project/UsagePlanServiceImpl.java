package com.ruoyi.system.service.impl.project;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.domain.project.UsagePlan;
import com.ruoyi.system.service.project.UsagePlanService;
import com.ruoyi.system.mapper.project.UsagePlanMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【usage_plan(用款计划表)】的数据库操作Service实现
 * @date 2025-06-12 10:12:34
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class UsagePlanServiceImpl extends ServiceImpl<UsagePlanMapper, UsagePlan> implements UsagePlanService {

}




