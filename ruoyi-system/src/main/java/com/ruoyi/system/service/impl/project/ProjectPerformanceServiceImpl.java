package com.ruoyi.system.service.impl.project;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.dto.ProjectPerformanceDto;
import com.ruoyi.system.domain.project.ProjectInfo;
import com.ruoyi.system.domain.project.ProjectPerformance;
import com.ruoyi.system.service.project.ProjectInfoService;
import com.ruoyi.system.service.project.ProjectPerformanceService;
import com.ruoyi.system.mapper.project.ProjectPerformanceMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【project_performance(项目绩效目标)】的数据库操作Service实现
 * @date 2025-06-03 15:20:05
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy, @Autowired})
public class ProjectPerformanceServiceImpl extends ServiceImpl<ProjectPerformanceMapper, ProjectPerformance> implements ProjectPerformanceService {

    private final ProjectInfoService projectInfoService;

    @Override
    public ProjectPerformanceDto getPerformanceInfo(String pid) {
        ProjectPerformanceDto dto = new ProjectPerformanceDto();
        dto.setProjId(pid);
        dto.setGoal(projectInfoService.lambdaQuery().eq(ProjectInfo::getId, pid).one().getGoal());
        dto.setPerformanceList(this.lambdaQuery().eq(ProjectPerformance::getProjId, pid).list());
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateInfo(ProjectPerformanceDto dto) {
        String pid = dto.getProjId();
        this.lambdaUpdate()
                .eq(ProjectPerformance::getProjId, pid)
                .remove();
        String goal = dto.getGoal();
        if (StringUtils.isNotBlank(goal)) {
            projectInfoService.lambdaUpdate()
                    .eq(ProjectInfo::getId, pid)
                    .set(ProjectInfo::getGoal, goal)
                    .update();
        }
        List<ProjectPerformance> performances = dto.getPerformanceList();
        if (performances != null && !performances.isEmpty()) {
            for (ProjectPerformance entity : performances) {
                entity.setProjId(pid);
                this.saveOrUpdate(entity);
            }
        }
        return true;
    }
}




