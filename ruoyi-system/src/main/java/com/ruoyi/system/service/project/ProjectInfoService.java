package com.ruoyi.system.service.project;

import com.ruoyi.system.domain.dto.AdjustPlanDto;
import com.ruoyi.system.domain.dto.ProjectSummaryDto;
import com.ruoyi.system.domain.excel.ProjectInfoMainExcel;
import com.ruoyi.system.domain.project.ProjectInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.vo.project.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【project_info(项目信息表;)】的数据库操作Service
 * @date 2025-06-03 15:28:40
 */
public interface ProjectInfoService extends IService<ProjectInfo> {


    /**
     * 获取正常项目列表
     *
     * @param vo 查询条件
     * @return 正常项目列表
     */
    List<ProjectInfoVo> getNormalList(ProjectQueryConditionVo vo);

    /**
     * 获取回收站项目列表
     *
     * @param vo 查询条件
     * @return 异常项目列表
     */
    List<ProjectInfoVo> getAbnormalList(ProjectQueryConditionVo vo);

    /**
     * 获取项目详情
     *
     * @param id 项目id
     * @return 项目详情
     */
    ProjectInfoVo getProjectDetail(String id, Integer year);

    /**
     * 根据用款计划年度，获取相关的项目信息
     *
     * @param year 年
     * @return 项目详情列表
     */
    public List<ProjectInfoVo> getProjectDetailForUsagePlan(Integer year);

    /**
     * 保存项目信息
     *
     * @param projectInfoVo 项目信息
     * @return 保存结果 临时编号和项目id
     */
    Map<String, String> saveProjectInfo(ProjectInfoVo projectInfoVo);

    /**
     * 修改项目info表信息
     *
     * @param projectInfo 项目信息
     * @return 是否
     */
    Boolean updateProjectInfo(ProjectInfo projectInfo);

    /**
     * 复制项目
     *
     * @param vo 条件参数
     * @return 是否
     */
    List<ProjectInfoCopyVo> getReproducibleProject(ProjectQueryConditionVo vo);

    /**
     * 复制项目
     *
     * @param ids 项目id集合
     * @return 是否
     */
    Boolean copyProject(String[] ids, Integer year);

    /**
     * 修改项目概述信息
     *
     * @param dto 项目概述信息
     * @return 是否
     */
    Boolean updateProjectSummary(ProjectSummaryDto dto);

    /**
     * 移动到下一年
     *
     * @param pid 项目id
     * @return 是否
     */
    Boolean moveNextYear(String pid, Integer year);

    /**
     * 项目和计划同时上报
     *
     * @param pid 项目id
     * @param year 年
     * @return 是否
     */
    Boolean reportProject(String pid,  Integer year);

    /**
     * 仅上报项目
     *
     * @param pid 项目id
     * @return 是否
     */
    Boolean onlyReportProject(String pid);

    /**
     * 项目和计划同时撤回
     *
     * @param pid 项目id
     * @return 是否
     */
    Boolean withdrawProject(String pid,  Integer year);

    /**
     * 仅项目撤回
     * @param pid 项目id
     * @return 是否
     */
    Boolean onlyWithdrawProject(String pid);

    /**
     * 移动到回收站
     *
     * @param pid 项目id
     * @return 是否
     */
    Boolean putTrash(String pid, Integer year);

    /**
     * 恢复项目
     *
     * @param pid 项目id
     * @return 是否
     */
    Boolean recoveryProject(String pid, Integer year);

    /**
     * 删除项目年度信息
     *
     * @param pid 项目id
     * @param year 年
     * @return 是否
     */
    Boolean removeYearRelationByPid(String pid, Integer year);

    /**
     * 删除项目信息
     *
     * @param pid 项目id
     * @return 是否
     */
    Boolean removeProjectById(String pid);


    /**
     * 项目退回
     *
     * @param pid 项目id
     * @return 是否
     */
    Boolean backProject(String pid);


    /**
     * 调整计划
     *
     * @param dto 调整信息
     * @return 是否
     */
    Boolean adjustPlan(AdjustPlanDto dto);

    /**
     * 撤回计划调整
     *
     * @param pid 项目id
     * @param year 年
     * @return 是否
     */
    Boolean withdrawAdjustPlan(String pid, Integer year);

    /**
     * 用款计划表项目查询
     *
     * @param vo 条件参数
     * @return 是否
     */
    List<ProjectInfoOfPlanVo> getProjectInfoOfPlan(ProjectQueryOfUsagePlanConditionVo vo);

    /**
     * 获取导出excel的项目信息列表
     *
     * @param vo 列表参数
     * @return excel中的项目信息列表
     */
    ProjectInfoMainExcel getProjectInfoMainExcel(ProjectQueryConditionVo vo);
}
