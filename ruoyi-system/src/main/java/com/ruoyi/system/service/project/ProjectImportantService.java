package com.ruoyi.system.service.project;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.system.domain.project.ProjectImportant;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.vo.project.*;

import java.util.List;

/**
* <AUTHOR> @description 针对表【project_important(重点项目清单表)】的数据库操作Service
* @date 2025-06-03 14:43:10
*/
public interface ProjectImportantService extends IService<ProjectImportant> {

    /**
     * 获取重点项目清单列表列表
     * @param vo 查询条件
     * @return 重点项目列表
     */
    List<ProjectImportantVo> getList(ProjectImportantConditionVo vo);

    List<ProjectImportantVo> getNormalList(ProjectImportantConditionVo vo);

    /**
     * 获取部门清单列表
     * @param vo
     * @return
     */
    List<ProjectImpVo> getDeptList(ProjectImportantConditionVo vo);

    /**
     * 获取重点项目->项目整理->项目列表
     * @param vo 查询条件
     * @return 项目列表
     */
    List<ProjectImpInputVo> getPiList(ProjectImportantConditionVo vo);

    List<ProjectImpInputVo> getJhList(ProjectImportantConditionVo vo);

    List<ProjectImpStepVo> geStepList(ProjectImportantConditionVo vo);
    /**
     * 保存或修改重点项目录入数据
     * @param projectImpInputVos
     * @return
     */
    Boolean saveOrUpdatePinput(List<ProjectImpInputVo>  projectImpInputVos);

    Boolean saveOrUpdateJhinput(List<ProjectImpInputVo>  projectImpInputVos) throws JsonProcessingException;

    /**
     * 获取重点项目详情
     *
     * @param relId 项目id
     * @return 项目详情
     */
    ProjectImportantVo getImportProjDetail(String relId);

    List<Integer> getYear();

}
