package com.ruoyi.system.service.impl.project;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.toolkit.JoinWrappers;
import com.ruoyi.common.constant.CommonConstant;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysAttachment;
import com.ruoyi.system.domain.dto.AdjustPlanDto;
import com.ruoyi.system.domain.dto.ProjectSummaryDto;
import com.ruoyi.system.domain.event.OperateEvent;
import com.ruoyi.system.domain.excel.ProjectInfoExcel;
import com.ruoyi.system.domain.excel.ProjectInfoMainExcel;
import com.ruoyi.system.domain.excel.ProjectInfoTotalExcel;
import com.ruoyi.system.domain.project.*;
import com.ruoyi.system.domain.vo.project.*;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.SysAttachmentService;
import com.ruoyi.system.service.project.*;
import com.ruoyi.system.mapper.project.ProjectInfoMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【project_info(项目信息表;)】的数据库操作Service实现
 * @date 2025-06-03 15:28:40
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy, @Autowired})
public class ProjectInfoServiceImpl extends ServiceImpl<ProjectInfoMapper, ProjectInfo> implements ProjectInfoService {

    private final ProjectYearRelationService projectYearRelationService;
    private final ProjectPlanService projectPlanService;
    private final SysAttachmentService sysAttachmentService;
    private final ProjectSnService projectSnService;
    private final ISysDeptService sysDeptService;
    private final ProjectPerformanceService projectPerformanceService;


    @Override
    public List<ProjectInfoVo> getNormalList(ProjectQueryConditionVo vo) {
        List<ProjectInfoVo> list = new ArrayList<>();
        List<ProjectYearRelation> yearRelations = projectYearRelationService.lambdaQuery()
                .eq(ProjectYearRelation::getYear, vo.getYear())
                .eq(ProjectYearRelation::getIsClose, CommonConstant.ZeroOrOne.ZERO.value)
                .eq(ProjectYearRelation::getIsRecycle, CommonConstant.ZeroOrOne.ZERO.value)
                .list();
        return getProjectInfoVos(vo, list, yearRelations);
    }

    @Override
    public List<ProjectInfoVo> getAbnormalList(ProjectQueryConditionVo vo) {
        List<ProjectInfoVo> list = new ArrayList<>();
        List<ProjectYearRelation> yearRelations = projectYearRelationService.lambdaQuery()
                .eq(ProjectYearRelation::getYear, vo.getYear())
                .and(q -> q.eq(ProjectYearRelation::getIsClose, CommonConstant.ZeroOrOne.ONE.value)
                        .or()
                        .eq(ProjectYearRelation::getIsRecycle, CommonConstant.ZeroOrOne.ONE.value))
                .list();
        return getProjectInfoVos(vo, list, yearRelations);
    }

    /**
     * 获取项目信息
     *
     * @param vo            查询条件
     * @param list          存储容器
     * @param yearRelations 年度项目ids
     * @return 项目信息
     */
    private List<ProjectInfoVo> getProjectInfoVos(ProjectQueryConditionVo vo, List<ProjectInfoVo> list, List<ProjectYearRelation> yearRelations) {
        List<String> proIds = yearRelations.stream().map(ProjectYearRelation::getProjId).toList();
        Map<String, ProjectYearRelation> yearRelationMap = yearRelations.stream().collect(Collectors.toMap(ProjectYearRelation::getProjId, p -> p));
        if (!proIds.isEmpty()) {
            List<ProjectInfo> infos;
            LambdaQueryChainWrapper<ProjectInfo> wrapper = this.lambdaQuery();
            wrapper.and(StringUtils.isNotBlank(vo.getProjectUnits()) && !"1".equals(vo.getOnlyAssess()),
                            q -> q.eq(ProjectInfo::getApplyOrgid, vo.getProjectUnits())
                                    .or()
                                    .eq(ProjectInfo::getSubmitOrgid, vo.getProjectUnits())
                                    .or()
                                    .eq(ProjectInfo::getAssessOrgid, vo.getProjectUnits())
                                    .or()
                                    .apply("(JSON_CONTAINS(JSON_EXTRACT(cooperate_orgid, '$.id'), '" + vo.getProjectUnits() + "', '$') = 1)")
                    )
                    .eq(StringUtils.isNotBlank(vo.getProjectUnits()) && "1".equals(vo.getOnlyAssess()), ProjectInfo::getAssessOrgid, vo.getProjectUnits())
                    .eq(StringUtils.isNotBlank(vo.getTypeId()), ProjectInfo::getTypeId, vo.getTypeId())
                    .eq(StringUtils.isNotBlank(vo.getNatureCode()), ProjectInfo::getNatureCode, vo.getNatureCode())
                    .eq(StringUtils.isNotBlank(vo.getIsSzhyq()), ProjectInfo::getIsSzhyq, vo.getIsSzhyq())
                    .eq(StringUtils.isNotBlank(vo.getIsJxhgl()), ProjectInfo::getIsJxhgl, vo.getIsJxhgl())
                    .eq(StringUtils.isNotBlank(vo.getIsZfgm()), ProjectInfo::getIsZfgm, vo.getIsZfgm())
                    .eq(StringUtils.isNotBlank(vo.getBasketCode()), ProjectInfo::getBasketCode, vo.getBasketCode())
                    .eq(StringUtils.isNotBlank(vo.getLibState()), ProjectInfo::getLibState, vo.getLibState())
                    .eq(StringUtils.isNotBlank(vo.getCheckState()), ProjectInfo::getCheckState, vo.getCheckState())
                    .like(StringUtils.isNotBlank(vo.getName()), ProjectInfo::getName, vo.getName())
                    .in(ProjectInfo::getId, proIds);
            if (Objects.nonNull(vo.getPurposeIds()) && !vo.getPurposeIds().isEmpty()) {
                for (String purposeId : vo.getPurposeIds()) {
                    wrapper.apply("(JSON_CONTAINS(JSON_EXTRACT(purpose, '$.value'), '\"" + purposeId + "\"', '$') = 1)");
                }
            }
            infos = wrapper.list();
            // 获取指定年度的所有项目计划
            Map<String, ProjectPlan> planMap = projectPlanService.lambdaQuery()
                    .eq(ProjectPlan::getYear, vo.getYear())
                    .list()
                    .stream()
                    .collect(Collectors.toMap(ProjectPlan::getProjId, p -> p));
            List<String> idList = infos.stream().map(ProjectInfo::getId).toList();
            Map<String, ProjectSn> snMap;
            if (!idList.isEmpty()) {
                //根据用户id获取所有项目编号信息
                snMap = projectSnService.lambdaQuery()
                        .in(ProjectSn::getProjId, idList)
                        .list()
                        .stream()
                        .collect(Collectors.toMap(ProjectSn::getProjId, p -> p));
            } else {
                snMap = null;
            }
            // 获取所有项目计划信息
            List<UsagePlanItem> usagePlanItems = JoinWrappers.lambda(UsagePlanItem.class)
                    .selectAll(UsagePlanItem.class)
                    .join("inner join", UsagePlan.class, UsagePlan::getId, UsagePlanItem::getFundId)
                    .eq(UsagePlan::getYear, vo.getYear())
                    .eq(UsagePlan::getIsSnap, CommonConstant.ZeroOrOne.ZERO.value)
                    .list();
            infos.forEach(e -> {
                ProjectInfoVo projectInfoVo = BeanUtil.copyProperties(e, ProjectInfoVo.class);
                usagePlanItems.stream().filter(item -> e.getId().equals(item.getProjId())).findAny().ifPresent(projectInfoVo::setUsagePlanItem);
                if (Objects.nonNull(snMap)) {
                    projectInfoVo.setProjectSn(snMap.get(e.getId()));
                }
                projectInfoVo.setFundPlan(planMap.get(e.getId()));
                projectInfoVo.setCurrentYearRelation(yearRelationMap.get(e.getId()));
                list.add(projectInfoVo);
            });
        }
        return list;
    }

    @Override
    public ProjectInfoVo getProjectDetail(String id, Integer year) {
        ProjectInfo byId = this.getById(id);
        ProjectInfoVo projectInfoVo = BeanUtil.copyProperties(byId, ProjectInfoVo.class);
        //设置资金计划
        projectInfoVo.setFundPlan(projectPlanService.lambdaQuery()
                .eq(ProjectPlan::getProjId, id)
                .eq(ProjectPlan::getYear, year)
                .one());
        //设置附件
        projectInfoVo.setSysAttachments(sysAttachmentService.lambdaQuery()
                .eq(SysAttachment::getSourceType, CommonConstant.SourceType.PROJECT.value)
                .eq(SysAttachment::getSourceId, id)
                .list());
        //设计年度信息
        projectInfoVo.setCurrentYearRelation(projectYearRelationService.lambdaQuery()
                .eq(ProjectYearRelation::getProjId, id)
                .eq(ProjectYearRelation::getYear, year)
                .one());
        //设置项目编号信息
        projectInfoVo.setProjectSn(projectSnService.lambdaQuery()
                .eq(ProjectSn::getProjId, id)
                .one());
        //设置项目绩效信息
        projectInfoVo.setPerformance(projectPerformanceService.lambdaQuery()
                .eq(ProjectPerformance::getProjId, id)
                .list());
        return projectInfoVo;
    }

    @Override
    public List<ProjectInfoVo> getProjectDetailForUsagePlan(Integer year) {
        List<ProjectInfoVo> list = new ArrayList<>();
        List<UsagePlanItem> usagePlanItems = JoinWrappers.lambda(UsagePlanItem.class)
                .selectAll(UsagePlanItem.class)
                .join("inner join", UsagePlan.class, UsagePlan::getId, UsagePlanItem::getFundId)
                .eq(UsagePlan::getYear, year)
                .eq(UsagePlan::getIsSnap, CommonConstant.ZeroOrOne.ZERO.value)
                .list();
        List<String> ids = usagePlanItems.stream().map(UsagePlanItem::getProjId).filter(Objects::nonNull).toList();
        if (!ids.isEmpty()) {
            list = this.getBaseMapper().getProjectDetailForUsagePlan(ids, year);
        }
        return list;
    }

    public ProjectInfoVo getProjectDetailForUsagePlan(String id, Integer year) {
        ProjectInfo byId = this.getById(id);
        ProjectInfoVo projectInfoVo = BeanUtil.copyProperties(byId, ProjectInfoVo.class);
        //设置上一年资金计划
        projectInfoVo.setPrevFundPlan(projectPlanService.lambdaQuery()
                .eq(ProjectPlan::getProjId, id)
                .eq(ProjectPlan::getYear, year - 1)
                .one());
        //设置资金计划
        projectInfoVo.setFundPlan(projectPlanService.lambdaQuery()
                .eq(ProjectPlan::getProjId, id)
                .eq(ProjectPlan::getYear, year)
                .one());

        //设计年度信息
        projectInfoVo.setCurrentYearRelation(projectYearRelationService.lambdaQuery()
                .eq(ProjectYearRelation::getProjId, id)
                .eq(ProjectYearRelation::getYear, year)
                .one());
        return projectInfoVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> saveProjectInfo(ProjectInfoVo vo) {
        ProjectInfo projectInfo = BeanUtil.copyProperties(vo, ProjectInfo.class);
        ProjectInfo one = this.lambdaQuery().eq(ProjectInfo::getName, projectInfo.getName()).one();
        if (Objects.nonNull(one)) {
            throw new ServiceException("项目名称已存在");
        }
        //新增项目信息
        Map<String, String> map = new HashMap<>();
        Optional<Integer> max = this.lambdaQuery().list().stream().map(ProjectInfo::getSeqid).max(Integer::compareTo);
        max.ifPresentOrElse(integer -> projectInfo.setSeqid(integer + 1), () -> projectInfo.setSeqid(1));
        projectInfo.setApplyOrgname(SecurityUtils.getLoginUser().getUser().getDept().getDeptName());
        projectInfo.setState(CommonConstant.StateType.DRAFT.value);

        //todo 临时将考核单位设置为申报单位，后期可能去除
        projectInfo.setAssessOrgid(SecurityUtils.getDeptId());
        SysDept sysDept = sysDeptService.selectDeptById(projectInfo.getAssessOrgid());
        projectInfo.setAssessOrgname(sysDept.getDeptName());

        this.saveOrUpdate(projectInfo);

        //新增项目编号记录
        ProjectSn sn = this.generateSn(projectInfo.getId(), projectInfo.getSeqid(), null);

        //新增项目年度信息
        ProjectYearRelation projectYearRelation = new ProjectYearRelation();
        projectYearRelation.setProjId(projectInfo.getId());
        projectYearRelation.setProjType(Integer.valueOf(vo.getProjType()));
        projectYearRelation.setYear(LocalDate.now().getYear());
        projectYearRelation.setUpdatedTime(new Date());
        Optional<Integer> max1 = projectYearRelationService.lambdaQuery()
                .list()
                .stream()
                .map(ProjectYearRelation::getSeqNo)
                .max(Integer::compareTo);
        max1.ifPresentOrElse(integer -> projectYearRelation.setSeqNo(integer + 1), () -> projectYearRelation.setSeqNo(1));
        projectYearRelationService.save(projectYearRelation);

        //新增项目(计划）操作记录表
        publishOperateEvent(projectInfo.getId(), "新增");

        map.put("id", projectInfo.getId());
        map.put("tempSn", sn.getTempSn());
        return map;
    }

    @Override
    public Boolean updateProjectInfo(ProjectInfo projectInfo) {
        List<ProjectInfo> list = this.lambdaQuery()
                .eq(ProjectInfo::getName, projectInfo.getName())
                .ne(ProjectInfo::getId, projectInfo.getId())
                .list();
        if (!list.isEmpty()){
            throw new ServiceException("项目名称已存在");
        }
        boolean update = this.updateById(projectInfo);
        //新增项目(计划）操作记录表
        publishOperateEvent(projectInfo.getId(), "修改");
        return update;
    }

    @Override
    public List<ProjectInfoCopyVo> getReproducibleProject(ProjectQueryConditionVo vo) {
        return this.getBaseMapper().getReproducibleProject(vo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean copyProject(String[] ids, Integer year) {
        for (String id : ids) {
            //复制项目信息
            ProjectInfo projectInfo = this.getById(id);
            projectInfo.setId(null);
            Optional<Integer> max = this.lambdaQuery().list().stream().map(ProjectInfo::getSeqid).max(Integer::compareTo);
            max.ifPresentOrElse(integer -> projectInfo.setSeqid(integer + 1), () -> projectInfo.setSeqid(1));
            projectInfo.setNatureCode("JC");
            projectInfo.setNatureName("经常性项目");
            projectInfo.setName(projectInfo.getName() + "[复制]");
            projectInfo.setApplyTime(new Date());
            projectInfo.setBuildBegin(updateDate(true));
            projectInfo.setBuildEnd(updateDate(false));
            projectInfo.setFundBegin(updateDate(true));
            projectInfo.setFundEnd(updateDate(false));
            projectInfo.setState(CommonConstant.StateType.DRAFT.value);
            projectInfo.setLibAmount(null);
            projectInfo.setContractAmount(null);
            projectInfo.setLibState(null);
            projectInfo.setLibReason(null);
            projectInfo.setCheckState(null);
            projectInfo.setCheckReason(null);
            projectInfo.setBasketCode(null);
            projectInfo.setBasketName(null);
            projectInfo.setBaseProjid(null);
            projectInfo.setCreateBy(null);
            projectInfo.setCreateTime(null);
            projectInfo.setUpdateBy(null);
            projectInfo.setUpdateTime(null);
            this.save(projectInfo);

            //新增编号信息
            this.generateSn(projectInfo.getId(), projectInfo.getSeqid(), projectInfo.getApplyOrgid());

            //生成年度信息
            ProjectYearRelation yearRelation = new ProjectYearRelation();
            yearRelation.setYear(LocalDate.now().getYear());
            yearRelation.setProjId(projectInfo.getId());
            yearRelation.setProjType(CommonConstant.ProjectType.RECURRENT_PROJECT.value);
            Optional<Integer> maxSeqNo = projectYearRelationService.lambdaQuery()
                    .list()
                    .stream()
                    .map(ProjectYearRelation::getSeqNo)
                    .max(Integer::compareTo);
            maxSeqNo.ifPresentOrElse(integer -> yearRelation.setSeqNo(integer + 1), () -> yearRelation.setSeqNo(1));
            yearRelation.setUpdatedTime(new Date());
            projectYearRelationService.save(yearRelation);

            //复制绩效信息
            List<ProjectPerformance> performanceList = projectPerformanceService.lambdaQuery()
                    .eq(ProjectPerformance::getProjId, id)
                    .list();
            for (ProjectPerformance performance : performanceList) {
                performance.setId(null);
                performance.setProjId(projectInfo.getId());
                performance.setCreateBy(null);
                performance.setCreateTime(null);
                performance.setUpdateBy(null);
                performance.setUpdateTime(null);
                projectPerformanceService.save(performance);
            }
        }
        return true;
    }

    @Override
    public Boolean updateProjectSummary(ProjectSummaryDto dto) {

        String projId = dto.getProjId();
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, projId)
                .set(ProjectInfo::getNecessity, dto.getNecessity())
                .set(ProjectInfo::getBasis, dto.getBasis())
                .set(ProjectInfo::getMainCnt, dto.getMainCnt())
                .set(ProjectInfo::getActionPlan, dto.getActionPlan())
                .update();

        //新增项目(计划）操作记录表
        publishOperateEvent(dto.getProjId(), "修改");

        return update;
    }

    @Override
    public Boolean moveNextYear(String pid, Integer year) {

        boolean update = projectYearRelationService.lambdaUpdate()
                .eq(ProjectYearRelation::getProjId, pid)
                .eq(ProjectYearRelation::getYear, year)
                .set(ProjectYearRelation::getYear, year + 1)
                .update();

        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "移动一下年");

        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reportProject(String pid, Integer year) {
//        ProjectInfo info = this.lambdaQuery().eq(ProjectInfo::getId, pid).one();

        List<ProjectPerformance> performanceList = projectPerformanceService.lambdaQuery()
                .eq(ProjectPerformance::getProjId, pid)
                .list();
        if (performanceList.isEmpty()) {
            throw new ServiceException("[绩效目标]绩效分解目标必须填写");
        }
        ProjectPlan projectPlan = projectPlanService.lambdaQuery()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .one();
        if (StringUtils.isBlank(projectPlan.getPlanDetail())) {
            throw new ServiceException("计划明细请填写完整，当年上报计划必须填写");
        }
        projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getDeclareUserid, SecurityUtils.getUserId())
                .set(ProjectPlan::getDeclareTime, new Date())
                .set(ProjectPlan::getPlanState, CommonConstant.StateType.ESCALATED.value)
                .update();


        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, pid)
                .set(ProjectInfo::getSubmitOrgid, SecurityUtils.getLoginUser().getDeptId())
                .set(ProjectInfo::getSubmitOrgname, SecurityUtils.getUsername())
                .set(ProjectInfo::getSubmitTime, new Date())
                .set(ProjectInfo::getState, CommonConstant.StateType.ESCALATED.value)
                .update();

        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "上报");
        publishOperateEvent(pid, year, CommonConstant.ProjectOrPlan.PLAN, "上报");

        return update;
    }

    @Override
    public Boolean onlyReportProject(String pid) {

        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, pid)
                .set(ProjectInfo::getSubmitOrgid, SecurityUtils.getLoginUser().getDeptId())
                .set(ProjectInfo::getSubmitTime, new Date())
                .set(ProjectInfo::getState, CommonConstant.StateType.ESCALATED.value)
                .update();

        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "上报");

        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean withdrawProject(String pid, Integer year) {

        projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getDeclareUserid, null)
                .set(ProjectPlan::getDeclareTime, null)
                .set(ProjectPlan::getPlanState, CommonConstant.StateType.DRAFT.value)
                .update();

        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, pid)
                .set(ProjectInfo::getSubmitOrgid, null)
                .set(ProjectInfo::getSubmitOrgname, null)
                .set(ProjectInfo::getSubmitTime, null)
                .set(ProjectInfo::getState, CommonConstant.StateType.DRAFT.value)
                .update();

        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "撤销上报");
        publishOperateEvent(pid, year, CommonConstant.ProjectOrPlan.PLAN, "撤销上报");


        return update;
    }

    @Override
    public Boolean onlyWithdrawProject(String pid) {
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, pid)
                .set(ProjectInfo::getSubmitOrgid, null)
                .set(ProjectInfo::getSubmitOrgname, null)
                .set(ProjectInfo::getSubmitTime, null)
                .set(ProjectInfo::getState, CommonConstant.StateType.DRAFT.value)
                .update();

        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "撤销上报");

        return update;
    }

    @Override
    public Boolean putTrash(String pid, Integer year) {
        ProjectYearRelation yearRelation = projectYearRelationService.lambdaQuery()
                .eq(ProjectYearRelation::getProjId, pid)
                .eq(ProjectYearRelation::getYear, year)
                .one();

        boolean update = projectYearRelationService.lambdaUpdate()
                .eq(ProjectYearRelation::getId, yearRelation.getId())
                .set(CommonConstant.ProjectType.CARRYOVER_PROJECT.value.equals(yearRelation.getProjType()), ProjectYearRelation::getIsClose, CommonConstant.ZeroOrOne.ONE.value)
                .set(!CommonConstant.ProjectType.CARRYOVER_PROJECT.value.equals(yearRelation.getProjType()), ProjectYearRelation::getIsRecycle, CommonConstant.ZeroOrOne.ONE.value)
                .set(ProjectYearRelation::getUpdatedTime, new Date())
                .update();

        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "放入回收站");
        return update;
    }

    @Override
    public Boolean recoveryProject(String pid, Integer year) {
        ProjectYearRelation yearRelation = projectYearRelationService.lambdaQuery()
                .eq(ProjectYearRelation::getProjId, pid)
                .eq(ProjectYearRelation::getYear, year)
                .one();
        boolean update = projectYearRelationService.lambdaUpdate()
                .eq(ProjectYearRelation::getId, yearRelation.getId())
                .set(CommonConstant.ProjectType.CARRYOVER_PROJECT.value.equals(yearRelation.getProjType()), ProjectYearRelation::getIsClose, CommonConstant.ZeroOrOne.ZERO.value)
                .set(!CommonConstant.ProjectType.CARRYOVER_PROJECT.value.equals(yearRelation.getProjType()), ProjectYearRelation::getIsRecycle, CommonConstant.ZeroOrOne.ZERO.value)
                .set(ProjectYearRelation::getUpdatedTime, new Date())
                .update();

        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "回收站恢复");
        return update;
    }

    @Override
    public Boolean removeYearRelationByPid(String pid, Integer year) {
        boolean remove = this.projectYearRelationService.lambdaUpdate()
                .eq(ProjectYearRelation::getProjId, pid)
                .eq(ProjectYearRelation::getYear, year)
                .remove();

        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "删除" + year + "年项目");
        return remove;
    }

    @Override
    public Boolean removeProjectById(String pid) {
        boolean b = this.removeById(pid);
        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "删除项目");
        return b;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean backProject(String pid) {
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, pid)
                .set(ProjectInfo::getState, CommonConstant.StateType.BACK.value)
                .set(ProjectInfo::getBasketCode, null)
                .set(ProjectInfo::getBasketName, null)
                .set(ProjectInfo::getLibAmount, null)
                .set(ProjectInfo::getLibState, null)
                .set(ProjectInfo::getLibReason, null)
                .set(ProjectInfo::getCheckAmount, null)
                .set(ProjectInfo::getCheckState, null)
                .set(ProjectInfo::getCheckReason, null)
                .update();

        projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .set(ProjectPlan::getPlanState, CommonConstant.StateType.BACK.value)
                .set(ProjectPlan::getDeclareUserid, null)
                .set(ProjectPlan::getDeclareTime, null)
                .set(ProjectPlan::getFormalAmount, null)
                .set(ProjectPlan::getAdjustAmount, null)
                .set(ProjectPlan::getApplyState, CommonConstant.ApplyStateType.BACK.value)
                .update();
        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "退回申请人");
        return update;
    }

    @Override
    public Boolean adjustPlan(AdjustPlanDto dto) {
        boolean update = projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, dto.getProjId())
                .eq(ProjectPlan::getYear, dto.getYear())
                .set(ProjectPlan::getApplyAdjust, dto.getApplyAdjust())
                .set(ProjectPlan::getApplyReason, dto.getApplyReason())
                .set(ProjectPlan::getApplyUserid, SecurityUtils.getUserId())
                .set(ProjectPlan::getApplyTime, new Date())
                .set(ProjectPlan::getApplyState, CommonConstant.ApplyStateType.ESCALATED.value)
                .update();
        //新增项目(计划）操作记录表
        publishOperateEvent(dto.getProjId(), dto.getYear(), CommonConstant.ProjectOrPlan.PLAN, "计划调整");
        return update;
    }

    @Override
    public Boolean withdrawAdjustPlan(String pid, Integer year) {
        boolean update = projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getApplyState, CommonConstant.ApplyStateType.BACK.value)
                .update();
        //新增项目(计划）操作记录表
        publishOperateEvent(pid, year, CommonConstant.ProjectOrPlan.PLAN, "撤销计划调整");
        return update;
    }

    @Override
    public List<ProjectInfoOfPlanVo> getProjectInfoOfPlan(ProjectQueryOfUsagePlanConditionVo vo) {
        List<ProjectInfoOfPlanVo> planVoList = this.getBaseMapper().getProjectInfoOfPlan(vo);
        List<UsagePlanItem> itemList = JoinWrappers.lambda(UsagePlanItem.class)
                .selectAll(UsagePlanItem.class)
                .join("inner join", UsagePlan.class, UsagePlan::getId, UsagePlanItem::getFundId)
                .eq(UsagePlan::getYear, vo.getYear())
                .eq(UsagePlan::getIsSnap, CommonConstant.ZeroOrOne.ZERO.value)
                .list();
        if (Objects.nonNull(itemList) && !itemList.isEmpty()) {
            List<String> pidList = itemList.stream().map(UsagePlanItem::getProjId).toList();
            planVoList.removeIf(planVo -> pidList.contains(planVo.getId()));
        }
        return planVoList;
    }

    @Override
    public ProjectInfoMainExcel getProjectInfoMainExcel(ProjectQueryConditionVo vo) {
        List<ProjectInfoVo> abnormalList = getNormalList(vo);
        ProjectInfoMainExcel mainExcel = new ProjectInfoMainExcel();
        List<ProjectInfoExcel> jzList = new ArrayList<>();
        List<ProjectInfoExcel> jcList = new ArrayList<>();
        List<ProjectInfoExcel> xzList = new ArrayList<>();
        ProjectInfoTotalExcel jzTotal = new ProjectInfoTotalExcel();
        ProjectInfoTotalExcel jcTotal = new ProjectInfoTotalExcel();
        ProjectInfoTotalExcel xzTotal = new ProjectInfoTotalExcel();
        ProjectInfoTotalExcel total = new ProjectInfoTotalExcel();
        for (ProjectInfoVo info : abnormalList) {
            ProjectYearRelation currentYearRelation = info.getCurrentYearRelation();
            ProjectInfoExcel projectInfoExcel = new ProjectInfoExcel();
            projectInfoExcel.setName(info.getName());
            projectInfoExcel.setAssessOrgname(info.getAssessOrgname());
            projectInfoExcel.setHandler(info.getHandler());
            projectInfoExcel.setEstAmount(Objects.nonNull(info.getEstAmount()) ? bigDecimalToString(info.getEstAmount()) : null);
            String projectStatus;
            Integer state = info.getState();
            if (state == 0) {
                projectStatus = "未上报";
            } else if (StringUtils.isBlank(info.getBasketName())) {
                projectStatus = "已上报";
            } else {
                projectStatus = info.getBasketName();
            }

            projectInfoExcel.setProjectStatus(projectStatus);
            projectInfoExcel.setLibAmount(Objects.nonNull(info.getLibAmount()) ? bigDecimalToString(info.getLibAmount()) : null);
            projectInfoExcel.setCheckAmount(Objects.nonNull(info.getCheckAmount()) ? bigDecimalToString(info.getCheckAmount()) : null);
            //todo 待完善
            projectInfoExcel.setHistoricalExecutedAmounts(null);
            //todo 待完善
            projectInfoExcel.setCurrYearExecutedAmounts(null);
            if (Objects.nonNull(projectInfoExcel.getHistoricalExecutedAmounts()) || Objects.nonNull(projectInfoExcel.getCurrYearExecutedAmounts())) {
                BigDecimal historicalExecutedAmounts = Objects.nonNull(projectInfoExcel.getHistoricalExecutedAmounts()) ? new BigDecimal(projectInfoExcel.getHistoricalExecutedAmounts()) : BigDecimal.ZERO;
                BigDecimal currYearExecutedAmounts = Objects.nonNull(projectInfoExcel.getCurrYearExecutedAmounts()) ? new BigDecimal(projectInfoExcel.getCurrYearExecutedAmounts()) : BigDecimal.ZERO;
                projectInfoExcel.setExecutedAmountsTotal(bigDecimalToString(historicalExecutedAmounts.add(currYearExecutedAmounts)));
            } else {
                projectInfoExcel.setExecutedAmountsTotal(null);
            }
            ProjectPlan projectPlan = info.getFundPlan();
            if (Objects.nonNull(projectPlan)) {
                if (CommonConstant.StateType.BACK.value.equals(projectPlan.getPlanState()) || CommonConstant.StateType.DRAFT.value.equals(projectPlan.getPlanState())) {
                    projectInfoExcel.setDeclareAmount("未上报");
                } else {
                    projectInfoExcel.setDeclareAmount(bigDecimalToString(projectPlan.getDeclareAmount()));
                }
                projectInfoExcel.setFormalAmount(Objects.nonNull(projectPlan.getFormalAmount()) ? bigDecimalToString(projectPlan.getFormalAmount()) : null);
                Integer applyState = projectPlan.getApplyState();
                if (CommonConstant.ApplyStateType.ESCALATED.value.equals(applyState) || CommonConstant.ApplyStateType.CONFIRM.value.equals(applyState)) {
                    projectInfoExcel.setApplyAdjust(Objects.nonNull(projectPlan.getApplyAdjust()) ? bigDecimalToString(projectPlan.getApplyAdjust()) : null);
                } else {
                    projectInfoExcel.setApplyAdjust(null);
                }
                projectInfoExcel.setAdjustAmount(Objects.nonNull(projectPlan.getAdjustAmount()) ? bigDecimalToString(projectPlan.getAdjustAmount()) : null);
            } else {
                projectInfoExcel.setDeclareAmount("未上报");
            }
            //todo 待完善
            projectInfoExcel.setRemark(null);
            if (CommonConstant.ProjectType.CARRYOVER_PROJECT.value.equals(currentYearRelation.getProjType())) {
                computeTotal(jzList, jzTotal, projectInfoExcel);
            } else if (CommonConstant.ProjectType.RECURRENT_PROJECT.value.equals(currentYearRelation.getProjType())) {
                computeTotal(jcList, jcTotal, projectInfoExcel);
            } else if (CommonConstant.ProjectType.NEW_PROJECT.value.equals(currentYearRelation.getProjType())) {
                computeTotal(xzList, xzTotal, projectInfoExcel);
            }
        }
        //计算总合计
        List<ProjectInfoTotalExcel> list = Arrays.asList(jzTotal, jcTotal, xzTotal);
        total.setEstAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getEstAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setLibAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getLibAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setCheckAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getCheckAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setHistoricalExecutedAmountsTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getHistoricalExecutedAmountsTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setCurrYearExecutedAmountsTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getCurrYearExecutedAmountsTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setExecutedAmountsTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getExecutedAmountsTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setDeclareAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getDeclareAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setFormalAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getFormalAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setApplyAdjustTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getApplyAdjustTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setAdjustAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getAdjustAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));

        SysDept sysDept = sysDeptService.selectDeptById(Long.valueOf(vo.getProjectUnits()));

        mainExcel.setYear(vo.getYear())
                .setDeptName(sysDept.getDeptName())
                .setJzList(jzList)
                .setJzTotal(jzTotal)
                .setJzCount(jzList.size())
                .setJcList(jcList)
                .setJcTotal(jcTotal)
                .setJcCount(jcList.size())
                .setXzList(xzList)
                .setXzTotal(xzTotal)
                .setXzCount(xzList.size())
                .setHjCount(jzList.size() + jcList.size() + xzList.size())
                .setTotal(total);

        return mainExcel;
    }

    /**
     * 计算不同类别的合计以及存储
     *
     * @param infoList         存储项目信息excel对象列表
     * @param infoTotal        存储项目信息excel对象合计对象
     * @param projectInfoExcel 单个项目信息excel对象
     */
    private void computeTotal(List<ProjectInfoExcel> infoList, ProjectInfoTotalExcel infoTotal, ProjectInfoExcel projectInfoExcel) {
        projectInfoExcel.setIndex(infoList.size() + 1);
        infoList.add(projectInfoExcel);


        if (Objects.nonNull(projectInfoExcel.getEstAmount())) {
            infoTotal.setEstAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getEstAmountTotal()).add(new BigDecimal(projectInfoExcel.getEstAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getLibAmount())) {
            infoTotal.setLibAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getLibAmountTotal()).add(new BigDecimal(projectInfoExcel.getLibAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getCheckAmount())) {
            infoTotal.setCheckAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getCheckAmountTotal()).add(new BigDecimal(projectInfoExcel.getCheckAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getHistoricalExecutedAmounts())) {
            infoTotal.setHistoricalExecutedAmountsTotal(bigDecimalToString(new BigDecimal(infoTotal.getHistoricalExecutedAmountsTotal()).add(new BigDecimal(projectInfoExcel.getHistoricalExecutedAmounts()))));
        }

        if (Objects.nonNull(projectInfoExcel.getCurrYearExecutedAmounts())) {
            infoTotal.setCurrYearExecutedAmountsTotal(bigDecimalToString(new BigDecimal(infoTotal.getCurrYearExecutedAmountsTotal()).add(new BigDecimal(projectInfoExcel.getCurrYearExecutedAmounts()))));
        }
        infoTotal.setExecutedAmountsTotal(bigDecimalToString(new BigDecimal(infoTotal.getHistoricalExecutedAmountsTotal()).add(new BigDecimal(infoTotal.getCurrYearExecutedAmountsTotal()))));

        if (Objects.nonNull(projectInfoExcel.getDeclareAmount()) && !"未上报".equals(projectInfoExcel.getDeclareAmount())) {
            infoTotal.setDeclareAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getDeclareAmountTotal()).add(new BigDecimal(projectInfoExcel.getDeclareAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getFormalAmount())) {
            infoTotal.setFormalAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getFormalAmountTotal()).add(new BigDecimal(projectInfoExcel.getFormalAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getApplyAdjust())) {
            infoTotal.setApplyAdjustTotal(bigDecimalToString(new BigDecimal(infoTotal.getApplyAdjustTotal()).add(new BigDecimal(projectInfoExcel.getApplyAdjust()))));
        }
        if (Objects.nonNull(projectInfoExcel.getAdjustAmount())) {
            infoTotal.setAdjustAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getAdjustAmountTotal()).add(new BigDecimal(projectInfoExcel.getAdjustAmount()))));
        }
    }

    /**
     * 生成项目编号信息
     *
     * @param pid   项目id
     * @param seqId 项目序号
     * @return 项目编号信息对象
     */
    private ProjectSn generateSn(String pid, Integer seqId, Long deptId) {
        int year = LocalDate.now().getYear();
        ProjectSn projectSn = new ProjectSn();
        projectSn.setProjId(pid);
        projectSn.setTempYear(year);
        projectSn.setTempSeqid(seqId);
        if (Objects.isNull(deptId)) {
            deptId = SecurityUtils.getDeptId();
        }
        SysDept sysDept = sysDeptService.selectDeptById(deptId);
        String dSeqNo = Objects.nonNull(sysDept) ? sysDept.getSeqNo() : "0";
        if (String.valueOf(seqId).length() < 4) {
            projectSn.setTempSn(String.format("临-%d-%s-%04d", year, dSeqNo, seqId));
        } else {
            projectSn.setTempSn(String.format("临-%d-%s-%d", year, dSeqNo, seqId));
        }
        projectSn.setTempTime(new Date());
        projectSnService.save(projectSn);
        return projectSn;
    }

    /**
     * 复制操作部分日期年度跟新
     *
     * @param isBegin 是否是一年的开始，不是则设置一年的最后一天
     * @return 更新后的日期
     */
    private Date updateDate(boolean isBegin) {
        int currentYear = LocalDate.now().getYear();
        Instant instant;
        if (isBegin) {
            instant = LocalDate.of(currentYear, 1, 1).atStartOfDay(ZoneId.systemDefault()).toInstant();
        } else {
            instant = LocalDate.of(currentYear, 12, 31).atStartOfDay(ZoneId.systemDefault()).toInstant();
        }
        return Date.from(instant);
    }


    private void publishOperateEvent(String pid, String operateType) {
        publishOperateEvent(pid, null, CommonConstant.ProjectOrPlan.PROJECT, operateType);
    }

    /**
     * 发布项目操作事件
     *
     * @param pid         项目id（必填）
     * @param year        年度
     * @param type        项目类型
     * @param operateType 操作类型
     */
    private void publishOperateEvent(String pid, Integer year, CommonConstant.ProjectOrPlan type, String operateType) {
        OperateEvent event = new OperateEvent()
                .setProjId(pid)
                .setType(type)
                .setOperateType(operateType)
                .setYear(year);
        SpringUtil.getApplicationContext().publishEvent(event);
    }

    private String bigDecimalToString(BigDecimal val) {
        return val.stripTrailingZeros().toPlainString();
    }
}




