package com.ruoyi.system.service.impl.project;

import com.alibaba.fastjson2.JSON;
import com.github.yulichang.toolkit.JoinWrappers;
import com.ruoyi.common.constant.CommonConstant;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.fund.FundPaybill;
import com.ruoyi.system.domain.fund.FundPaybillSkr;
import com.ruoyi.system.domain.project.ProjectPlan;
import com.ruoyi.system.domain.project.PurposeEntity;
import com.ruoyi.system.domain.vo.project.ProjectInfoVo;
import com.ruoyi.system.domain.vo.project.ProjectManageConditionVo;
import com.ruoyi.system.mapper.project.ProjectInfoMapper;
import com.ruoyi.system.service.impl.SysDictTypeServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 资金计划分解Service
 *
 * <AUTHOR>
 * @date 2025/7/1 14:29
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy, @Autowired})
public class FundPlanBreakDownService {

    private final ProjectInfoMapper projectInfoMapper;
    private final SysDictTypeServiceImpl sysDictTypeService;

    private static final String PROJECT_CATEGORY = "项目分类";
    private static final String CATEGORY_TOTALS = "类别总计";
    private static final String USE_TOTALS = "用途总计";
    private static final String PERCENTAGE = "占比";
    private static final String EXECUTED = "已执行";
    private static final String EXECUTE_RATE = "执行率";
    private static final String SZ = "涉及数字化园区支出";
    private static final String SZ_EXECUTED = "数字化已执行";
    private static final String SZ_EXECUTE_RATE = "数字化执行率";

    public Map<String, List<Map<String, Object>>> getFundPlanBreakDownList(Integer year) {

        //获取项目列表
        ProjectManageConditionVo vo = new ProjectManageConditionVo();
        vo.setYear(year);
        List<ProjectInfoVo> infoList = projectInfoMapper.getProjectManageList(vo);

        if (infoList.isEmpty()) {
            return null;
        }
        List<String> ids = infoList.stream().map(ProjectInfoVo::getId).toList();
        //获取资金拨付信息
        List<FundPaybillSkr> innerJoin = JoinWrappers.lambda(FundPaybillSkr.class)
                .selectAll(FundPaybillSkr.class)
                .select(FundPaybill::getProjId)
                .join("inner join", FundPaybill.class, FundPaybill::getId, FundPaybillSkr::getBillId)
                .in(FundPaybill::getProjId, ids)
                .list();
        Map<String, List<FundPaybillSkr>> skrMap = innerJoin.stream().collect(Collectors.groupingBy(FundPaybillSkr::getProjId));


        List<SysDictData> projectPurpose = sysDictTypeService.selectDictDataByType("project_purpose");
        if (projectPurpose.isEmpty()) {
            throw new ServiceException("请联系系统管理员，设置项目用途字典");
        }
        // 项目用途 键值对
//        Map<String, String> purposeMap = projectPurpose.stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue));
        // 项目用途  列
        List<String> purposeColumn = projectPurpose.stream()
                .sorted((o1, o2) -> Math.toIntExact(o1.getDictSort() - o2.getDictSort()))
                .map(SysDictData::getDictValue)
                .toList();


        List<SysDictData> projectType = sysDictTypeService.selectDictDataByType("project_type");
        // 项目类型 行
        List<String> projectTypeRow = projectType
                .stream()
                .sorted((o1, o2) -> Math.toIntExact(o1.getDictSort() - o2.getDictSort()))
                .map(SysDictData::getDictValue)
                .toList();
        // 项目类型 键值对
        Map<String, String> projectTypeMap = projectType.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

        if (projectTypeRow.isEmpty()) {
            throw new ServiceException("请联系系统管理员，设置项目类型字典");
        }


        // 先求出所有金额二维数组，项目类型 + 已执行 为行，项目用途 + 已执行 + 涉及数字化园区支出 + 数字化已执行 为列
        //初步计划
        BigDecimal[][] earlyResult = IntStream.range(0, projectTypeRow.size() + 1)
                .mapToObj(i -> IntStream.range(0, purposeColumn.size() + 3)
                        .mapToObj(j -> BigDecimal.ZERO)
                        .toArray(BigDecimal[]::new))
                .toArray(BigDecimal[][]::new);
        //正式计划
        BigDecimal[][] formalResult = IntStream.range(0, projectTypeRow.size() + 1)
                .mapToObj(i -> IntStream.range(0, purposeColumn.size() + 3)
                        .mapToObj(j -> BigDecimal.ZERO)
                        .toArray(BigDecimal[]::new))
                .toArray(BigDecimal[][]::new);
        //调整计划
        BigDecimal[][] adjustResult = IntStream.range(0, projectTypeRow.size() + 1)
                .mapToObj(i -> IntStream.range(0, purposeColumn.size() + 3)
                        .mapToObj(j -> BigDecimal.ZERO)
                        .toArray(BigDecimal[]::new))
                .toArray(BigDecimal[][]::new);


        for (ProjectInfoVo info : infoList) {
            List<FundPaybillSkr> skrList = skrMap.get(info.getId());
            BigDecimal payAmount = BigDecimal.ZERO;
            if (skrList != null && !skrList.isEmpty()) {
                payAmount = skrList.stream().map(item -> item.getAmount() == null ? BigDecimal.ZERO : item.getAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            ProjectPlan fundPlan = info.getFundPlan();
            if (Objects.isNull(fundPlan) || CommonConstant.StateType.BACK.value.equals(fundPlan.getPlanState()) || CommonConstant.StateType.DRAFT.value.equals(fundPlan.getPlanState())) {
                continue;
            }
            boolean isSz = "1".equals(info.getIsSzhyq());
            String typeId = info.getTypeId();
            int rowIndex = projectTypeRow.indexOf(typeId);
            String purpose = info.getPurpose();
            List<PurposeEntity> purposeEntities = JSON.parseArray(purpose, PurposeEntity.class);
            for (PurposeEntity entity : purposeEntities) {
                String value = entity.getValue();
                int columnIndex = purposeColumn.indexOf(value);
                if (rowIndex != -1 && columnIndex != -1) {
                    BigDecimal earlyAmount = fundPlan.getEarlyAmount();
                    BigDecimal formalAmount = fundPlan.getFormalAmount();
                    BigDecimal adjustAmount = fundPlan.getApplyAdjust();

                    computeArray(earlyResult, rowIndex, columnIndex, earlyAmount, purposeColumn.size() + 2, projectTypeRow.size(), isSz, payAmount);
                    computeArray(formalResult, rowIndex, columnIndex, formalAmount, purposeColumn.size() + 2, projectTypeRow.size(), isSz, payAmount);
                    computeArray(adjustResult, rowIndex, columnIndex, adjustAmount, purposeColumn.size() + 2, projectTypeRow.size(), isSz, payAmount);

                }
            }

        }
        List<Map<String, Object>> earlyList = handleData(earlyResult, projectTypeRow, projectTypeMap, purposeColumn);
        List<Map<String, Object>> formalList = handleData(formalResult, projectTypeRow, projectTypeMap, purposeColumn);
        List<Map<String, Object>> adjustList = handleData(adjustResult, projectTypeRow, projectTypeMap, purposeColumn);
        LinkedHashMap<String, List<Map<String, Object>>> map = new LinkedHashMap<>();
        map.put("early", earlyList);
        map.put("formal", formalList);
        map.put("adjust", adjustList);
        return map;
    }

    private List<Map<String, Object>> handleData(BigDecimal[][] array, List<String> rowList, Map<String, String> rowMap, List<String> columnList) {

        List<Map<String, Object>> list = new ArrayList<>();

        //总计
        BigDecimal total = BigDecimal.ZERO;
        for (int i = 0; i < rowList.size(); i++) {
            for (int j = 0; j < columnList.size(); j++) {
                total = total.add(array[i][j]);
            }
        }
        // 总的已执行
        BigDecimal totalExecuted = BigDecimal.ZERO;
        // 总的数字化支出
        BigDecimal totalSz = BigDecimal.ZERO;
        // 总的数字化已执行
        BigDecimal totalSzExecuted = BigDecimal.ZERO;


        for (int i = 0; i < rowList.size(); i++) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put(PROJECT_CATEGORY, rowMap.get(rowList.get(i)));
            for (int j = 0; j < columnList.size(); j++) {
                map.put(columnList.get(j), array[i][j]);
            }
            //类别总计
            BigDecimal categoryTotals = Arrays.stream(array[i]).limit(columnList.size()).reduce(BigDecimal.ZERO, BigDecimal::add);
            map.put(CATEGORY_TOTALS, categoryTotals);

            //占比
            computeRate(map, PERCENTAGE, categoryTotals, total);
            //已执行
            BigDecimal executed = array[i][columnList.size()];
            totalExecuted = totalExecuted.add(executed);
            map.put(EXECUTED, executed);

            //执行率
            computeRate(map, EXECUTE_RATE, executed, categoryTotals);

            //涉及数字化园区支出
            BigDecimal szAmount = array[i][columnList.size() + 1];
            totalSz = totalSz.add(szAmount);

            map.put(SZ, szAmount);

            //涉及数字化园区已执行
            BigDecimal szExecuted = array[i][columnList.size() + 2];
            totalSzExecuted = totalSzExecuted.add(szExecuted);

            map.put(SZ_EXECUTED, szExecuted);
            //涉及数字化园区执行率
            computeRate(map, SZ_EXECUTE_RATE, szExecuted, szAmount);

            //保存行记录
            list.add(map);
        }
        //----------------------------- 用途总计 -----------------------------
        Map<String, Object> ytMap = new LinkedHashMap<>();
        //项目分类
        ytMap.put(PROJECT_CATEGORY, USE_TOTALS);
        // 列值 同用途合计
        for (int j = 0; j < columnList.size(); j++) {
            BigDecimal columnTotal = BigDecimal.ZERO;
            for (int i = 0; i < rowList.size(); i++) {
                columnTotal = columnTotal.add(array[i][j]);
            }
            ytMap.put(columnList.get(j), columnTotal);
        }
        //类别总计 总合计
        ytMap.put(CATEGORY_TOTALS, total);
        //占比
        if (total.compareTo(BigDecimal.ZERO) != 0) {
            ytMap.put(PERCENTAGE, "100.0%");
        } else {
            ytMap.put(PERCENTAGE, "0.0%");
        }
        // 已执行
        ytMap.put(EXECUTED, totalExecuted);
        //执行率
        computeRate(ytMap, EXECUTE_RATE, totalExecuted, total);
        //涉及数字化园区支出
        ytMap.put(SZ, totalSz);
        //涉及数字化园区已执行
        ytMap.put(SZ_EXECUTED, "--");
        //涉及数字化园区执行率
        ytMap.put(SZ_EXECUTE_RATE, "--");
        list.add(ytMap);

        //----------------------------- 占比 -----------------------------
        Map<String, Object> zbMap = new LinkedHashMap<>();
        zbMap.put(PROJECT_CATEGORY, PERCENTAGE);
        for (String s : columnList) {
            BigDecimal useTotal = (BigDecimal) ytMap.get(s);
            computeRate(zbMap, s, useTotal, total);
        }
        //类别总计
        zbMap.put(CATEGORY_TOTALS, "--");
        //占比
        zbMap.put(PERCENTAGE, "--");
        // 已执行
        zbMap.put(EXECUTED, "--");
        //执行率
        zbMap.put(EXECUTE_RATE, "--");
        //涉及数字化园区支出
        computeRate(zbMap, SZ, totalSz, total);
        //涉及数字化园区已执行
        zbMap.put(SZ_EXECUTED, "--");
        //涉及数字化园区执行率
        zbMap.put(SZ_EXECUTE_RATE, "--");
        list.add(zbMap);

        //----------------------------- 已执行 -----------------------------
        Map<String, Object> yzxMap = new LinkedHashMap<>();
        // 项目分类
        yzxMap.put(PROJECT_CATEGORY, EXECUTED);
        // 列值
        for (int j = 0; j < columnList.size(); j++) {
            BigDecimal executedAmount = array[rowList.size()][j];
            yzxMap.put(columnList.get(j), executedAmount);
        }
        //类别总计
        yzxMap.put(CATEGORY_TOTALS, "--");
        //占比
        yzxMap.put(PERCENTAGE, "--");
        // 已执行
        yzxMap.put(EXECUTED, "--");
        //执行率
        yzxMap.put(EXECUTE_RATE, "--");
        //涉及数字化园区支出
        yzxMap.put(SZ, "--");
        //涉及数字化园区已执行
        yzxMap.put(SZ_EXECUTED, totalSzExecuted);
        //涉及数字化园区执行率
        yzxMap.put(SZ_EXECUTE_RATE, "--");
        list.add(yzxMap);

        //----------------------------- 执行率 -----------------------------
        Map<String, Object> zxlMap = new LinkedHashMap<>();
        // 项目分类
        zxlMap.put(PROJECT_CATEGORY, EXECUTE_RATE);
        // 列值
        for (String s : columnList) {
            BigDecimal executed = (BigDecimal) yzxMap.get(s);
            BigDecimal useTotal = (BigDecimal) ytMap.get(s);
            computeRate(zxlMap, s, executed, useTotal);
        }
        // 类别总计
        zxlMap.put(CATEGORY_TOTALS, "--");
        // 占比
        zxlMap.put(PERCENTAGE, "--");
        // 已执行
        zxlMap.put(EXECUTED, "--");
        // 执行率
        computeRate(zxlMap, EXECUTE_RATE, totalExecuted, total);
        //涉及数字化园区支出
        zxlMap.put(SZ, "--");
        //涉及数字化园区已执行
        zxlMap.put(SZ_EXECUTED, "--");
        //涉及数字化园区执行率
        computeRate(zxlMap, SZ_EXECUTE_RATE, totalSzExecuted, totalSz);
        list.add(zxlMap);

        return list;
    }

    /**
     * 计算百分比
     *
     * @param storageMap 存储map
     * @param mapKey     map的key
     * @param dividend   分子
     * @param divisor    分母
     */
    private void computeRate(Map<String, Object> storageMap, String mapKey, BigDecimal dividend, BigDecimal divisor) {
        if (dividend.compareTo(BigDecimal.ZERO) != 0 && divisor.compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal rate = dividend.divide(divisor, 3, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
            String plainString = rate.setScale(1, RoundingMode.HALF_UP).toPlainString();
            storageMap.put(mapKey, plainString + "%");
        } else {
            storageMap.put(mapKey, "0.0%");
        }
    }

    /**
     * 填充二维数组中金额
     *
     * @param array           二维数组
     * @param rowIndex        行索引
     * @param columnIndex     列索引
     * @param amount          金额
     * @param lastColumnIndex 最后一列索引
     * @param isSz            是否是涉及数字化园区支出
     * @param payAmount       已执行金额
     */
    private void computeArray(BigDecimal[][] array,
                              int rowIndex,
                              int columnIndex,
                              BigDecimal amount,
                              int lastColumnIndex,
                              int lastRowIndex,
                              boolean isSz,
                              BigDecimal payAmount) {
        if (amount == null) {
            return;
        }
        //填充项目用途金额
        BigDecimal bigDecimal = array[rowIndex][columnIndex];
//        if (Objects.isNull(bigDecimal)) {
//            array[rowIndex][columnIndex] = amount;
//        } else {
        array[rowIndex][columnIndex] = bigDecimal.add(amount);
//        }

        //填充项目类型已执行金额
        BigDecimal executedTypeAmount = array[rowIndex][lastColumnIndex - 2];
        array[rowIndex][lastColumnIndex - 2] = executedTypeAmount.add(payAmount);

        //填充项目用途已执行金额
        BigDecimal executedUseAmount = array[lastRowIndex][columnIndex];
        array[lastRowIndex][columnIndex] = executedUseAmount.add(payAmount);

        if (isSz) {
            //填充项目用途涉及数字化支出金额
            BigDecimal szDecimal = array[rowIndex][lastColumnIndex - 1];
//            if (Objects.isNull(szDecimal)) {
//                array[rowIndex][lastColumnIndex] = amount;
//            } else {
            array[rowIndex][lastColumnIndex - 1] = szDecimal.add(amount);
//            }
            //填充项目用途涉及数字化已执行金额
            BigDecimal szExecutedAmount = array[rowIndex][lastColumnIndex];
            array[rowIndex][lastColumnIndex] = szExecutedAmount.add(payAmount);
        }
    }
}
