package com.ruoyi.system.service.project;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.dto.ProjectBasketDto;
import com.ruoyi.system.domain.dto.ProjectAmountDto;
import com.ruoyi.system.domain.excel.ProjectInfoMainExcel;
import com.ruoyi.system.domain.project.ProjectInfo;
import com.ruoyi.system.domain.vo.project.ProjectInfoVo;
import com.ruoyi.system.domain.vo.project.ProjectManageConditionVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10 13:55
 */
public interface ProjectManageService extends IService<ProjectInfo> {

    /**
     * 获取项目管理列表
     *
     * @param vo 查询条件
     * @return 项目管理列表
     */
    List<ProjectInfoVo> getProjectManageList(ProjectManageConditionVo vo);

    /**
     * 获取项目基础信息
     *
     * @param pid 项目id
     * @return 项目基础信息
     */
    ProjectInfo getProjectBasicInfo(String pid);

    /**
     * 添加重点项目
     *
     * @param pid  项目id
     * @param year 年
     * @return 是否成功
     */
    Boolean addImportantProject(String pid, Integer year);

    /**
     * 取消重点项目
     *
     * @param pid  项目id
     * @param year 年
     * @return 是否成功
     */
    Boolean cancelImportantProject(String pid, Integer year);

    /**
     * 添加项目篮子
     *
     * @param dto 项目篮子信息
     * @return 是否成功
     */
    Boolean addProjectBasket(ProjectBasketDto dto);

    /**
     * 更新项目入库信息
     *
     * @param dto 项目入库信息
     * @return 是否成功
     */
    Boolean updateLibInfo(ProjectAmountDto dto);

    /**
     * 取消项目入库
     *
     * @param pid 项目id
     * @return 是否成功
     */
    Boolean cancelLib(String pid);

    /**
     * 更新项目核定信息
     *
     * @param dto 项目id
     * @return 项目入库信息
     */
    Boolean updateCheckInfo(ProjectAmountDto dto);

    /**
     * 取消项目核定
     *
     * @param pid 项目id
     * @return 是否成功
     */
    Boolean cancelCheck(String pid);

    /**
     * 下达计划
     *
     * @param pid    项目id
     * @param year   年
     * @param amount 金额
     * @return 是否成功
     */
    Boolean releasePlan(String pid, Integer year, BigDecimal amount);

    /**
     * 双击下达计划
     *
     * @param pid    项目id
     * @param year   年
     * @param amount 金额
     * @return 是否成功
     */
    Boolean doubleClickRelease(String pid, Integer year, BigDecimal amount);

    /**
     * 取消下达计划
     *
     * @param pid 项目id
     * @param year 年
     * @return 是否成功
     */
    Boolean cancelRelease(String pid, Integer year);

    /**
     * 调整计划
     *
     * @param pid    项目id
     * @param year   年
     * @param amount 金额
     * @return 是否成功
     */
    Boolean adjustPlan(String pid, Integer year, BigDecimal amount);

    /**
     * 双击调整计划
     *
     * @param pid    项目id
     * @param year   年
     * @param amount 金额
     * @return 是否成功
     */
    Boolean doubleClickAdjust(String pid, Integer year, BigDecimal amount);


    /**
     * 取消调整计划
     *
     * @param pid 项目id
     * @param year 年
     * @return 是否成功
     */
    Boolean cancelAdjust(String pid, Integer year);


    /**
     * 获取导出excel的项目信息列表
     *
     * @param vo 列表参数
     * @return excel中的项目信息列表
     */
    ProjectInfoMainExcel getProjectInfoMainExcel(ProjectManageConditionVo vo);

}
