package com.ruoyi.system.service.impl.project;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.github.yulichang.wrapper.UpdateJoinWrapper;
import com.ruoyi.common.constant.CommonConstant;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.dto.ProjectBasketDto;
import com.ruoyi.system.domain.dto.ProjectAmountDto;
import com.ruoyi.system.domain.event.OperateEvent;
import com.ruoyi.system.domain.excel.ProjectInfoExcel;
import com.ruoyi.system.domain.excel.ProjectInfoMainExcel;
import com.ruoyi.system.domain.excel.ProjectInfoTotalExcel;
import com.ruoyi.system.domain.project.*;
import com.ruoyi.system.domain.vo.project.ProjectInfoVo;
import com.ruoyi.system.domain.vo.project.ProjectManageConditionVo;
import com.ruoyi.system.mapper.project.ProjectInfoMapper;
import com.ruoyi.system.mapper.project.UsagePlanItemMapper;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.SmsService;
import com.ruoyi.system.service.project.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 针对表【project_info(项目信息表;)】的数据库操作Service实现，通过项目管理模块进行管理
 * @date 2025/6/10 13:56
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy, @Autowired})
public class ProjectManageServiceImpl extends ServiceImpl<ProjectInfoMapper, ProjectInfo> implements ProjectManageService {

    public static final String PROJECT_BASKET_DICT_TYPE = "project_basket_type";
    public static final String IMPLEMENT_LIBRARY = "实施库";
    public static final String RESERVE_VAULTS = "储备库";

    @Value("${sms.template.addBasketMessage}")
    private String addBasketTemplate;
    @Value("${sms.template.libMessage}")
    private String libMessageTemplate;
    @Value("${sms.template.checkMessage}")
    private String checkMessageTemplate;

    private final ProjectYearRelationService projectYearRelationService;
    private final ProjectPlanService projectPlanService;
    private final UsagePlanItemMapper usagePlanItemMapper;
    private final ProjectSnService projectSnService;
    private final ISysDeptService sysDeptService;
    private final SmsService smsService;
    private final ISysConfigService configService;

    @Override
    public List<ProjectInfoVo> getProjectManageList(ProjectManageConditionVo vo) {

        List<ProjectInfoVo> infos = this.getBaseMapper().getProjectManageList(vo);
        // 获取所有项目用款计划信息
        List<UsagePlanItem> usagePlanItems = JoinWrappers.lambda(UsagePlanItem.class)
                .selectAll(UsagePlanItem.class)
                .join("inner join", UsagePlan.class, UsagePlan::getId, UsagePlanItem::getFundId)
                .eq(UsagePlan::getYear, vo.getYear())
                .eq(UsagePlan::getIsSnap, CommonConstant.ZeroOrOne.ZERO.value)
                .list();
        infos.forEach(e -> usagePlanItems.stream().filter(item -> e.getId().equals(item.getProjId())).findAny().ifPresent(e::setUsagePlanItem));
        return infos;
    }

    @Override
    public ProjectInfo getProjectBasicInfo(String pid) {
        return this.getById(pid);
    }

    @Override
    public Boolean addImportantProject(String pid, Integer year) {
        boolean update = projectYearRelationService.lambdaUpdate()
                .eq(ProjectYearRelation::getProjId, pid)
                .eq(ProjectYearRelation::getYear, year)
                .set(ProjectYearRelation::getImpProj, CommonConstant.ZeroOrOne.ONE.value)
                .update();
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "添加重点项目");
        return update;
    }

    @Override
    public Boolean cancelImportantProject(String pid, Integer year) {
        boolean update = projectYearRelationService.lambdaUpdate()
                .eq(ProjectYearRelation::getProjId, pid)
                .eq(ProjectYearRelation::getYear, year)
                .set(ProjectYearRelation::getImpProj, CommonConstant.ZeroOrOne.ZERO.value)
                .update();
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "取消重点项目");
        return update;
    }

    @Override
    public Boolean addProjectBasket(ProjectBasketDto dto) {
        //在项目中添加篮子信息
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, dto.getProjId())
                .set(ProjectInfo::getBasketCode, dto.getBasketCode())
                .set(ProjectInfo::getBasketName, dto.getBasketName())
                .set(ProjectInfo::getLibAmount, dto.getLibAmount())
                .update();

        if (CommonConstant.ZeroOrOne.ONE.value.equals(dto.getIsSendSms())) {
            ProjectInfo info = this.getById(dto.getProjId());
            smsService.sendSms(info.getHandlerTel(), String.format(addBasketTemplate, info.getName(), dto.getBasketName(), dto.getLibAmount().toPlainString()));
        }
        //新增项目(计划)操作记录表
        this.publishOperateEvent(dto.getProjId(), "添加篮子");
        return update;
    }

    @Override
    public Boolean updateLibInfo(ProjectAmountDto dto) {
        String dictValue = DictUtils.getDictValue(PROJECT_BASKET_DICT_TYPE, IMPLEMENT_LIBRARY);
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, dto.getProjId())
                .set(ProjectInfo::getBasketCode, dictValue)
                .set(ProjectInfo::getBasketName, IMPLEMENT_LIBRARY)
                .set(ProjectInfo::getLibAmount, dto.getAmount())
                .set(ProjectInfo::getLibReason, dto.getReason())
                .set(ProjectInfo::getLibState, CommonConstant.ZeroOrOne.ONE.value)
                .update();

        if (CommonConstant.ZeroOrOne.ONE.value.equals(dto.getIsSendSms())) {
            ProjectInfo info = this.getById(dto.getProjId());
            smsService.sendSms(info.getHandlerTel(), String.format(libMessageTemplate, info.getName(), info.getLibAmount()));
        }
        //新增项目(计划)操作记录表
        this.publishOperateEvent(dto.getProjId(), "更新入库信息");
        return update;
    }

    @Override
    public Boolean cancelLib(String pid) {
        String dictValue = DictUtils.getDictValue(PROJECT_BASKET_DICT_TYPE, RESERVE_VAULTS);
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, pid)
                .set(ProjectInfo::getBasketCode, dictValue)
                .set(ProjectInfo::getBasketName, RESERVE_VAULTS)
                .set(ProjectInfo::getLibState, CommonConstant.ZeroOrOne.ZERO.value)
                .update();
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "取消入库");
        return update;
    }

    @Override
    public Boolean updateCheckInfo(ProjectAmountDto dto) {
        String dictValue = DictUtils.getDictValue(PROJECT_BASKET_DICT_TYPE, IMPLEMENT_LIBRARY);
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, dto.getProjId())
                .set(ProjectInfo::getBasketCode, dictValue)
                .set(ProjectInfo::getBasketName, IMPLEMENT_LIBRARY)
                .set(ProjectInfo::getLibAmount, dto.getAmount())
                .set(ProjectInfo::getCheckAmount, dto.getAmount())
                .set(ProjectInfo::getCheckReason, dto.getReason())
                .set(ProjectInfo::getCheckState, CommonConstant.ZeroOrOne.ONE.value)
                .update();

        if (CommonConstant.ZeroOrOne.ONE.value.equals(dto.getIsSendSms())) {
            ProjectInfo info = this.getById(dto.getProjId());
            String checkUser = SecurityUtils.getUsername();
            smsService.sendSms(info.getHandlerTel(), String.format(checkMessageTemplate, info.getName(), checkUser, info.getLibAmount()));
        }
        //新增项目(计划)操作记录表
        this.publishOperateEvent(dto.getProjId(), "更新核定信息");
        return update;
    }

    @Override
    public Boolean cancelCheck(String pid) {
        String dictValue = DictUtils.getDictValue(PROJECT_BASKET_DICT_TYPE, RESERVE_VAULTS);
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, pid)
                .set(ProjectInfo::getCheckAmount, null)
                .set(ProjectInfo::getBasketCode, dictValue)
                .set(ProjectInfo::getBasketName, RESERVE_VAULTS)
                .set(ProjectInfo::getCheckState, CommonConstant.ZeroOrOne.ZERO.value)
                .update();
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "取消核定");
        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean releasePlan(String pid, Integer year, BigDecimal amount) {
        // 生成正式编号
        generateFormalSn(pid, year);

        boolean update = projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getPlanState, CommonConstant.StateType.RELEASED.value)
                .set(ProjectPlan::getFormalAmount, amount)
                .update();
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "下达计划");
        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean doubleClickRelease(String pid, Integer year, BigDecimal amount) {
        String flag = configService.selectConfigByKey("sys.click.releasePlan");
        if (StringUtils.isBlank(flag) || !(CommonConstant.ZeroOrOne.ONE.value.equals(flag) || CommonConstant.ZeroOrOne.ZERO.value.equals(flag))) {
            throw new ServiceException("请联系管理员配置正确的参数！");
        }
        UpdateJoinWrapper<UsagePlanItem> wrapper = JoinWrappers.update(UsagePlanItem.class);
        UsagePlanItem usagePlanItem = new UsagePlanItem();
        if (CommonConstant.ZeroOrOne.ZERO.value.equals(flag)) {
            projectPlanService.updateEarlyAmount(pid, year, amount);
            usagePlanItem.setCurrEarly(amount);
        }
        if (CommonConstant.ZeroOrOne.ONE.value.equals(flag)) {
            usagePlanItem.setCurrFormal(amount);
        }
        wrapper.leftJoin(UsagePlan.class, UsagePlan::getId, UsagePlanItem::getFundId)
                .eq(UsagePlan::getYear, year)
                .eq(UsagePlan::getIsSnap, CommonConstant.ZeroOrOne.ZERO.value)
                .eq(UsagePlanItem::getProjId, pid);
        int i = usagePlanItemMapper.updateJoin(usagePlanItem, wrapper);
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "双击下达计划");
        return i == 1;
    }

    @Override
    public Boolean cancelRelease(String pid, Integer year) {
        boolean update = projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getPlanState, CommonConstant.StateType.ESCALATED.value)
                .set(ProjectPlan::getFormalAmount, null)
                .update();
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "取消下达计划");
        return update;
    }

    @Override
    public Boolean adjustPlan(String pid, Integer year, BigDecimal amount) {
        boolean update = projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getAdjustAmount, amount)
                .set(ProjectPlan::getApplyState, CommonConstant.ApplyStateType.CONFIRM.value)
                .update();
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "调整下达");
        return update;
    }

    @Override
    public Boolean doubleClickAdjust(String pid, Integer year, BigDecimal amount) {
        UpdateJoinWrapper<UsagePlanItem> wrapper = JoinWrappers.update(UsagePlanItem.class);
        UsagePlanItem usagePlanItem = new UsagePlanItem();
        usagePlanItem.setCurrAdjust(amount);
        wrapper.leftJoin(UsagePlan.class, UsagePlan::getId, UsagePlanItem::getFundId)
                .eq(UsagePlan::getYear, year)
                .eq(UsagePlan::getIsSnap, CommonConstant.ZeroOrOne.ZERO.value)
                .eq(UsagePlanItem::getProjId, pid);
        int i = usagePlanItemMapper.updateJoin(usagePlanItem, wrapper);
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "双击调整下达");
        return i == 1;
    }

    @Override
    public Boolean cancelAdjust(String pid, Integer year) {
        boolean update = projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getAdjustAmount, null)
                .set(ProjectPlan::getApplyState, CommonConstant.ApplyStateType.BACK.value)
                .update();
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "取消调整下达");
        return update;

    }

    @Override
    public ProjectInfoMainExcel getProjectInfoMainExcel(ProjectManageConditionVo vo) {
        List<ProjectInfoVo> abnormalList = getProjectManageList(vo);
        ProjectInfoMainExcel mainExcel = new ProjectInfoMainExcel();
        List<ProjectInfoExcel> jzList = new ArrayList<>();
        List<ProjectInfoExcel> jcList = new ArrayList<>();
        List<ProjectInfoExcel> xzList = new ArrayList<>();
        ProjectInfoTotalExcel jzTotal = new ProjectInfoTotalExcel();
        ProjectInfoTotalExcel jcTotal = new ProjectInfoTotalExcel();
        ProjectInfoTotalExcel xzTotal = new ProjectInfoTotalExcel();
        ProjectInfoTotalExcel total = new ProjectInfoTotalExcel();
        for (ProjectInfoVo info : abnormalList) {
            ProjectYearRelation currentYearRelation = info.getCurrentYearRelation();

            ProjectInfoExcel projectInfoExcel = new ProjectInfoExcel();
            projectInfoExcel.setName(info.getName());
            ProjectSn projectSn = info.getProjectSn();
            String sn = null;
            if (Objects.nonNull(projectSn)) {
                sn = projectSn.getFormalSn();
                if (StringUtils.isBlank(sn)) {
                    sn = projectSn.getTempSn();
                }
            }
            projectInfoExcel.setSn(sn);
            projectInfoExcel.setAssessOrgname(info.getAssessOrgname());
            projectInfoExcel.setHandler(info.getHandler());
            projectInfoExcel.setEstAmount(Objects.nonNull(info.getEstAmount()) ? bigDecimalToString(info.getEstAmount()) : null);
            String projectStatus;
            Integer state = info.getState();
            if (state == 0) {
                projectStatus = "未上报";
            } else if (StringUtils.isBlank(info.getBasketName())) {
                projectStatus = "已上报";
            } else {
                projectStatus = info.getBasketName();
            }

            projectInfoExcel.setProjectStatus(projectStatus);
            projectInfoExcel.setLibAmount(Objects.nonNull(info.getLibAmount()) ? bigDecimalToString(info.getLibAmount()) : null);
            projectInfoExcel.setCheckAmount(Objects.nonNull(info.getCheckAmount()) ? bigDecimalToString(info.getCheckAmount()) : null);
            //todo 待完善
            projectInfoExcel.setHistoricalExecutedAmounts(null);
            //todo 待完善
            projectInfoExcel.setCurrYearExecutedAmounts(null);
            if (Objects.nonNull(projectInfoExcel.getHistoricalExecutedAmounts()) || Objects.nonNull(projectInfoExcel.getCurrYearExecutedAmounts())) {
                BigDecimal historicalExecutedAmounts = Objects.nonNull(projectInfoExcel.getHistoricalExecutedAmounts()) ? new BigDecimal(projectInfoExcel.getHistoricalExecutedAmounts()) : BigDecimal.ZERO;
                BigDecimal currYearExecutedAmounts = Objects.nonNull(projectInfoExcel.getCurrYearExecutedAmounts()) ? new BigDecimal(projectInfoExcel.getCurrYearExecutedAmounts()) : BigDecimal.ZERO;
                projectInfoExcel.setExecutedAmountsTotal(bigDecimalToString(historicalExecutedAmounts.add(currYearExecutedAmounts)));
            } else {
                projectInfoExcel.setExecutedAmountsTotal(null);
            }
            ProjectPlan projectPlan = info.getFundPlan();
            if (Objects.nonNull(projectPlan)) {
                if (CommonConstant.StateType.BACK.value.equals(projectPlan.getPlanState()) || CommonConstant.StateType.DRAFT.value.equals(projectPlan.getPlanState())) {
                    projectInfoExcel.setDeclareAmount("未上报");
                } else {
                    projectInfoExcel.setDeclareAmount(bigDecimalToString(projectPlan.getDeclareAmount()));
                }
                //获取下达计划。第一查看是否已经下达计划，是取下达计划的金额，否再查看用款计划表的当年初步使用计划或当年正式计划；如果均不存在，则为null
                UsagePlanItem usagePlanItem = info.getUsagePlanItem();
                BigDecimal formalAmount = projectPlan.getFormalAmount();
                if (Objects.isNull(formalAmount) && Objects.nonNull(usagePlanItem) && (Objects.nonNull(usagePlanItem.getCurrFormal()) || Objects.nonNull(usagePlanItem.getCurrAdjust()))) {
                    formalAmount = usagePlanItem.getCurrFormal();
                    projectInfoExcel.setIsFormalFlag("1");
                }
                projectInfoExcel.setFormalAmount(Objects.nonNull(formalAmount) ? bigDecimalToString(formalAmount) : null);
                //获取调整计划。第一查看是否已经调整计划，是取调整计划的金额，否取下达计划的金额
                Integer applyState = projectPlan.getApplyState();
                if (CommonConstant.ApplyStateType.ESCALATED.value.equals(applyState) || CommonConstant.ApplyStateType.CONFIRM.value.equals(applyState)) {
                    projectInfoExcel.setApplyAdjust(Objects.nonNull(projectPlan.getApplyAdjust()) ? bigDecimalToString(projectPlan.getApplyAdjust()) : null);
                } else if (Objects.nonNull(projectPlan.getFormalAmount())) {
                    projectInfoExcel.setApplyAdjust(bigDecimalToString(projectPlan.getFormalAmount()));
                    projectInfoExcel.setIsApplyAdjustFlag("2");
                } else {
                    projectInfoExcel.setApplyAdjust(null);
                }
                BigDecimal adjustAmount = projectPlan.getAdjustAmount();
                if (Objects.isNull(adjustAmount) && Objects.nonNull(usagePlanItem) && Objects.nonNull(usagePlanItem.getCurrAdjust())) {
                    adjustAmount = usagePlanItem.getCurrAdjust();
                    projectInfoExcel.setIsAdjustAmountFlag("1");
                } else if (Objects.isNull(adjustAmount) && Objects.nonNull(projectPlan.getFormalAmount())) {
                    adjustAmount = projectPlan.getFormalAmount();
                    projectInfoExcel.setIsAdjustAmountFlag("2");
                }
                projectInfoExcel.setAdjustAmount(Objects.nonNull(adjustAmount) ? bigDecimalToString(adjustAmount) : null);
            } else {
                projectInfoExcel.setDeclareAmount("未上报");
            }
            //todo 待完善
            projectInfoExcel.setRemark(null);
            if (CommonConstant.ProjectType.CARRYOVER_PROJECT.value.equals(currentYearRelation.getProjType())) {
                computeTotal(jzList, jzTotal, projectInfoExcel);
            } else if (CommonConstant.ProjectType.RECURRENT_PROJECT.value.equals(currentYearRelation.getProjType())) {
                computeTotal(jcList, jcTotal, projectInfoExcel);
            } else if (CommonConstant.ProjectType.NEW_PROJECT.value.equals(currentYearRelation.getProjType())) {
                computeTotal(xzList, xzTotal, projectInfoExcel);
            }
        }
        jzTotal.setFormalAmountTotal(jzTotal.getFormalAmount1() + "\n" + jzTotal.getFormalAmount2());
        jzTotal.setAdjustAmountTotal(jzTotal.getAdjustAmount1() + "\n" + jzTotal.getAdjustAmount2());
        jcTotal.setFormalAmountTotal(jcTotal.getFormalAmount1() + "\n" + jcTotal.getFormalAmount2());
        jcTotal.setAdjustAmountTotal(jcTotal.getAdjustAmount1() + "\n" + jcTotal.getAdjustAmount2());
        xzTotal.setFormalAmountTotal(xzTotal.getFormalAmount1() + "\n" + xzTotal.getFormalAmount2());
        xzTotal.setAdjustAmountTotal(xzTotal.getAdjustAmount1() + "\n" + xzTotal.getAdjustAmount2());

        //计算总合计
        List<ProjectInfoTotalExcel> list = Arrays.asList(jzTotal, jcTotal, xzTotal);
        total.setEstAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getEstAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setLibAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getLibAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setCheckAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getCheckAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setHistoricalExecutedAmountsTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getHistoricalExecutedAmountsTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setCurrYearExecutedAmountsTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getCurrYearExecutedAmountsTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setExecutedAmountsTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getExecutedAmountsTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setDeclareAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getDeclareAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setFormalAmount1(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getFormalAmount1())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setFormalAmount2(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getFormalAmount2())).reduce(BigDecimal.ZERO, BigDecimal::add)));
//        total.setFormalAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getFormalAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setApplyAdjustTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getApplyAdjustTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
//        total.setAdjustAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getAdjustAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setAdjustAmount1(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getAdjustAmount1())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setAdjustAmount2(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getAdjustAmount2())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setFormalAmountTotal(total.getFormalAmount1() + "\n" + total.getFormalAmount2());
        total.setAdjustAmountTotal(total.getAdjustAmount1() + "\n" + total.getAdjustAmount2());

        mainExcel.setYear(vo.getYear())
                .setJzList(jzList)
                .setJzTotal(jzTotal)
                .setJzCount(jzList.size())
                .setJcList(jcList)
                .setJcTotal(jcTotal)
                .setJcCount(jcList.size())
                .setXzList(xzList)
                .setXzTotal(xzTotal)
                .setHjCount(jzList.size() + jcList.size() + xzList.size())
                .setXzCount(xzList.size())
                .setTotal(total);

        return mainExcel;
    }

    /**
     * 计算不同类别的合计以及存储
     *
     * @param infoList         存储项目信息excel对象列表
     * @param infoTotal        存储项目信息excel对象合计对象
     * @param projectInfoExcel 单个项目信息excel对象
     */
    private void computeTotal(List<ProjectInfoExcel> infoList, ProjectInfoTotalExcel infoTotal, ProjectInfoExcel projectInfoExcel) {
        projectInfoExcel.setIndex(infoList.size() + 1);
        infoList.add(projectInfoExcel);

        if (Objects.nonNull(projectInfoExcel.getEstAmount())) {
            infoTotal.setEstAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getEstAmountTotal()).add(new BigDecimal(projectInfoExcel.getEstAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getLibAmount())) {
            infoTotal.setLibAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getLibAmountTotal()).add(new BigDecimal(projectInfoExcel.getLibAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getCheckAmount())) {
            infoTotal.setCheckAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getCheckAmountTotal()).add(new BigDecimal(projectInfoExcel.getCheckAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getHistoricalExecutedAmounts())) {
            infoTotal.setHistoricalExecutedAmountsTotal(bigDecimalToString(new BigDecimal(infoTotal.getHistoricalExecutedAmountsTotal()).add(new BigDecimal(projectInfoExcel.getHistoricalExecutedAmounts()))));
        }

        if (Objects.nonNull(projectInfoExcel.getCurrYearExecutedAmounts())) {
            infoTotal.setCurrYearExecutedAmountsTotal(bigDecimalToString(new BigDecimal(infoTotal.getCurrYearExecutedAmountsTotal()).add(new BigDecimal(projectInfoExcel.getCurrYearExecutedAmounts()))));
        }
        infoTotal.setExecutedAmountsTotal(bigDecimalToString(new BigDecimal(infoTotal.getHistoricalExecutedAmountsTotal()).add(new BigDecimal(infoTotal.getCurrYearExecutedAmountsTotal()))));

        if (Objects.nonNull(projectInfoExcel.getDeclareAmount()) && !"未上报".equals(projectInfoExcel.getDeclareAmount())) {
            infoTotal.setDeclareAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getDeclareAmountTotal()).add(new BigDecimal(projectInfoExcel.getDeclareAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getFormalAmount())) {
            if (!CommonConstant.ZeroOrOne.ONE.value.equals(projectInfoExcel.getIsFormalFlag())) {
                infoTotal.setFormalAmount1(bigDecimalToString(new BigDecimal(infoTotal.getFormalAmount1()).add(new BigDecimal(projectInfoExcel.getFormalAmount()))));
            }
            infoTotal.setFormalAmount2(bigDecimalToString(new BigDecimal(infoTotal.getFormalAmount2()).add(new BigDecimal(projectInfoExcel.getFormalAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getApplyAdjust())) {
            infoTotal.setApplyAdjustTotal(bigDecimalToString(new BigDecimal(infoTotal.getApplyAdjustTotal()).add(new BigDecimal(projectInfoExcel.getApplyAdjust()))));
        }
        if (Objects.nonNull(projectInfoExcel.getAdjustAmount())) {
            if (!CommonConstant.ZeroOrOne.ONE.value.equals(projectInfoExcel.getIsAdjustAmountFlag())) {
                infoTotal.setAdjustAmount1(bigDecimalToString(new BigDecimal(infoTotal.getAdjustAmount1()).add(new BigDecimal(projectInfoExcel.getAdjustAmount()))));
            }
            infoTotal.setAdjustAmount2(bigDecimalToString(new BigDecimal(infoTotal.getAdjustAmount2()).add(new BigDecimal(projectInfoExcel.getAdjustAmount()))));
        }
    }

    /**
     * 生成正式项目编号
     *
     * @param pid  项目id
     * @param year 计划年份
     */
    private void generateFormalSn(String pid, Integer year) {
        ProjectSn projectSn = projectSnService.lambdaQuery().eq(ProjectSn::getProjId, pid).one();
        if (StringUtils.isBlank(projectSn.getFormalSn())) {
            ProjectInfo projectInfo = this.getById(pid);
            List<ProjectSn> list = projectSnService.lambdaQuery()
                    .eq(ProjectSn::getFormalYear, year)
                    .list();
            int formalSeqId = list.size() + 1;
            SysDept sysDept = sysDeptService.selectDeptById(SecurityUtils.getDeptId());
            String dSeqNo = Objects.nonNull(sysDept.getSeqNo()) ? sysDept.getSeqNo() : "0";
            String formalSn;
            if (String.valueOf(formalSeqId).length() < 3) {
                formalSn = String.format("%d-%s-%s-%s-%03d", year, dSeqNo, projectInfo.getTypeId(), projectInfo.getNatureCode(), formalSeqId);
            } else {
                formalSn = String.format("%d-%s-%s-%s-%d", year, dSeqNo, projectInfo.getTypeId(), projectInfo.getNatureCode(), formalSeqId);
            }
            projectSnService.lambdaUpdate()
                    .eq(ProjectSn::getProjId, pid)
                    .set(ProjectSn::getFormalYear, year)
                    .set(ProjectSn::getFormalSeqid, formalSeqId)
                    .set(ProjectSn::getFormalSn, formalSn)
                    .set(ProjectSn::getFormalTime, new Date())
                    .update();

        }
    }

    /**
     * 发布项目操作事件
     *
     * @param pid         项目id（必填）
     * @param operateType 操作类型
     */
    private void publishOperateEvent(String pid, String operateType) {
        OperateEvent event = new OperateEvent()
                .setProjId(pid)
                .setType(CommonConstant.ProjectOrPlan.PROJECT)
                .setOperateType(operateType);
        SpringUtil.getApplicationContext().publishEvent(event);
    }

    private String bigDecimalToString(BigDecimal val) {
        return val.stripTrailingZeros().toPlainString();
    }
}
