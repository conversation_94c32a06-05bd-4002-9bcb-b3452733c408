package com.ruoyi.system.service;

import cn.hutool.core.lang.generator.UUIDGenerator;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SmsService {

    @Value("${sms.service.username}")
    private String username;
    @Value("${sms.service.password}")
    private String password;
    @Value("${sms.service.url}")
    private String url;

    @Async
    @SneakyThrows
    public void sendSms(String phones, String msg) {
        if (StrUtil.isBlank(phones) || StrUtil.isBlank(msg)){
            return;
        }

        String json = "{" +
                "    \"dataStructure\": {" +
                "        \"identifier\": \"sms\"," +
                "        \"displayName\": \"短信\"," +
                "        \"explanatoryComment\": \"描述发送短信的数据结构\"," +
                "        \"dataUnit\": [" +
                "            {" +
                "                \"iDName\": \"id\"," +
                "                \"displayName\": \"标识\"," +
                "                \"definition\": \"唯一标识\"," +
                "                \"datatype\": \"String\"," +
                "                \"maximumSize\": \"32\"," +
                "                \"minimumSize\": \"32\"," +
                "                \"scale\": \"1\"" +
                "            }," +
                "            {" +
                "                \"iDName\": \"content\"," +
                "                \"displayName\": \"内容\"," +
                "                \"definition\": \"发送的内容\"," +
                "                \"datatype\": \"String\"," +
                "                \"maximumSize\": \"65535\"," +
                "                \"minimumSize\": \"1\"," +
                "                \"scale\": \"1\"" +
                "            }," +
                "            {" +
                "                \"iDName\": \"smsTo\"," +
                "                \"displayName\": \"接收人\"," +
                "                \"definition\": \"接收人手机号码,多个接收人以英文逗号隔开\"," +
                "                \"datatype\": \"String\"," +
                "                \"maximumSize\": \"65535\"," +
                "                \"minimumSize\": \"11\"," +
                "                \"scale\": \"1\"" +
                "            }" +
                "        ]" +
                "    }," +
                "    \"dataSet\": {" +
                "        \"recordData\": [" +
                "            {" +
                "                \"dataRecord\": [" +
                "                    {" +
                "                        \"dataUnit\": [" +
                "                            {" +
                "                                \"unitIDName\": \"id\"," +
                "                                \"unitDisplayName\": \"唯一标识\"," +
                "                                \"unitValue\": \"" + new UUIDGenerator().next().replaceAll("-", "") + "\"" +
                "                            }," +
                "                            {" +
                "                                \"unitIDName\": \"content\"," +
                "                                \"unitDisplayName\": \"内容\"," +
                "                                \"unitValue\": \"" + msg + "\"" +
                "                            }," +
                "                            {" +
                "                                \"unitIDName\": \"smsTo\"," +
                "                                \"unitDisplayName\": \"接收人\"," +
                "                                \"unitValue\": \"" + phones + "\"" +
                "                            }" +
                "                        ]" +
                "                    }" +
                "                ]" +
                "            }" +
                "        ]" +
                "    }" +
                "}";

        String result2 = HttpRequest.post(url)
                .header("username", username)
                .header("password", password)
                .timeout(20000)
                .body(json)
                .execute()
                .body();
        log.info("短信发送：" + result2);
    }
}
