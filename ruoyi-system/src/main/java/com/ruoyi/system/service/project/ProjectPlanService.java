package com.ruoyi.system.service.project;

import com.ruoyi.system.domain.project.ProjectPlan;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 针对表【project_plan(资金计划表)】的数据库操作Service
 * @date 2025-06-03 14:52:19
 */
public interface ProjectPlanService extends IService<ProjectPlan> {

    /**
     * 获取项目资金计划信息
     *
     * @param pid 项目id
     * @param year 年
     * @return 项目资金计划信息
     */
    ProjectPlan getPlanInfo(String pid, Integer year);


    /**
     * 上报项目资金计划
     *
     * @param pid 项目id
     * @param year 年
     * @return 上报结果
     */
    Boolean reportPlan(String pid,  Integer year);

    /**
     * 撤回项目资金计划
     *
     * @param pid 项目id
     * @param year 年
     * @return 撤回结果
     */
    Boolean withdrawPlan(String pid,  Integer year);

    /**
     * 更新项目资金计划中的年初计划金额
     *
     * @param pid 项目id
     * @param year 年
     * @param earlyAmount 金额
     * @return 更新结果
     */
    Boolean updateEarlyAmount(String pid,  Integer year, BigDecimal earlyAmount);
}
