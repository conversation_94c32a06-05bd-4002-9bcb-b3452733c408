package com.ruoyi.system.service.impl.project;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.CommonConstant;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.event.OperateEvent;
import com.ruoyi.system.domain.project.ProjectPlan;
import com.ruoyi.system.service.project.ProjectPlanService;
import com.ruoyi.system.mapper.project.ProjectPlanMapper;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 针对表【project_plan(资金计划表)】的数据库操作Service实现
 * @date 2025-06-03 14:52:19
 */
@Service
public class ProjectPlanServiceImpl extends ServiceImpl<ProjectPlanMapper, ProjectPlan> implements ProjectPlanService {

    @Override
    public ProjectPlan getPlanInfo(String pid,  Integer year) {
        return this.lambdaQuery()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .one();
    }

    @Override
    public Boolean reportPlan(String pid,  Integer year) {
        boolean update = this.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getDeclareUserid, SecurityUtils.getUserId())
                .set(ProjectPlan::getDeclareTime, new Date())
                .set(ProjectPlan::getPlanState, CommonConstant.StateType.ESCALATED.value)
                .update();
        this.publishOperateEvent(pid, year, "上报计划");
        return update;
    }

    @Override
    public Boolean withdrawPlan(String pid, Integer year) {
        boolean update = this.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getDeclareUserid, null)
                .set(ProjectPlan::getDeclareTime, null)
                .set(ProjectPlan::getPlanState, CommonConstant.StateType.DRAFT.value)
                .update();
        this.publishOperateEvent(pid, year, "撤回计划");
        return update;
    }

    @Override
    public Boolean updateEarlyAmount(String pid, Integer year, BigDecimal earlyAmount) {
        boolean update = this.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getEarlyAmount, earlyAmount)
                .update();
        this.publishOperateEvent(pid, year, "修改年初计划金额");
        return update;
    }

    /**
     * 发布计划操作事件
     *
     * @param pid         项目id（必填）
     * @param year        年度
     * @param operateType 操作类型
     */
    private void publishOperateEvent(String pid, Integer year, String operateType) {
        OperateEvent event = new OperateEvent()
                .setProjId(pid)
                .setType(CommonConstant.ProjectOrPlan.PLAN)
                .setOperateType(operateType)
                .setYear(year);
        SpringUtil.getApplicationContext().publishEvent(event);
    }
}




