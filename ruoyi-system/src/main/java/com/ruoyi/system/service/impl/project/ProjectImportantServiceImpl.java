package com.ruoyi.system.service.impl.project;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.constant.CommonConstant;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.dto.ProjectPerformanceDto;
import com.ruoyi.system.domain.project.*;
import com.ruoyi.system.domain.vo.project.*;
import com.ruoyi.system.mapper.project.ProjectInfoMapper;
import com.ruoyi.system.service.project.ProjectImportantService;
import com.ruoyi.system.mapper.project.ProjectImportantMapper;
import com.ruoyi.system.service.project.ProjectInfoService;
import com.ruoyi.system.service.project.ProjectPerformanceService;
import com.ruoyi.system.service.project.ProjectYearRelationService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【project_important(重点项目清单表)】的数据库操作Service实现
* @date 2025-06-03 14:43:10
*/
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy, @Autowired})
public class ProjectImportantServiceImpl extends ServiceImpl<ProjectImportantMapper, ProjectImportant> implements ProjectImportantService{


    private final ProjectImportantService projectImportantService;

    private final ProjectInfoService projectInfoService;

    private final ProjectYearRelationService projectYearRelationService;


    @Override
    public List<ProjectImportantVo> getNormalList(ProjectImportantConditionVo vo) {
        List<ProjectImportantVo> list = new ArrayList<>();
        List<ProjectImportant> projectImportant = projectImportantService.lambdaQuery()
                .eq(ProjectImportant::getYear, vo.getYear())
                .apply(StringUtils.isNotBlank(vo.getDeptId()),"EXISTS (SELECT 1 FROM project_info pr WHERE pr.id = project_important.proj_id AND pr.assess_orgid = {0})", vo.getDeptId())
                .list();
        return getProjectImportantVos(vo, list, projectImportant);
    }
   /**
     * 获取重点项目清单详情
     * @param relId 关联id
     * @return
    */
    @Override
    public ProjectImportantVo getImportProjDetail(String relId) {
        Integer year=projectYearRelationService.getById(relId).getYear();
        String projId = projectYearRelationService.getById(relId).getProjId();
        if(projectImportantService.lambdaQuery().eq(ProjectImportant::getYear,year).eq(ProjectImportant::getProjId,projId).exists()){
            ProjectImportantVo projectImportantVo = BeanUtil.copyProperties(projectImportantService.lambdaQuery()
                    .eq(ProjectImportant::getYear,year)
                    .eq(ProjectImportant::getProjId,projId)
                    .one(), ProjectImportantVo.class);
            projectImportantVo.setProjName(projectInfoService.lambdaQuery().eq(ProjectInfo::getId, projId).one().getName());
            projectImportantVo.setRelId(relId);
           return projectImportantVo;
        }
        ProjectImportantVo projectImportantVo = new ProjectImportantVo();
        projectImportantVo.setYear(year);
        projectImportantVo.setProjId(projId);
        projectImportantVo.setRelId(relId);
        projectImportantVo.setProjName(projectInfoService.lambdaQuery().eq(ProjectInfo::getId, projId).one().getName());
        return projectImportantVo;
    }

    private List<ProjectImportantVo> getProjectImportantVos(ProjectImportantConditionVo vo, List<ProjectImportantVo> list, List<ProjectImportant> projectImportants) {
        projectImportants.forEach(e -> {
            ProjectImportantVo projectImportantVo = BeanUtil.copyProperties(e, ProjectImportantVo.class);
            list.add(projectImportantVo);
        });
        return list;
    }

    /**
     * 重点项目清单列表的实现
     * @param vo 查询条件
     * @return
     */
    @Override
    public List<ProjectImportantVo> getList(ProjectImportantConditionVo vo) {
        List<ProjectImportantVo> projectImportantVos = new ArrayList<>();
        List<ProjectImpVo> list=this.getBaseMapper().getReprortProjectImportant(vo);

        list.forEach(e -> {
            ProjectImportantVo projectImportantVo = BeanUtil.copyProperties(e, ProjectImportantVo.class);
            projectImportantVo.setProjName(e.getDeptName()); //显示部门名称
            projectImportantVo.setProjAmount(null);   //部门项目金额为空，金额移到 重点项目推进小计
            projectImportantVo.setYearPlan(new BigDecimal(0.00));   //正式计划
            projectImportantVo.setYearExecuted(new BigDecimal(0.00));//已执行
            projectImportantVos.add(projectImportantVo);  // 第一条记录为部门名称 例如 发展公司
            ProjectImportantVo projectImportantVo1 = BeanUtil.copyProperties(e, ProjectImportantVo.class);
            projectImportantVo1.setProjName("其中重点推进项目小计"); //项目名称为 "其中重点推进项目小计"
            projectImportantVo1.setYearPlan(new BigDecimal(400.00));   //正式计划
            projectImportantVo1.setYearExecuted(new BigDecimal(400.00));//已执行
            projectImportantVos.add(projectImportantVo1);    // 第二条记录为"其中重点推进项目小计"
            ProjectImportantConditionVo vo1=new ProjectImportantConditionVo();
            vo1.setYear(e.getYear());
            vo1.setDeptId(e.getDeptId());
            List<ProjectImpOutVo> outlist=this.getBaseMapper().getReportProjectImportantOut(vo1);
            if(outlist!=null&&outlist.size()>0){
                //未保存重点项目表的记录
                outlist.forEach(e3 -> {
                    ProjectImportantVo projectImportantVo4 = BeanUtil.copyProperties(e3, ProjectImportantVo.class);
                    projectImportantVos.add(projectImportantVo4);
                });
            }
            List<ProjectImpStepVo> steplist=this.getBaseMapper().getReportProjectImportantStep(vo1);
            if(steplist!=null&&steplist.size()>0){
                steplist.forEach(e1 -> {
                    ProjectImportantVo projectImportantVo2 = BeanUtil.copyProperties(e1, ProjectImportantVo.class);
                    projectImportantVo2.setProjName(e1.getStepName()); //显示阶段性名称
                    projectImportantVo2.setDeptId(projectImportantVo1.getDeptId());
                    projectImportantVo2.setYearPlan(new BigDecimal(3.00));   //正式计划
                    projectImportantVo2.setYearExecuted(new BigDecimal(3.00));//已执行
                    projectImportantVos.add(projectImportantVo2);  //第三条记录为阶段性名称
                    //开始重点项目表记录分阶段查询
                    List<ProjectImportant> projectImportants = projectImportantService.lambdaQuery()
                            .eq(ProjectImportant::getYear, projectImportantVo2.getYear())
                            .eq(ProjectImportant::getStepNo, projectImportantVo2.getStepNo())
                            .apply(StringUtils.isNotBlank(projectImportantVo2.getDeptId()),"EXISTS (SELECT 1 FROM project_info pr WHERE pr.id = project_important.proj_id AND pr.assess_orgid = {0})", projectImportantVo2.getDeptId())
                            .list();
                    projectImportants.forEach(e2 -> {
                        ProjectImportantVo projectImportantVo3 = BeanUtil.copyProperties(e2, ProjectImportantVo.class);
                        projectImportantVo3.setProjName(projectInfoService.lambdaQuery().eq(ProjectInfo::getId, e2.getProjId()).one().getName()); //项目名称
                        projectImportantVo3.setDeptId(projectImportantVo2.getDeptId());
                        projectImportantVo3.setProjAmount(projectInfoService.lambdaQuery().eq(ProjectInfo::getId, e2.getProjId()).one().getContractAmount());
                        projectImportantVo3.setPlanBegin(projectInfoService.lambdaQuery().eq(ProjectInfo::getId, e2.getProjId()).one().getBuildBegin()); //计划开工
                        projectImportantVo3.setProjEnd(projectInfoService.lambdaQuery().eq(ProjectInfo::getId, e2.getProjId()).one().getBuildEnd()); //计划完工
                        projectImportantVo3.setRelId(projectYearRelationService.lambdaQuery().eq(ProjectYearRelation::getYear,e2.getYear()).eq(ProjectYearRelation::getProjId,e2.getProjId()).one().getId());
                        //月度计划
                        if(StringUtils.isNotBlank(projectImportantVo3.getMonthPlan())){
                            ObjectMapper objectMapper = new ObjectMapper();
                            List<ProjectImportantJhVo> Vos = null;
                            try {
                                Vos = objectMapper.readValue(projectImportantVo3.getMonthPlan(), new TypeReference<List<ProjectImportantJhVo>>() {});

                            } catch (JsonProcessingException jsonProcessingException) {
                                jsonProcessingException.printStackTrace();
                            }
                            Collections.sort(Vos, (o1, o2) -> o1.getMonth().compareTo(o2.getMonth()));
                            projectImportantVo3.setProjectImportantJhVos(Vos);
                        }

                        //月度实施计划
                        if(StringUtils.isNotBlank(projectImportantVo3.getMonthAction())){
                            ObjectMapper objectMapper1 = new ObjectMapper();
                            List<ProjectImportantExceVo> eVos = null;
                            try {
                                eVos = objectMapper1.readValue(projectImportantVo3.getMonthAction(), new TypeReference<List<ProjectImportantExceVo>>() {});
                            } catch (JsonProcessingException jsonProcessingException) {
                                jsonProcessingException.printStackTrace();
                            }
                            Collections.sort(eVos, (o1, o2) -> o1.getMonth().compareTo(o2.getMonth()));
                            projectImportantVo3.setProjectImportantExceVos(eVos);
                        }
                        projectImportantVos.add(projectImportantVo3);  //第4条记录为 重点项目各个名称
                    });
                });
            }

        });
        return projectImportantVos;
    }

    /**
     * 项目整理 部门清单
     * @param vo
     * @return
     */
    @Override
    public List<ProjectImpVo> getDeptList(ProjectImportantConditionVo vo) {
        return this.getBaseMapper().getReprortProjectImportant(vo);
    }

    /**
     * 项目整理-》项目列表
     * @param vo 查询条件
     * @return
     */
    @Override
    public List<ProjectImpInputVo> getPiList(ProjectImportantConditionVo vo) {

        return this.getBaseMapper().getReportProjectImportantInput(vo);
    }

    /**
     * 项目整理 阶段清单
     * @param vo
     * @return
     */
    @Override
    public List<ProjectImpStepVo> geStepList(ProjectImportantConditionVo vo) {
        return this.getBaseMapper().getReportProjectImportantStep1(vo);
    }
    /**
     * 月度计划列表-》项目列表
     * @param vo 查询条件
     * @return
     */
    @Override
    public List<ProjectImpInputVo> getJhList(ProjectImportantConditionVo vo) {
        List<ProjectImpInputVo> vos=this.getBaseMapper().getReportProjectImportantInput(vo);
        List<ProjectImpInputVo> projectImpInputVos = new ArrayList<>();
        vos.forEach(e -> {
            ProjectImpInputVo projectImpInputVo = BeanUtil.copyProperties(e, ProjectImpInputVo.class);
            if(StringUtils.isNotBlank(projectImpInputVo.getMonthPlan())){
                ObjectMapper objectMapper = new ObjectMapper();
                List<ProjectImportantJhVo> Vos = null;
                try {
                    Vos = objectMapper.readValue(projectImpInputVo.getMonthPlan(), new TypeReference<List<ProjectImportantJhVo>>() {});
                } catch (JsonProcessingException jsonProcessingException) {
                    jsonProcessingException.printStackTrace();
                }
                projectImpInputVo.setProjectImportantJhVos(Vos);
            }else{
                List<ProjectImportantJhVo> defaultList=new ArrayList<>();
                for (int i = 1; i <= 12; i++) {
                    defaultList.add(new ProjectImportantJhVo(i, ""));
                }
                projectImpInputVo.setProjectImportantJhVos(defaultList);
            }
            projectImpInputVos.add(projectImpInputVo);
        });
        return projectImpInputVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdatePinput(List<ProjectImpInputVo>  projectImpInputVos) {
        if (projectImpInputVos != null && !projectImpInputVos.isEmpty()) {
            for (ProjectImpInputVo entity : projectImpInputVos) { //
                if(StringUtils.isNotBlank(entity.getId())){
                    ProjectImportant projectImportant = projectImportantService.getById(entity.getId());
                    projectImportant.setProjCode(entity.getProjCode());
                    projectImportant.setStepNo(entity.getStepNo());
                    projectImportant.setStepName(entity.getStepName());
                    this.saveOrUpdate(projectImportant);
                }else{
                    ProjectImportant projectImportant = BeanUtil.copyProperties(entity, ProjectImportant.class);
                    this.saveOrUpdate(projectImportant);
                }

            }
        }
        return true;
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateJhinput(List<ProjectImpInputVo>  projectImpInputVos) throws JsonProcessingException {
        if (projectImpInputVos != null && !projectImpInputVos.isEmpty()) {
            for (ProjectImpInputVo entity : projectImpInputVos) { //
                if(StringUtils.isNotBlank(entity.getId())){
                    ProjectImportant projectImportant = projectImportantService.getById(entity.getId());
                    projectImportant.setYearGoal(entity.getYearGoal());
                    ObjectMapper objectMapper = new ObjectMapper();
                    String jsonString = objectMapper.writeValueAsString(entity.getProjectImportantJhVos());
                    projectImportant.setMonthPlan(jsonString);
                    List<ProjectImportantExceVo> defaultList=new ArrayList<>();
                    for (int i = 1; i <= 12; i++) {
                        defaultList.add(new ProjectImportantExceVo(i, ""));
                    }
                    String json = objectMapper.writeValueAsString(defaultList);
                    projectImportant.setMonthAction(json);
                    this.saveOrUpdate(projectImportant);
                }else{
                    ProjectImportant projectImportant = BeanUtil.copyProperties(entity, ProjectImportant.class);
                    ObjectMapper objectMapper = new ObjectMapper();
                    String jsonString = objectMapper.writeValueAsString(entity.getProjectImportantJhVos());
                    projectImportant.setMonthPlan(jsonString);
                    List<ProjectImportantExceVo> defaultList=new ArrayList<>();
                    for (int i = 1; i <= 12; i++) {
                        defaultList.add(new ProjectImportantExceVo(i, ""));
                    }
                    String json = objectMapper.writeValueAsString(defaultList);
                    projectImportant.setMonthAction(json);
                    this.saveOrUpdate(projectImportant);
                }

            }
        }
        return true;
    }

    @Override
    public List<Integer> getYear(){
        return this.getBaseMapper().getDistinctYears();
    }
}




