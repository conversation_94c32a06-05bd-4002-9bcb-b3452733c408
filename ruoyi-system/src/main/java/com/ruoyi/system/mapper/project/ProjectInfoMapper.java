package com.ruoyi.system.mapper.project;

import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.system.domain.project.ProjectInfo;
import com.ruoyi.system.domain.vo.project.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【project_info(项目信息表;)】的数据库操作Mapper
* @Date 2025-06-03 15:28:40
*/
public interface ProjectInfoMapper extends MPJBaseMapper<ProjectInfo> {

    List<ProjectInfoVo> getProjectManageList(ProjectManageConditionVo vo);

    List<ProjectInfoCopyVo> getReproducibleProject(ProjectQueryConditionVo vo);


    List<ProjectInfoOfPlanVo> getProjectInfoOfPlan(ProjectQueryOfUsagePlanConditionVo vo);

    List<ProjectInfoVo> getProjectDetailForUsagePlan(@Param("pIds") List<String> pIds , @Param("year") Integer year);
}




