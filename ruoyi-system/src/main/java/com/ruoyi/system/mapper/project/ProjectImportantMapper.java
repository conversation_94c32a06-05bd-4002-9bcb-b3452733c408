package com.ruoyi.system.mapper.project;

import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.system.domain.project.ProjectImportant;
import com.ruoyi.system.domain.vo.project.*;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【project_important(重点项目清单表)】的数据库操作Mapper
* Date 2025-06-03 14:43:10
*/
public interface ProjectImportantMapper extends MPJBaseMapper<ProjectImportant> {
    /**
     * 查询重点项目列表 按部门名称group by deptId
     * @param vo
     * @return
     */
    List<ProjectImpVo> getReprortProjectImportant(ProjectImportantConditionVo vo);

    /**
     * 查询重点项目列表 按阶段名称group by stepNo
     * @param vo
     * @return
     */
    List<ProjectImpStepVo> getReportProjectImportantStep(ProjectImportantConditionVo vo);

    List<ProjectImpStepVo> getReportProjectImportantStep1(ProjectImportantConditionVo vo);

    /**
     * 查询重点项目列表 未保在重点项目表内的记录
     * @param vo
     * @return
     */
    List<ProjectImpOutVo> getReportProjectImportantOut(ProjectImportantConditionVo vo);
    /**
     * 查询重点项目  项目整理和计划管理的输入视图
     * @param vo
     * @return
     */
    List<ProjectImpInputVo> getReportProjectImportantInput(ProjectImportantConditionVo vo);

    List<Integer> getDistinctYears();

}




