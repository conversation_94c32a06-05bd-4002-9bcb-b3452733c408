package com.ruoyi.system.domain.project;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;
import lombok.Data;

/**
 * 重点项目清单表
 * <AUTHOR>
 * @TableName project_important
 */
@TableName(value ="project_important")
@Data
public class ProjectImportant {
    /**
     * 主键;主键
     */
    @TableId(value = "id")
    private String id;

    /**
     * 项目年度;项目年度
     */
    private Integer year;

    /**
     * 重点项目ID;重点项目ID
     */
    private String projId;

    /**
     * 项目编号;项目编号（此编号仅用于重点项目）
     */
    @TableField(value = "proj_code")
    private String projCode;

    /**
     * 项目阶段;项目阶段
     */
    private Integer stepNo;

    /**
     * 项目阶段名称;项目阶段（1方案研究阶段、2预算评审及报批阶段、3待开工阶段、4实施阶段、5经常性项目）
     */
    private String stepName;

    /**
     * 排序号;排序号
     */
    private Integer seqNo;

    /**
     * 年度目标;年度目标
     */
    private String yearGoal;

    /**
     * 进展说明及需协调问题;进展说明及需协调问题
     */
    private String progressIssue;

    /**
     * 更新人;更新人，存储用户名
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间;更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 项目月度计划;项目月度计划(json)，[{"month":1,"plan":"dddd"}]
     */
    private String monthPlan;

    /**
     * 项目月度执行;项目月度执行(json)，[{"month":1,"action":"dddd"}]
     */
    private String monthAction;
}