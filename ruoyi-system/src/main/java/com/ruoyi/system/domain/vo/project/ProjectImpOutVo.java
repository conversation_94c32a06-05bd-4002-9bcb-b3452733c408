package com.ruoyi.system.domain.vo.project;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 重点项目清单表视图;
 *
 */
@Data
public class ProjectImpOutVo {

    /**
     * 关联年度项目ID;
     */
    private String relId;
    /**
     * 项目年度;项目年度
     */
    private Integer year;
    /**
     * 项目id
     */
    private String projId;
    /**
     * 项目名称
     */
    private String projName;
    /**
     * 部门ID
     */
    private String deptId;
    /**
     * 项目金额
     */
    private BigDecimal projAmount;
    /**
     * 计划开工
     */
    private Date planBegin;
    /**
     * 计划完工
     */
    private Date projEnd;

}
