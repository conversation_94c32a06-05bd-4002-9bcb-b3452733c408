package com.ruoyi.system.domain.vo.project;


import com.baomidou.mybatisplus.annotation.TableField;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Date;

/**
 * 重点项目清单表视图;
 *
 */
@Data
public class ProjectImportantConditionVo {


    /**
     * 项目年度;项目年度
     */
    private Integer year;

    /**
     * 重点项目ID;重点项目ID
     */
    private String projId;
    /**
     * 项目阶段;项目阶段
     */
    private Integer stepNo;

    /**
     * 项目单位
     */
    private String deptId;


}
