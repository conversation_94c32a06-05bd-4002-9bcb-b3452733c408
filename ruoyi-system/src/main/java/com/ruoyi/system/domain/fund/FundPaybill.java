package com.ruoyi.system.domain.fund;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;
import lombok.Data;

/**
 * 资金拨款单
 *
 * <AUTHOR>
 * @TableName fund_paybill
 */
@TableName(value ="fund_paybill")
@Data
public class FundPaybill {
    /**
     * 主键;主键，确保有序
     */
    @TableId(value = "id")
    private String id;

    /**
     * 所属年度;所属年度
     */
    private Integer year;

    /**
     * 拨款单编号;拨款单编号
     */
    private String serialNumber;

    /**
     * 项目ID;项目ID
     */
    private String projId;

    /**
     * 项目名称;项目名称
     */
    private String projName;

    /**
     * 项目编号;项目编号
     */
    private String projSn;

    /**
     * 项目属性;项目属性（结转项目、新增项目）
     */
    private String projType;

    /**
     * 子项目ID;子项目ID
     */
    private String childId;

    /**
     * 子项目名称;子项目名称
     */
    private String childName;

    /**
     * 项目批准金额;项目批准金额
     */
    private Integer projAmount;

    /**
     * 当年下达用款计划;当年下达用款计划
     */
    private Integer planYear;

    /**
     * 累计下达用款计划;累计下达用款计划
     */
    private Integer planAmount;

    /**
     * 支出分类;支出分类
     */
    private Integer payTypeid;

    /**
     * 支出分类名称;支出分类名称
     */
    private String payTypename;

    /**
     * 支出用途;支出用途
     */
    private Integer payPurposeid;

    /**
     * 支出用途名称;支出用途名称
     */
    private String payPurposename;

    /**
     * 项目负责人;项目负责人
     */
    private String leaderName;

    /**
     * 项目负责人电话;项目负责人电话
     */
    private String leaderMobile;

    /**
     * 合同ID;合同ID
     */
    private String contractId;

    /**
     * 合同名称;合同名称
     */
    private String contractName;

    /**
     * 合同金额;合同金额
     */
    private Integer contractAmount;

    /**
     * 申请单位ID;申请单位ID
     */
    private String applyOrgid;

    /**
     * 申请单位名称;申请单位名称
     */
    private String applyOrgname;

    /**
     * 申请人ID;申请人ID
     */
    private String applyUserid;

    /**
     * 申请人名称;申请人名称
     */
    private String applyUsername;

    /**
     * 申请日期;申请日期
     */
    private Date applyDate;

    /**
     * 申请金额;申请金额
     */
    private Integer applyAmount;

    /**
     * 核定金额;核定金额
     */
    private Integer checkAmount;

    /**
     * 申请拨款类型;申请拨款类型（首付款、进度款、结算款）
     */
    private Integer applyType;

    /**
     * 申请理由;申请理由
     */
    private String applyReason;

    /**
     * 财务监理意见;财务监理意见（1是，0否），默认0
     */
    private String hasFinance;

    /**
     * 财务监理附件;财务监理附件
     */
    private String financeFile;

    /**
     * 项目累计拨款金额;项目累计拨款金额
     */
    private Integer projPayed;

    /**
     * 合同累计拨款金额;合同累计拨款金额
     */
    private Integer contractPayed;

    /**
     * 业务处室;业务处室
     */
    private String auditOrgid;

    /**
     * 审核人员;审核人员
     */
    private String auditUserid;

    /**
     * 高质量项目编号;高质量项目编号
     */
    private String hqdpProjcode;

    /**
     * 状态;状态（-1退回，0草稿，1已提交，2审批完成，3已拨付）
     */
    private String status;

    /**
     * 删除标识;删除标识（0否，1是），默认0
     */
    @TableLogic
    private String delFlag;

    /**
     * 创建人;创建人，存储用户名
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间;创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新人;更新人，存储用户名
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间;更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}