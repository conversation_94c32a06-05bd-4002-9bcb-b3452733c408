package com.ruoyi.system.domain.project;

import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;
import java.util.Date;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 项目信息表;
 *
 * <AUTHOR>
 * @TableName project_info
 */
@TableName(value ="project_info")
@Data
public class ProjectInfo {
    /**
     * 主键;主键，确保有序
     */
    @TableId(value = "id")
    private String id;

    /**
     * 项目序号;项目顺序号，递增（历史数据应兼容）
     */
    private Integer seqid;

    /**
     * 项目名称;项目名称
     */
    @NotBlank(message = "项目名称不能为空")
    @Length(max = 80, message = "项目名称不能超过80个字符")
    private String name;

    /**
     * 项目正式名称;项目正式名称，优先于项目名称
     */
    private String formalName;

    /**
     * 申报单位;申报单位ID
     */
    @NotBlank(message = "申报单位ID不能为空")
    private Long applyOrgid;

    /**
     * 申报单位名称;申报单位名称
     */
    private String applyOrgname;

    /**
     * 申报时间;申报单位名称
     */
    private Date applyTime;

    /**
     * 上报单位;上报单位ID
     */
    private Long submitOrgid;

    /**
     * 上报单位名称
     */
    private String submitOrgname;

    /**
     * 上报时间;上报单位名称
     */
    private Date submitTime;

    /**
     * 项目使用单位;使用单位（文本，无ID，因为使用单位可能是项目管理之外的部门）
     */
    private String useOrgname;

    /**
     * 考核单位;考核主体ID
     */
    private Long assessOrgid;

    /**
     * 考核单位名称
     */
    private String assessOrgname;

    /**
     * 项目类型ID;项目类型id（字典表）
     */
    @NotBlank(message = "项目类型ID不能为空")
    private String typeId;

    /**
     * 项目类型名称;项目类型名称
     */
    @NotBlank(message = "项目类型名称不能为空")
    private String typeName;

    /**
     * 项目性质代号;项目性质代号（JC/JD）
     */
    @NotBlank(message = "项目性质代号不能为空")
    private String natureCode;

    /**
     * 项目性质名称;项目性质名称（经常性项目/阶段性项目）
     */
    @NotBlank(message = "项目性质名称不能为空")
    private String natureName;

    /**
     * 项目负责人;项目负责人
     */
    private String leader;

    /**
     * 项目负责人联系电话;项目负责人联系电话（手机）
     */
    private String leaderTel;

    /**
     * 项目经办人;项目经办人
     */
    @NotBlank(message = "项目经办人不能为空")
    private String handler;

    /**
     * 项目经办人联系电话;项目经办人联系电话（手机）
     */
    @NotBlank(message = "项目经办人联系电话不能为空")
    private String handlerTel;

    /**
     * 项目估算金额;项目估算金额，单位：万元（2位小数）
     */
    @NotNull(message = "项目估算金额不能为空")
    private BigDecimal estAmount;

    /**
     * 项目入库金额;项目入库金额，单位：万元（2位小数）
     */
    private BigDecimal libAmount;

    /**
     * 项目核定金额;项目核定金额，单位：万元（2位小数）
     */
    private BigDecimal checkAmount;

    /**
     * 项目合同金额;项目合同金额，单位：万元（2位小数）
     */
    private BigDecimal contractAmount;

    /**
     * 入库标记;入库标记，默认0，已入库为1
     */
    private Integer libState;

    /**
     * 入库意见;入库意见，入库依据存附件表
     */
    private String libReason;

    /**
     * 核定标记;核定标记，默认0，已核定为1
     */
    private Integer checkState;

    /**
     * 核定意见;核定意见，核定依据存附件表
     */
    private String checkReason;

    /**
     * 项目必要性;项目必要性
     */
    private String necessity;

    /**
     * 项目依据;项目依据
     */
    private String basis;

    /**
     * 项目主要内容;项目主要内容
     */
    private String mainCnt;

    /**
     * 实施计划;实施计划
     */
    private String actionPlan;

    /**
     * 建设周期（开始）;建设周期（开始）
     */
    @NotBlank(message = "建设周期（开始）不能为空")
    private Date buildBegin;

    /**
     * 建设周期（结束）;建设周期（结束）
     */
    @NotBlank(message = "建设周期（结束）不能为空")
    private Date buildEnd;

    /**
     * 资金周期（开始）;资金周期（开始）
     */
    private Date fundBegin;

    /**
     * 资金周期（结束）;资金周期（结束）
     */
    private Date fundEnd;

    /**
     * 是否涉及数字化园区支出;是否涉及数字化园区支出（0否，1是）
     */
    private String isSzhyq;

    /**
     * 是否涉及精细化管理;是否涉及精细化管理（0否，1是）
     */
    private String isJxhgl;

    /**
     * 是否涉及政府购买服务;是否涉及政府购买服务（0否，1是）
     */
    private String isZfgm;

    /**
     * 项目总目标（绩效）;项目总目标（绩效）
     */
    private String goal;

    /**
     * 项目篮子ID;项目篮子ID
     */
    private Integer basketCode;

    /**
     * 项目篮子名称;项目篮子名称
     */
    private String basketName;

    /**
     * 关联项目;关联项目（如果是复制的经常性项目，存储被复制的项目ID）
     */
    private String baseProjid;

    /**
     * 计划状态;计划状态（-1退回申报人、0草稿、1已上报、2已受理、3已分送、4已审批、9已发布）
     */
    private Integer state;

    /**
     * 删除标识;删除标识（0否，1是）
     */
    @TableLogic
    private String delFlag;

    /**
     * 创建时间;创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建人;创建人，存储用户名
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间;更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 更新人;更新人，存储用户名
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 配合单位;配合单位，扩展为可支持多个，使用json存储，格式：[{"id":"1000","name":"经发处","seqid":"1"},{"id":"1001","name":"计财处","seqid":"2"}]
     */
    private String cooperateOrgid;

    /**
     * 项目用途;项目用途，支持多选，使用json存储，格式：[{"id":"1000","name":"用途1","seqid":"1"},{"id":"1000","name":"用途2","seqid":"1"}]
     */
    private String purpose;

    /**
     * 分解目标（绩效）;分解目标（绩效），json存储，格式：[{"sort":1,"name":"投入和管理目标",children:[{"sort":1,"name":"dsfdf","value":"dfsdfas"},{"sort":2,"name":"dsfdf","value":"dfsdfas"}]},{"sort":2,"name":"投入和管理目标",children:[{"sort":1,"name":"dsfdf","value":"dfsdfas"},{"sort":2,"name":"dsfdf","value":"dfsdfas"}]}]
     */
    private String goalDetail;
}