package com.ruoyi.system.domain.project;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 高质量项目关系表
 *
 * <AUTHOR>
 * @TableName project_hqdp
 */
@Data
@TableName(value = "project_hqdp")
public class ProjectHqdp implements Serializable {
    /**
     * 主键;主键，确保有序
     */
    @TableId(value = "id")
    private String id;

    /**
     * 年度;年度
     */
    private Integer year;

    /**
     * 单位ID;高质量项目主管部门ID
     */
    private String orgId;

    /**
     * 项目ID;关联项目ID
     */
    private String projId;
}