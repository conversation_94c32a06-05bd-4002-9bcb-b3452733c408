package com.ruoyi.system.domain.project;

import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.ruoyi.system.domain.vo.project.ProjectInfoVo;
import lombok.Data;
import org.apache.ibatis.type.Alias;

/**
 * 用款计划明细表
 *
 * <AUTHOR>
 * @TableName usage_plan_item
 */
@Data
@Alias("UPlanItem")
@TableName(value = "usage_plan_item")
public class UsagePlanItem {
    /**
     * 主键;主键，确保有序
     */
    @TableId(value = "id")
    private String id;

    /**
     * 用款计划表ID;用款计划表ID
     */
    private String fundId;

    /**
     * 数据项类型;数据项类型，1项目，2组
     */
    private Integer itemType;

    /**
     * 组汇总方式;组汇总方式，1自动累加，2自定义
     */
    private Integer sumMode;

    /**
     * 父项ID;父项ID
     */
    private String parentId;

    /**
     * 排序号;排序号，默认1
     */
    private Integer seqNo;

    /**
     * 项目ID;项目ID
     */
    private String projId;

    /**
     * 项目名称(组名称）;项目名称(组名称）
     */
    private String projName;

    /**
     * 上报单位ID;上报单位ID
     */
    private String sbOrgid;

    /**
     * 上报单位名称;上报单位名称
     */
    private String sbOrgname;

    /**
     * 考核主体ID;考核主体ID
     */
    private String assessOrgid;

    /**
     * 考核主体名称;考核主体名称
     */
    private String assessOrgname;

    /**
     * 是否重点项目;是否重点项目（Y是，N否）
     */
    private String isImp;

    /**
     * 重点项目类型;重点项目类型
     */
    private String impName;

    /**
     * 项目篮子ID;项目篮子ID
     */
    private Integer basketId;

    /**
     * 项目篮子名称;项目篮子名称
     */
    private String basketName;

    /**
     * 项目性质;项目性质（阶段性-新增，阶段性-结转，经常性）
     */
    private String natureName;

    /**
     * 项目估算;项目估算
     */
    private BigDecimal estAmount;

    /**
     * 入库金额;入库金额
     */
    private BigDecimal libAmount;

    /**
     * 核定金额;核定金额
     */
    private BigDecimal checkAmount;

    /**
     * 合同金额;合同金额
     */
    private BigDecimal contractAmount;

    /**
     * 至上年底累计执行;至上年底累计执行
     */
    private BigDecimal prevAmount;

    /**
     * 上年调整计划;上年调整计划
     */
    private BigDecimal prevAdjust;

    /**
     * 上年实际执行;上年实际执行
     */
    private BigDecimal prevPayed;

    /**
     * 当年资金需求;当年资金需求
     */
    private BigDecimal currAmount;

    /**
     * 当年初步使用计划;当年初步使用计划
     */
    private BigDecimal currEarly;

    /**
     * 当年正式计划;当年正式计划
     */
    private BigDecimal currFormal;

    /**
     * 当年调整计划;当年调整计划
     */
    private BigDecimal currAdjust;

    /**
     * 当年已执行;当年已执行
     */
    private BigDecimal currPayed;

    /**
     * 开工(采购)时间;开工(采购)时间
     */
    private String beginDate;

    /**
     * 竣工(完成)时间;竣工(完成)时间
     */
    private String endDate;

    /**
     * 备注1;备注1
     */
    private String remark1;

    /**
     * 备注2;备注2
     */
    private String remark2;

    /**
     * 创建人;创建人，存储用户名
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间;创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新人;更新人，存储用户名
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间;更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableField(exist = false)
    private List<UsagePlanItem> child;

    @TableField(exist = false)
    private ProjectInfoVo  projectInfo;

    public void add(UsagePlanItem other) {
        if (other.estAmount != null) {
            if (this.estAmount == null) {
                this.estAmount = other.estAmount;
            } else {
                this.estAmount = this.estAmount.add(other.estAmount);
            }
        }
        if (other.libAmount != null) {
            if (this.libAmount == null) {
                this.libAmount = other.libAmount;
            } else {
                this.libAmount = this.libAmount.add(other.libAmount);
            }
        }
        if (other.checkAmount != null) {
            if (this.checkAmount == null) {
                this.checkAmount = other.checkAmount;
            } else {
                this.checkAmount = this.checkAmount.add(other.checkAmount);
            }
        }
        if (other.contractAmount != null) {
            if (this.contractAmount == null) {
                this.contractAmount = other.contractAmount;
            } else {
                this.contractAmount = this.contractAmount.add(other.contractAmount);
            }
        }
        if (other.prevAmount != null) {
            if (this.prevAmount == null) {
                this.prevAmount = other.prevAmount;
            } else {
                this.prevAmount = this.prevAmount.add(other.prevAmount);
            }
        }
        if (other.prevAdjust != null) {
            if (this.prevAdjust == null) {
                this.prevAdjust = other.prevAdjust;
            } else {
                this.prevAdjust = this.prevAdjust.add(other.prevAdjust);
            }
        }
        if (other.prevPayed != null) {
            if (this.prevPayed == null) {
                this.prevPayed = other.prevPayed;
            } else {
                this.prevPayed = this.prevPayed.add(other.prevPayed);
            }
        }
        if (other.currAmount != null) {
            if (this.currAmount == null) {
                this.currAmount = other.currAmount;
            } else {
                this.currAmount = this.currAmount.add(other.currAmount);
            }
        }
        if (other.currEarly != null) {
            if (this.currEarly == null) {
                this.currEarly = other.currEarly;
            } else {
                this.currEarly = this.currEarly.add(other.currEarly);
            }
        }
        if (other.currFormal != null) {
            if (this.currFormal == null) {
                this.currFormal = other.currFormal;
            } else {
                this.currFormal = this.currFormal.add(other.currFormal);
            }
        }
        if (other.currAdjust != null) {
            if (this.currAdjust == null) {
                this.currAdjust = other.currAdjust;
            } else {
                this.currAdjust = this.currAdjust.add(other.currAdjust);
            }
        }
        if (other.currPayed != null) {
            if (this.currPayed == null) {
                this.currPayed = other.currPayed;
            } else {
                this.currPayed = this.currPayed.add(other.currPayed);
            }
        }
    }
}