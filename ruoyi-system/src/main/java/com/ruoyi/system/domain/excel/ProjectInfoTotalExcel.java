package com.ruoyi.system.domain.excel;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/18 15:53
 */

@Data
public class ProjectInfoTotalExcel {

    public ProjectInfoTotalExcel() {
        this.estAmountTotal = "0";
        this.libAmountTotal = "0";
        this.checkAmountTotal = "0";
        this.historicalExecutedAmountsTotal = "0";
        this.currYearExecutedAmountsTotal = "0";
        this.executedAmountsTotal = "0";
        this.declareAmountTotal = "0";
        this.formalAmount1 = "0";
        this.formalAmount2 = "0";
        this.formalAmountTotal = "0";
        this.applyAdjustTotal = "0";
        this.adjustAmountTotal = "0";
        this.adjustAmount1 = "0";
        this.adjustAmount2 = "0";
    }

    /**
     * 项目估算合计值
     */
    private String estAmountTotal;

    /**
     * 入库金额合计值
     */
    private String libAmountTotal;

    /**
     * 核定金额合计值
     */
    private String checkAmountTotal;

    /**
     * 截至上年底合计值
     */
    private String historicalExecutedAmountsTotal;

    /**
     * 当年合计值
     */
    private String currYearExecutedAmountsTotal;

    /**
     * 执行金额合计值
     */
    private String executedAmountsTotal;

    /**
     * 上报计划合计值
     */
    private String declareAmountTotal;

    /**
     * 下达计划合计值
     */
    private String formalAmountTotal;
    private String formalAmount1;
    private String formalAmount2;
    /**
     * 调整计划合计值
     */
    private String applyAdjustTotal;

    /**
     * 调整下达合计值
     */
    private String adjustAmountTotal;
    private String adjustAmount1;
    private String adjustAmount2;

}
