package com.ruoyi.system.domain.vo.project;


import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 重点项目清单表视图;
 *
 */
@Data
public class ProjectImpInputVo {


    /**
     * 主键;主键，确保有序
     */
    private String id;
    /**
     * 关联年度项目ID;
     */
    private String relId;
    /**
     * 项目年度;项目年度
     */
    private Integer year;
    /**
     * 项目id
     */
    private String projId;
    /**
     * 项目名称
     */
    private String projName;
    /**
     * 项目金额
     */
    private BigDecimal projAmount;
    /**
     * 部门ID
     */
    private String deptId;
    /**
     * 项目阶段
     */
    private Integer stepNo;
    private String stepName;
    /**
     * 项目编号
      */
    private String projCode;
    /**
     * 年度目标
     */
    private String yearGoal;
    /**
     * 月度计划计划
     */
    private String monthPlan;
    /**
     * 重点月度计划管理
     */
    private  ProjectImportantJhVo projectImportantJhVo;
    /**
     * 重点月度计划管理 List
     */
    private List<ProjectImportantJhVo> projectImportantJhVos;
}
