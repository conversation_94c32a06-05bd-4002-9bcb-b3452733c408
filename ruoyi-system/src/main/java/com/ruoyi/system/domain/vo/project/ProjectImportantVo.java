package com.ruoyi.system.domain.vo.project;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 重点项目清单表视图;
 *
 */
@Data
public class ProjectImportantVo {

    /**
     * 主键;主键，确保有序
     */

    private String id;
    /**
     * 关联年度项目ID;
     */
    @NotNull(message = "relId 主键不能为空")
    private String relId;

    /**
     * 项目年度;项目年度
     */
    private Integer year;

    /**
     * 重点项目ID;重点项目ID
     */
    private String projId;

    /**
     * 项目编号;项目编号（此编号仅用于重点项目）
     */
    private String projCode;

    /**
     * 项目阶段;项目阶段
     */
    private Integer stepNo;

    /**
     * 项目阶段名称;项目阶段（1方案研究阶段、2预算评审及报批阶段、3待开工阶段、4实施阶段、5经常性项目）
     */
    private String stepName;

    /**
     * 排序号;排序号
     */
    private Integer seqNo;

    /**
     * 年度目标;年度目标
     */
    private String yearGoal;

    /**
     * 进展说明及需协调问题;进展说明及需协调问题
     */
    private String progressIssue;

    /**
     * 更新人;更新人，存储用户名
     */
    private String updateBy;

    /**
     * 更新时间;更新时间
     */
    private Date updateTime;

    /**
     * 项目月度计划;项目月度计划(json)，[{"month":1,"plan":"dddd"}]
     */
    private String monthPlan;

    /**
     * 项目月度执行;项目月度执行(json)，[{"month":1,"action":"dddd"}]
     */
    private String monthAction;

    /**
     * 项目名称
     */
    private String projName;
    /**
     * 项目资金
     */
    private BigDecimal projAmount;
    /**
     * 计划开工
     */
    private Date planBegin;
    /**
     * 计划完工
     */
    private Date projEnd;

    /**
     * 年度正式计划
     */
    private BigDecimal yearPlan;
    /**
     * 年度已执行
     */
    private BigDecimal yearExecuted;
    /**
     * 部门ID
     */
    private String deptId;
    /**
     * 重点月度计划管理
     */
    private  ProjectImportantJhVo projectImportantJhVo;
    /**
     * 重点月度计划管理 List
     */
    private List<ProjectImportantJhVo> projectImportantJhVos;
    /**
     * 重点月度实施管理
     */
    private  ProjectImportantExceVo projectImportantExceVo;
    /**
     * 重点月度实施管理 List
     */
    private List<ProjectImportantExceVo> projectImportantExceVos;
}
