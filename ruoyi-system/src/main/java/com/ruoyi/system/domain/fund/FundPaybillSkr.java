package com.ruoyi.system.domain.fund;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * 资金拨款单收款人（多人收款）
 *
 * <AUTHOR>
 * @TableName fund_paybill_skr
 */
@TableName(value = "fund_paybill_skr")
@Data
public class FundPaybillSkr {
    /**
     * 主键;主键，确保有序
     */
    @TableId(value = "id")
    private String id;

    /**
     * 拨款单ID;拨款单ID
     */
    private String billId;

    /**
     * 银行账号ID;银行账号ID
     */
    private String accountId;

    /**
     * 开户行名;开户行名
     */
    private String bankName;

    /**
     * 开户行号;开户行号
     */
    private String stationCode;

    /**
     * 户名;户名
     */
    private String accountName;

    /**
     * 户号;户号
     */
    private String accountNumber;

    /**
     * 金额;金额
     */
    private BigDecimal amount;

    /**
     * 排序号;排序号
     */
    private Integer seqNo;

    /**
     * 付款说明
     */
    private String remark;

    /**
     * 支付日期
     */
    private Date payDate;

    /**
     * 支付凭证
     */
    private String payVoucher;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 项目ID
     */
    @TableField(exist = false)
    private String projId;
}