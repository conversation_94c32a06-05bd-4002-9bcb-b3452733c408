package com.ruoyi.system.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/18 15:34
 */
@Data
@Accessors(chain = true)
public class ProjectInfoMainExcel {

    @ExcelProperty("年度")
    private Integer year;

    @ExcelProperty("部门名称")
    private String deptName;

    @ExcelProperty("结转项目数")
    private Integer jzCount;

    @ExcelProperty("结转项目集合")
    private List<ProjectInfoExcel> jzList;

    @ExcelProperty("结转小计值")
    private ProjectInfoTotalExcel jzTotal;

    @ExcelProperty("经常性项目数")
    private Integer jcCount;

    @ExcelProperty("经常性项目集合")
    private List<ProjectInfoExcel> jcList;

    @ExcelProperty("经常性小计值")
    private ProjectInfoTotalExcel jcTotal;

    @ExcelProperty("新增项目数")
    private Integer xzCount;

    @ExcelProperty("经常性项目集合")
    private List<ProjectInfoExcel> xzList;

    @ExcelProperty("新增小计值")
    private ProjectInfoTotalExcel xzTotal;

    @ExcelProperty("总项目数")
    private Integer hjCount;

    @ExcelProperty("总合计值")
    private ProjectInfoTotalExcel total;


}
