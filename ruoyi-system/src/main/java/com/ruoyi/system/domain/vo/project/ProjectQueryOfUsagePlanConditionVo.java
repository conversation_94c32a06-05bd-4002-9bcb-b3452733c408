package com.ruoyi.system.domain.vo.project;

import lombok.Data;

import java.util.List;

/**
 * 用款计划表查询项目条件对象
 *
 * <AUTHOR>
 * @date 2025/06/23 10:07
 */
@Data
public class ProjectQueryOfUsagePlanConditionVo {

    /**
     * 年份
     */
    private String year;
    /**
     * 单位名称 0：查上报单位，1：考核单位
     */
    private String projectUnits;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 项目类型
     */
    private String typeId;

    /**
     * 项目性质代号
     */
    private String natureCode;

    /**
     * 项目用途ids
     */
    private List<String> purposeIds;

    /**
     * 考核部门ID
     */
    private String assessOrgid;


}
