package com.ruoyi.system.domain.project;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 年度项目关系表
 *
 * <AUTHOR>
 * @TableName project_year_relation
 */
@TableName(value ="project_year_relation")
@Data
public class ProjectYearRelation {
    /**
     * 主键;主键，确保有序
     */
    @TableId(value = "id")
    private String id;

    /**
     * 年度;项目年度
     */
    private Integer year;

    /**
     * 项目ID;项目ID
     */
    private String projId;

    /**
     * 项目类型;项目类型（1结转项目、2经常性项目、3新增项目）
     */
    private Integer projType;

    /**
     * 排序号;排序号，默认1
     */
    private Integer seqNo;

    /**
     * 更新时间;更新时间（每日定时更新，归类）
     */
    private Date updatedTime;

    /**
     * 回收站;回收站（0，1），默认为0，归类的结项项目，如果不执行，可标记放入回收站
     */
    private String isRecycle;

    /**
     * 结项删除;结项删除（0，1），默认为0
     */
    private String isClose;

    /**
     * 是否重点项目;是否重点项目（0，1），默认0
     */
    private String impProj;

    /**
     * 重点项目类型;重点项目类型
     */
    private Integer impCode;

    /**
     * 重点项目类型（名称）;重点项目类型（名称）
     */
    private String impName;
}