package com.ruoyi.system.domain.event;

import com.ruoyi.common.constant.CommonConstant;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/6/12 15:34
 */
@Data
@Accessors(chain = true)
public class OperateEvent {

    {
        currentOperateUser = SecurityUtils.getLoginUser();
    }
    /**
     * 类型 （必填）
     */
    public CommonConstant.ProjectOrPlan type;

    /**
     * 年 （类型为plan 年和计划id二选一）
     */
    public Integer year;

    /**
     * 项目ID
     */
    private String projId;

    /**
     * 计划ID（类型为plan 年和计划id二选一）
     */
    private String planId;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 操作意见
     */
    private String operateOpinion;

    /**
     * 操作理由
     */
    private String operateReason;

    /**
     * 待办ID
     */
    private String todoId;

    /**
     * 当前操作用户
     */
    private LoginUser currentOperateUser;
}
