package com.ruoyi.system.domain.project;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 资金计划表
 * <AUTHOR>
 * @TableName project_plan
 */
@TableName(value ="project_plan")
@Data
public class ProjectPlan {
    /**
     * 主键;主键，确保有序
     */
    @TableId(value = "id")
    private String id;

    /**
     * 项目ID;项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private String projId;

    /**
     * 资金计划年度;资金计划年度
     */
    @NotNull(message = "资金计划年度不能为空")
    private Integer year;

    /**
     * 计划状态;计划状态（-1退回申报人、0草稿、1已上报、2已受理、3已分送、4已审批、9已发布）
     */
    private Integer planState;

    /**
     * 上报计划类型;上报计划类型（年初计划、调整计划）
     */
    @NotNull(message = "上报计划类型不能为空")
    private String planType;

    /**
     * 上报计划金额;上报计划金额
     */
    @NotNull(message = "上报计划金额不能为空")
    private BigDecimal declareAmount;

    /**
     * 上报人;上报人
     */
    private String declareUserid;

    /**
     * 上报时间;上报时间
     */
    private Date declareTime;

    /**
     * 年初计划金额;年初计划金额（经发处下达）
     */
    private BigDecimal earlyAmount;

    /**
     * 正式计划金额;正式计划金额（经发处下达）
     */
    private BigDecimal formalAmount;

    /**
     * 调整计划金额;调整计划金额（经发处下达）
     */
    private BigDecimal adjustAmount;

    /**
     * 计划描述;计划描述
     */
    private String describe;

    /**
     * 计划篮子ID;计划篮子ID（10待定、20原已定、25新增已定、30预备金）
     */
    private Integer basketId;

    /**
     * 计划篮子名称;计划篮子名称（10待定、20原已定、25新增已定、30预备金）
     */
    private String basketName;

    /**
     * 申请调整金额;申请调整金额
     */
    private BigDecimal applyAdjust;

    /**
     * 申请调整理由;申请调整理由
     */
    private String applyReason;

    /**
     * 调整审批状态;调整审批状态（-1退回、0草稿、1已上报、2确认调整）
     */
    private Integer applyState;

    /**
     * 调整申请人;调整申请人
     */
    private String applyUserid;

    /**
     * 调整申请时间;调整申请时间
     */
    private Date applyTime;

    /**
     * 调整退回理由;调整退回理由
     */
    private String applyRefused;

    /**
     * 资金计划明细;资金计划明细，格式：[{"sort":1,"title":"项目内容","estAmount":"项目估算(decimal)","executedAmount":"截至上年底已执行资金(decimal)","planAmount":"当年上报计划(decimal)","describe":"项目建设周期及进度说明","payPercent":"合同支付比例，[0,100]表示支付合同的0%至100%，[30,60]表示支付合同的30%至60%"}]
     */
    private String planDetail;

}