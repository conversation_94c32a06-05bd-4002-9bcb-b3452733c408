package com.ruoyi.system.domain.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 项目篮子Dto
 *
 * <AUTHOR>
 * @date 2025/6/10 16:26
 */
@Data
public class ProjectBasketDto {

    /**
     * 项目ID
     */
    @NotBlank(message = "项目id不能为空")
    private String projId;

    /**
     * 项目篮子ID
     */
    @NotBlank(message = "项目篮子id不能为空")
    private String basketCode;

    /**
     * 项目篮子名称
     */
    @NotBlank(message = "项目篮子名称不能为空")
    private String basketName;

    /**
     * 项目入库金额，单位：万元（2位小数）
     */
    @NotBlank(message = "项目入库金额不能为空")
    private BigDecimal libAmount;

    /**
     * 是否发送短信，0-否，1-是
     */
    private String isSendSms;
}
