package com.ruoyi.system.domain.vo.project;

import lombok.Data;

import java.util.List;

/**
 * 项目管理查询条件
 *
 * <AUTHOR>
 * @date 2025/6/10 13:22
 */
@Data
public class ProjectManageConditionVo {

    /**
     * 年份
     */
    private Integer year;
    /**
     * 考核主体
     */
    private String assessOrgid;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 已下达年初计划
     */
    private String hasEarlyAmount;

    /**
     * 已下达调整计划
     */
    private String hasAdjustAmount;

    /**
     * 项目类型
     */
    private String typeId;

    /**
     * 项目性质代号
     */
    private String natureCode;

    /**
     * 项目用途ids
     */
    private List<String> purposeIds;

    /**
     * 是否涉及数字化园区（0否，1是）
     */
    private String isSzhyq;

}
