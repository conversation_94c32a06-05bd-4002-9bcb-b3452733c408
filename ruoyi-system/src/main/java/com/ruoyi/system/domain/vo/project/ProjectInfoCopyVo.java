package com.ruoyi.system.domain.vo.project;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/18 11:27
 */
@Data
public class ProjectInfoCopyVo {

    /**
     * 主键;主键，确保有序
     */
    private String id;

    /**
     * 项目名称;项目名称
     */
    private String name;

    /**
     * 项目负责人;项目负责人
     */
    private String handler;

    /**
     * 项目金额
     */
    private BigDecimal estAmount;

    /**
     * 开始周期
     */
    private Date beginDate;

    /**
     * 结束周期
     */
    private Date endDate;
}
