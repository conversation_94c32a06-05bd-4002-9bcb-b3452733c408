package com.ruoyi.system.domain.project;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 项目编号表
 *
 * <AUTHOR>
 * @TableName project_sn
 */
@TableName(value ="project_sn")
@Data
public class ProjectSn {
    /**
     * 主键;主键，无意义，确保有序
     */
    @TableId(value = "id")
    private String id;

    /**
     * 项目ID;project_info表外键
     */
    private String projId;

    /**
     * 项目临时年度;项目临时年度
     */
    private Integer tempYear;

    /**
     * 项目临时序号;项目临时序号（和project_info表中的seqid一致）
     */
    private Integer tempSeqid;

    /**
     * 项目临时编号;项目临时编号，项目暂存后即生成，规则：临-年度4位-单位编号2位-项目顺序号4位（超出4位以实际位数）（临-2025-13-1931）
     */
    private String tempSn;

    /**
     * 临时编号产生时间;临时编号产生时间，因临时编号在初次保存时即产生，该时间和初次保存时间一致
     */
    private Date tempTime;

    /**
     * 项目正式年度;项目正式年度，首个资金计划发布年度
     */
    private Integer formalYear;

    /**
     * 项目正式序号;项目正式编号，该年度已发布的资金计划排序号，从1开始
     */
    private Integer formalSeqid;

    /**
     * 项目正式编号;项目正式编号，优先于临时编号，项目发布后生成，规则：年度4位-部门编号2位-项目类型2位-项目性质2位-年度发布顺序号3位（2025-13-B2-JC-027）
     */
    private String formalSn;

    /**
     * 正式编号产生时间;正式编号产生时间，即首个资金计划发布的时间
     */
    private Date formalTime;
}