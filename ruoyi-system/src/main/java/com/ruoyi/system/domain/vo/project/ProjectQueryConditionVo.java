package com.ruoyi.system.domain.vo.project;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/5 16:07
 */
@Data
public class ProjectQueryConditionVo {

    /**
     * 年份
     */
    private Integer year;
    /**
     * 单位名称 0：查上报单位，1：考核单位
     */
    private String projectUnits;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 项目类型
     */
    private String typeId;

    /**
     * 项目性质代号
     */
    private String natureCode;

    /**
     * 项目用途ids
     */
    private List<String> purposeIds;

    /**
     * 是否涉及数字化园区（0否，1是）
     */
    private String isSzhyq;

    /**
     * 是否涉及精细化管理（0否，1是）
     */
    private String isJxhgl;

    /**
     * 是否涉及政府购买服务（0否，1是）
     */
    private String isZfgm;

    /**
     * 项目篮子ID
     */
    private String basketCode;

    /**
     * 入库标记，默认0，已入库为1
     */
    private String libState;

    /**
     * 核定标记，默认0，已核定为1
     */
    private String checkState;

    /**
     * 是否仅考核
     */
    private String onlyAssess;

    /**
     * 考核部门ID
     */
    private String assessOrgid;


}
