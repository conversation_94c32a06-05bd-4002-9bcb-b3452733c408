package com.ruoyi.system.domain.vo.project;


import lombok.Data;

import java.math.BigDecimal;

/**
 * 重点项目清单表视图;
 *
 */
@Data
public class ProjectImpStepVo {


    /**
     * 项目年度;项目年度
     */
    private Integer year;

    /**
     * 项目阶段;项目阶段
     */
    private Integer stepNo;

    /**
     * 项目阶段名称;项目阶段（1方案研究阶段、2预算评审及报批阶段、3待开工阶段、4实施阶段、5经常性项目）
     */
    private String stepName;
    /**
     * 项目金额
     */
    private BigDecimal projAmount;

    /**
     * 项目数量
     */
    private BigDecimal projCount;

}
