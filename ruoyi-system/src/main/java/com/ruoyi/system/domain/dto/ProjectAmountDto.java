package com.ruoyi.system.domain.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 项目入库信息Dto
 *
 * <AUTHOR>
 * @date 2025/6/11 09:53
 */
@Data
public class ProjectAmountDto {

    /**
     * 项目ID
     */
    @NotBlank(message = "项目id不能为空")
    private String projId;

    /**
     * 项目入库金额
     */
    @NotBlank(message = "金额不能为空")
    private BigDecimal amount;

    /**
     * 入库意见
     */
    @NotBlank(message = "意见不能为空")
    private String reason;

    /**
     * 是否发送短信，0-否，1-是
     */
    private String isSendSms;


}
