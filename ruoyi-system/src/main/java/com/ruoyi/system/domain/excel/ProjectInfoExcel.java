package com.ruoyi.system.domain.excel;

import lombok.Data;

/**
 * 项目信息表视图;
 *
 * <AUTHOR>
 */
@Data
public class ProjectInfoExcel {

    /**
     * 序号
     */
    private Integer index;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 项目编号
     */
    private String sn;

    /**
     * 项目单位
     */
    private String assessOrgname;

    /**
     * 经办人
     */
    private String handler;

    /**
     * 项目估算
     */
    private String estAmount;

    /**
     * 项目状态
     */
    private String projectStatus;

    /**
     * 入库金额
     */
    private String libAmount;

    /**
     * 核定金额
     */
    private String checkAmount;

    /**
     * 历史执行金额
     */
    private String historicalExecutedAmounts;

    /**
     * 当年执行金额
     */
    private String currYearExecutedAmounts;

    /**
     * 合计
     */
    private String executedAmountsTotal;

    /**
     * 上报计划
     */
    private String declareAmount;

    /**
     * 下达计划
     */
    private String formalAmount;

    /**
     * "1" 表示通过用款计划表带出来的下达计划金额
     */
    private String isFormalFlag;

    /**
     * 调整计划
     */
    private String applyAdjust;

    /**
     * "2" 表示通过下达计划带出来的调整j计划金额 变灰
     */
    private String isApplyAdjustFlag;

    /**
     * 调整下达
     */
    private String adjustAmount;

    /**
     * "1" 表示通过用款计划表带出来的下达调整下达 变红 "2" 表示通过下达计划带出来的调整下达金额 变灰
     */
    private String isAdjustAmountFlag;

    /**
     * 备注
     */
    private String remark;

}