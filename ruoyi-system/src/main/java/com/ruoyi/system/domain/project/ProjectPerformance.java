package com.ruoyi.system.domain.project;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;
import lombok.Data;

/**
 * 项目绩效目标
 * <AUTHOR>
 * @TableName project_performance
 */
@TableName(value ="project_performance")
@Data
public class ProjectPerformance {
    /**
     * 主键;主键，无意义，确保有序
     */
    @TableId(value = "id")
    private String id;

    /**
     * 项目ID;项目ID
     */
    private String projId;

    /**
     * 一级指标;一级指标
     */
    private String indexLevel1;

    /**
     * 二级指标;二级指标
     */
    private String indexLevel2;

    /**
     * 三级指标;三级指标
     */
    private String indexLevel3;

    /**
     * 指标目标值;指标目标值
     */
    private String indexTarget;

    /**
     * 排序号;排序号
     */
    private Integer indexSort;

    /**
     * 创建时间;创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建人;创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间;更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 更新人;更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
}