package com.ruoyi.system.domain.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.system.domain.project.ProjectPerformance;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 项目绩效目标Dto
 * <AUTHOR>
 * @TableName project_performance
 */
@Data
public class ProjectPerformanceDto {

    /**
     * 项目ID
     */
    @NotBlank(message = "项目id不能为空")
    private String projId;

    /**
     * 项目总目标（绩效）
     */
    private String goal;

    /**
     * 项目绩效目标集
     */
    List<ProjectPerformance> performanceList;
}