package com.ruoyi.system.domain.vo.project;


import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 重点项目清单表视图;
 *
 */
@Data
public class ProjectImpVo {


    /**
     * 项目年度;项目年度
     */
    private Integer year;

    /**
     * 重点项目ID;重点项目ID
     */
/*    private String projId;*/
      /**
     * 项目名称
     */
/*    private String projName;*/
    /**
     * 部门ID
     */
    private String deptId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 项目金额
     */
    private BigDecimal projAmount;

    /**
     * 项目数量
     */
    private BigDecimal projCount;
    /**
     * 年度正式计划
     */
    private BigDecimal yearPlan;
    /**
     * 年度已执行
     */
    private BigDecimal yearExecuted;

}
