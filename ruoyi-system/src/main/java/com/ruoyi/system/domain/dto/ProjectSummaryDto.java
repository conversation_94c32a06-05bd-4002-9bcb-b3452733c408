package com.ruoyi.system.domain.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 项目概述Dto
 * <AUTHOR>
 * @date 2025/6/5 13:03
 */
@Data
public class ProjectSummaryDto {
    /**
     * 项目id
     */
    @NotBlank(message = "项目id不能为空")
    private String projId;

    /**
     * 项目必要性
     */
    private String necessity;

    /**
     * 项目依据
     */
    private String basis;

    /**
     * 项目主要内容
     */
    private String mainCnt;

    /**
     * 实施计划
     */
    private String actionPlan;
}
