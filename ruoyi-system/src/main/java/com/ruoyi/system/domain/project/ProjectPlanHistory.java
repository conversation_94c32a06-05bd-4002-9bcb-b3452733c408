package com.ruoyi.system.domain.project;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

import lombok.Data;

/**
 * 项目(计划）变更记录表
 *
 * <AUTHOR>
 * @TableName project_plan_history
 */
@TableName(value = "project_plan_history")
@Data
public class ProjectPlanHistory {
    /**
     * 主键;主键，确保有序
     */
    @TableId(value = "id")
    private String id;

    /**
     * 类型（项目，计划）;类型（项目，计划）
     */
    private String type;

    /**
     * 项目ID;项目ID（即使是计划，也需存项目ID）
     */
    private String projId;

    /**
     * 计划ID;计划ID
     */
    private String planId;

    /**
     * 变更人;变更人ID
     */
    private String updateUserid;

    /**
     * 变更时间;变更时间
     */
    private Date updateTime;

    /**
     * 变更原因;变更原因
     */
    private String updateReason;

    /**
     * 变更前数据;变更前数据，JSON
     */
    private String dataJson;

}