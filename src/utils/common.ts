import { dayjs } from "element-plus"
import Bignumber from 'bignumber.js';
import { isNumber, isString, isNaN } from "lodash";

export const getLabelFromDicts = (dicts?: any, value?: string) => {
  return dicts?.find((t: any) => t.value == value)?.label
}

export const dateFormat = (date?: string|Date, format = 'YYYY-MM-DD') => {
    if (date && dayjs(date).isValid()) {
        return dayjs(date).format(format)
    }
    return ''
}

export const sumAmount = (nums?: (number|undefined|null)[]) => {
  if (!nums || nums.length === 0) {
    return null;
  }
  const sum = nums.reduce((acc, num) => {
    if (num === undefined || num === null || isNaN(num)) {
      return acc;
    }
    return acc.plus(new Bignumber(num));
  }, new Bignumber(0));
  return sum.isZero() ? null : sum.toNumber();
}

export const checkIsNumber = (value: any): value is number => {
  // 检查是否是数字或者是可以转换为数字的字符串
  return (isNumber(value) && !isNaN(value)) || 
         (isString(value) && /^-?\d+(\.\d+)?$/.test(value));
}


export const roundNumber2 = (num: string | number): string | number => {
  if (!checkIsNumber(num)) return num

  const b = new Bignumber(num)
  const rounded = b.decimalPlaces(2, Bignumber.ROUND_HALF_UP)

  // 判断是否是整数
  if (rounded.isInteger()) {
    return rounded.toFixed(0) // 不保留小数
  } else {
    return rounded.toFixed(2, Bignumber.ROUND_HALF_UP)
  }
}

/**
 * 从日期数组中获取最大日期和最小日期
 * @param {Array} dateArray 日期数组
 * @returns {Object} 包含最大日期和最小日期的对象
 */
export function getMinMaxDate(dateArray: (string|Date|undefined)[]) {
  // 检查输入是否为数组
  if (!Array.isArray(dateArray)) {
    throw new Error('Input must be an array');
  }
  const newDates = dateArray.filter(date => dayjs(date).isValid())

  // 检查数组是否为空
  if (newDates.length === 0) {
    throw new Error('Array cannot be empty');
  }
  

  // 使用 dayjs 转换日期并初始化最大和最小日期
  let maxDate = dayjs(newDates[0]);
  let minDate = dayjs(newDates[0]);

  // 遍历日期数组
  for (let i = 1; i < newDates.length; i++) {
    const currentDate = dayjs(newDates[i]);

    // 更新最大日期
    if (currentDate.isAfter(maxDate)) {
      maxDate = currentDate;
    }

    // 更新最小日期
    if (currentDate.isBefore(minDate)) {
      minDate = currentDate;
    }
  }

  // 返回最大日期和最小日期
  return {
    maxDate: maxDate.format('YYYY-MM-DD'), // 格式化为字符串返回
    minDate: minDate.format('YYYY-MM-DD')
  };
}