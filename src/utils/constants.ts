/** 文件源类型枚举 */
export enum EFileSoureType {
    PROJECT = '项目',
    CONTRACT = '合同'
}

/** 文件类型枚举 */
export enum EFilePrimaryType {
    /** 入库依据 */
    rk = '100',
    /** 核定依据 */
    hd = '101'
}

/** 项目状态 */
export enum EProjectState {
    /** 退回 */
    reject = -1,
    /** 草稿 */
    draft = 0,
    /** 已上报 */
    reported = 1,
    /** 确认调整 */
    adjust = 2
}

/** 资金计划状态 */
export enum EFundPlanState {
    /** 退回 */
    reject = -1,
    /** 草稿 */
    draft = 0,
    /** 已上报 */
    ysb = 1,
    /** 已受理 */
    ysl = 2,
    /** 已分送 */
    yfs = 3,
    /** 已审批 */
    ysp = 4,
    /** 已发布 */
    yfb = 9
}

/**
 * 入库标记
 */
export enum ELibState {
    /** 未入库 */
    wrk = 0,
    /** 已入库 */
    yrk = 1
}

/**
 * 核定标记
 */
export enum ECheckState {
    /** 未核定 */
    whd = 0,
    /** 已核定 */
    yhd = 1
}

/** 资金计划数据项目类型 */
export enum EUsageFundPlanItemType {
    /**
     * 类型
     */
    type = 0,
    /** 项目 */
    project = 1,
    /** 项目组 */
    projectGroup = 2,
}


/**
 * 调整状态
 */
export enum EApplyAdjustState {
    /** 退回 */
    reject = -1,
    /** 草稿 */
    draft = 0,
    /** 已上报 */
    ysb = 1,
    /** 确认调整 */
    qrtz = 2
}