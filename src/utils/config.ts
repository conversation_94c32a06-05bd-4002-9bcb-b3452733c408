// @ts-ignore
import { ref, toRefs } from 'vue'
// @ts-ignore
import { getConfigKey } from '@/api/system/config' // 你的API方法
import { useConfigStore } from '@/store/modules/config' // 假设你有一个用于缓存配置的pinia store

export function useConfig(...keys: string[]) {
  const result = ref<Record<string, string>>({})

  keys.forEach((key) => {
    result.value[key] = ''
    const cached = useConfigStore().getConfig(key)
    if (cached) {
      result.value[key] = cached
    } else {
      getConfigKey(key).then((res: any) => {
        if (res.code === 200 && typeof res.msg === 'string') {
          result.value[key] = res.msg
          useConfigStore().setConfig(key, res.msg)
        }
      })
    }
  })

  return toRefs(result.value)
}
