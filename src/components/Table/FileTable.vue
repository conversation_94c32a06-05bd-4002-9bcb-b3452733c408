<template>
    <el-table v-bind="$attrs" :data="fileList" border>
        <el-table-column v-if="!hiddenColumns.includes('index')" type="index" label="序号" align="center"
            width="60px"></el-table-column>
        <el-table-column v-if="!hiddenColumns.includes('primaryType')" label="附件类型" prop="primaryType" align="center"
            show-overflow-tooltip></el-table-column>

        <el-table-column v-if="!hiddenColumns.includes('fileName')" label="文件名称" prop="fileName" align="center"
            show-overflow-tooltip>
            <template #default="{ row }">
                <a class="text-[var(--el-color-primary-light-3)]" @click="handleView(row)">{{ row.fileName }}</a>
            </template>
        </el-table-column>
        <el-table-column v-if="!hiddenColumns.includes('createdBy')" label="上传人" prop="createdBy" align="center"
            width="100px" show-overflow-tooltip></el-table-column>
        <el-table-column v-if="!hiddenColumns.includes('createdTime')" label="上传时间" prop="createdTime" align="center"
            width="180px" :formatter="(row: any) => dateFormat(row.createdTime, 'YYYY-MM-DD HH:mm:ss')"
            show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" align="center" width="120px">
            <template #default="{ row }">
                <div class="flex justify-center">
                    <el-button v-if="!hiddenColumns.includes('del')" class="px-1" type="danger" size="small"
                        icon="delete" plain text @click="handleRemove(row.id)">删除</el-button>
                    <el-button class="!ml-0 px-1" type="primary" size="small" icon="download" plain text
                        @click="handleDownload(row)">下载</el-button>
                </div>

            </template>
        </el-table-column>
    </el-table>
    <el-dialog v-model="fileViewShow" width="1000px" append-to-body>
        <template #title>
            <span class="text-[16px]">预览文件</span>
        </template>
        <iframe :src="fileUrl" class="w-full h-[700px] border-none" />
    </el-dialog>
</template>

<script setup lang="ts">
import { removeFile, downloadFile } from '@/api/fund/project/file';
import { dateFormat } from '@/utils/common';
import { ElMessage, ElMessageBox } from 'element-plus';
import { saveAs } from 'file-saver'

const { proxy } = getCurrentInstance() as { proxy: any };

const emit = defineEmits(['update']);
const props = withDefaults(defineProps<{
    fileList: ISysAttachment[],
    hiddenColumns: string[],
}>(), {
    hiddenColumns: () => [] as string[]
});

const handleDownload = (row: ISysAttachment) => {
    downloadFile(row.id!).then(res => {
        saveAs(new Blob([res]), row.fileName ?? '文件')
    })
}

const imageTypes = ['png', 'jpg', 'jpeg', 'webp', 'bmp']
const pdf = ['pdf']

const fileViewShow = ref(false)
const fileUrl = ref('')
const handleView = (row: ISysAttachment) => {
    downloadFile(row.id!).then(res => {
        const ext = row.fileExt!.toLocaleLowerCase()
        let mimeType = ''
        if (imageTypes.includes(ext)) mimeType = 'image/' + row.fileExt
        if (pdf.includes(ext)) mimeType = 'application/pdf'
        if (mimeType) {
            const blob = new Blob([res], { type: mimeType })
            fileUrl.value = URL.createObjectURL(blob)
            fileViewShow.value = true
        }

    })
}

const handleRemove = (id: string) => {
    ElMessageBox.confirm('是否确认删除该文件？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        removeFile(id).then(res => {
            ElMessage.success('删除成功');
            emit('update')
        })
    })
}
</script>