<template>
    <el-tree-select v-bind="$attrs" v-model="deptId" :data="deptOptions" :default-expanded-keys="getExpandedKeys"
        :props="{ value: 'deptId', label: 'deptName', children: 'children' }" value-key="deptId" check-strictly />
</template>

<script setup lang="ts">
// @ts-ignore
import { listDept } from "@/api/system/dept";
import { useOtherStore } from "@/store/modules/other";
import { foreach } from 'tree-lodash'

const deptId = defineModel("deptId")

const { proxy } = getCurrentInstance() as any;
const otherStore = useOtherStore()

const deptOptions = ref([]);
const deptList = ref([])
listDept().then((response: any) => {
    deptList.value = response.data
    const data = proxy.handleTree(response.data, "deptId");
    deptOptions.value = data

    otherStore.deptList = deptList.value
});

const getExpandedKeys = computed(() => {
    return deptList.value.map((t: any) => t.deptId)
})

defineExpose({
    getDeptList() {
        return deptList.value
    },
});
</script>