<template>
    <el-watermark :content="[(userStore.user.nickName ?? userStore.user.name ?? '') + ' ' + timeNow]" :font="font" style="min-height:100%;">
        <slot>这里是水印内容区域</slot>
    </el-watermark>
</template>

<script setup name="WaterMark">
import useUserStore from '@/store/modules/user'
import { dayjs } from 'element-plus';
import { ref } from 'vue';

const userStore = useUserStore()

const timeNow = ref('')
const font = {
    color: 'rgba(0,0,0,0.2)',
    fontSize: 14,
    fontWeight: 1
}

const getTimeNow = () => {    
    timeNow.value = dayjs(new Date()).format('YYYY-MM-DD HH:mm')

    window.setTimeout(() => {
        getTimeNow()
    }, 10 * 1000)
}

getTimeNow()
</script>