<template>
    <el-upload v-bind="$attrs" :action="actionUrl" :headers="headers" :show-file-list="false" :on-success="handleOnSuccess">
        <el-button type="primary">选择文件</el-button>
    </el-upload>
</template>

<script setup >
import { getToken } from "@/utils/auth";

const emit = defineEmits(['upload-success'])

const actionUrl = import.meta.env.VITE_APP_BASE_API + '/resource/file/upload';
const headers = ref({ Authorization: "Bearer " + getToken() });

const handleOnSuccess = (response) => {
    emit('upload-success', response)
}
</script>