export default {
  /**
   * 网页标题
   */
  title: import.meta.env.VITE_APP_TITLE,
  /**
   * 侧边栏主题 深色主题theme-dark，浅色主题theme-light
   */
  sideTheme: 'theme-light',
  /**
   * 是否系统布局配置
   */
  showSettings: true,

  /**
   * 是否显示顶部导航
   */
  topNav: false,

  /**
   * 是否显示 tagsView
   */
  tagsView: true,

  /**
   * 是否固定头部
   */
  fixedHeader: true,

  /**
   * 是否显示logo
   */
  sidebarLogo: true,

  /**
   * 是否显示动态标题
   */
  dynamicTitle: true,

  /**
   * @type {string | array} 'production' | ['production', 'development']
   * @description Need show err logs component.
   * The default is only used in the production env
   * If you want to also use it in dev, you can pass ['production', 'development']
   */
  errorLog: 'production',

 // casEnable: false, // 此开关替换casseting.js调用接口：  启用时修改地址 比如：http://localhost:3003===>http://************:18082/devfund
  casLoginUrl: 'http://demo.metinform.cn/scip-cas/login?service=http://************:18082/devfund/casLogin',
  casLogoutUrl: 'http://demo.metinform.cn/scip-cas/logout?service=http://************:18082/devfund',
  appLoginUrl: 'http://************:18082/devfund/casLogin', // 自身登录地址
  appLogoutUrl: '/logout' // 自身登出地址
}
