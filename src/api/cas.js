import request from '@/utils/request'
export function validateLogin(ticket, service) {
    const params = {
        ticket,
        service
    }
    return request({
        url: '/cas/login',
        headers: {
            isToken: false,
        },
        method: 'GET',
        params: params
    })
}

export function casConfigApi() {
    return request({
        url: '/system/config/casConfig',
        method: 'get'
    })
}