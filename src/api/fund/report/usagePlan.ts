import request from '@/utils/request'

export function getList() {
  return request<IResult<IUsagePlan[]>>({
    url: '/table/usagePlan/list',
    method: 'get',
  })
}

export function getListSnap(year: number) {
  return request<IResult<IUsagePlan[]>>({
    url: '/table/usagePlan/listSnap/' + year,
    method: 'get',
  })
}

export function delByYear(year: number) {
  return request<IResult<number>>({
    url: '/table/usagePlan/' + year,
    method: 'delete',
  })
}

export function delYearData(year: number, ids: string[]) {
  return request<IResult<number>>({
    url: `/table/usagePlan/${year}/item`,
    method: 'delete',
    data: ids
  })
}

export function save(data: Partial<IUsagePlan>&{ tableColumns: string}) {
  return request<IResult<boolean>>({
    url: '/table/usagePlan/save',
    method: 'post',
    data
  })
}

export function saveItem(data: {id: string, dataList: IUsagePlanItem[]}) {
  return request<IResult<boolean>>({
    url: '/table/usagePlan/saveItem',
    method: 'post',
    data
  })
}


export function getDetail(year: number, searchForm: any) {
  return request<IResult<IUsagePlan&{tableColumns: string,dataList: IUsagePlanItem[] }>>({
    url: '/table/usagePlan/detail/' + year,
    method: 'get',
    params: searchForm
  })
}

export function getDetailById(id: string) {
  return request<IResult<IUsagePlan&{tableColumns: string,dataList: IUsagePlanItem[] }>>({
    url: '/table/usagePlan/detailById/' + id,
    method: 'get',
  })
}


export function updateTableTitle(year: number, title: string) {
  const formData = new FormData()
  formData.append('year', String(year))
  formData.append('title', title)
  return request<IResult<number>>({
    url: '/table/usagePlan/updateTableTitle',
    method: 'put',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

export function updateTableUnit(year: number, unit: string) {
  const formData = new FormData()
  formData.append('year', String(year))
  formData.append('unit', unit)
  return request<IResult<number>>({
    url: '/table/usagePlan/updateTableUnit',
    method: 'put',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

export function updateTableValue(data: {id: string, year: number, prop: string, value: string}) {
  const formData = new FormData()
  formData.append('id', data.id)
  formData.append('year', String(data.year))
  formData.append('prop', data.prop)
  formData.append('value', data.value)
  return request<IResult<number>>({
    url: '/table/usagePlan/updateTableValue',
    method: 'put',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

export function updateTableClose(id: string) {
  return request<IResult<number>>({
    url: '/table/usagePlan/updateTableClose',
    method: 'put',
    params: {id}
  })
}

export function createSnap(year: number, title: string, dataList: IUsagePlanItem[]) {
  return request<IResult<boolean>>({
    url: '/table/usagePlan/createSnap',
    method: 'post',
    params: {
      year,
      title
    },
    data: dataList
  })
}

export function resort(year: number, dataList: any[]) {
  return request<IResult<boolean>>({
    url: `/table/usagePlan/${year}/resort`,
    method: 'put',
    data: dataList
  })
}

export function updateEarlyAmount(pid: string, year: number, earlyAmount: string) {
  const formData = new FormData()
  formData.append('pid', pid)
  formData.append('year', String(year))
  formData.append('earlyAmount', String(earlyAmount))
  return request<IResult<boolean>>({
    url: `/project/plan/updateEarlyAmount`,
    method: 'put',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}
