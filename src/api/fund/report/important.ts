import request from '@/utils/request'

/**
 * 查询重点项目清单
 * @returns 
 */
export function getList(vo: Partial<ProjectImportantConditionVo>) {
  return request<IResult<ProjectImportantVo[]>>({
    url: '/project/important/list',
    method: 'get',
    params: vo
  })
}

/**
 * 重点项目-》项目管理 录入清单
 * @returns 
 */
export function piList(vo: ProjectImportantConditionVo) {
  return request<IResult<ProjectImpInputVo[]>>({
    url: '/project/important/piList',
    method: 'get',
    params: vo
  })
}

/**
 * 重点项目-》计划管理 录入清单
 * @returns 
 */
export function jhList(vo: ProjectImportantConditionVo) {
  return request<IResult<ProjectImpInputVo[]>>({
    url: '/project/important/jhList',
    method: 'get',
    params: vo
  })
}



/**
 * 保存或修改重点项目记录数据
 * @returns 
 */
export function saveOrUpdatePjimportant(data: {
  id: string, 
  progressIssue?: string
  projectImportantExceVo?: ProjectImportantExceVo
}) {
  return request<IResult<null>>({
    url: '/project/important/saveOrUpdatePjimportant',
    method: 'post',
    data: data
  })
}

/**
 * 保存或修改重点项目记录数据
 * @returns 
 */
export function saveOrUpdatePiput(data: ProjectImpInputVo[]) {
  return request<IResult<null>>({
    url: '/project/important/saveOrUpdatePiput',
    method: 'post',
    data: data
  })
}

/**
 * 保存计划管理
 * @returns 
 */
export function saveOrUpdateJhput(data: ProjectImpInputVo[]) {
  return request<IResult<null>>({
    url: '/project/important/saveOrUpdateJhput',
    method: 'post',
    data: data
  })
}




export function getDeptList(year: number) {
  return request<IResult<any[]>>({
    url: '/project/important/deptList',
    method: 'get',
    params: {year}
  })
}

export function getYear() {
  return request<IResult<number[]>>({
    url: '/project/important/getYear',
    method: 'get',
  })
}

