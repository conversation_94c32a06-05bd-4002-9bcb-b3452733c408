import request from '@/utils/request'

export function addPlan(data: any) {
  return request<IResult<IProjectPlan>>({
    url: '/project/plan',
    method: 'post',
    data
  })
}

/**
 * 计划上报
 * @param pid 
 * @param year 
 * @returns 
 */
export function reportPlan(pid: string, year: number) {
  return request<IResult<any>>({
    url: `/project/plan/reportPlan`,
    method: 'put',
    params: {
      pid,
      year
    }
  })
}

/**
 * 撤销资金计划
 * @param pid 
 * @param year 
 * @returns 
 */
export function withdrawPlan(pid: string, year: number) {
  return request<IResult<any>>({
    url: `/project/plan/withdrawPlan`,
    method: 'put',
    params: {
      pid,
      year
    }
  })
}

