import request from '@/utils/request'

/**
 * 查询项目列表
 */
export function getNormalList(params: Partial<ProjectQueryConditionVo>) {
  return request<IResult<IProjectInfoVo[]>>({
    url: '/project/info/normalList',
    method: 'get',
    params: params
  })
}

/**
 * 查询回收站项目列表
 */
export function getAbnormalList(params: Partial<ProjectQueryConditionVo>) {
  return request<IResult<IProjectInfoVo[]>>({
    url: '/project/info/abnormalList',
    method: 'get',
    params: params
  })
}


/**
 * 新增项目信息
 */
export function add(data: IProjectInfoVo) {
  return request<IResult<{id: string, tempSn: string}>>({
    url: '/project/info',
    method: 'post',
    data
  })
}

export function update(data: IProjectInfoVo) {
  return request<IResult<any>>({
    url: '/project/info/info',
    method: 'put',
    data
  })
}

export function summary(data: any) {
  return request<IResult<any>>({
    url: '/project/info/summary',
    method: 'put',
    data
  })
}



export function getProjectDetail(id: string, year: number) {
  return request<IResult<IProjectInfoVo>>({
    url: `/project/info/${id}/${year}`,
    method: 'get',
  })
}


/**
 * 移到下一年
 * @param pid 
 * @param year 
 * @returns 
 */
export function moveNextYear(pid: string, year: number) {
  return request<IResult<any>>({
    url: '/project/info/moveNextYear',
    method: 'put',
    params: {
      pid,
      year
    }
  })
}
/**
 * 移到回收站
 * @param pid 
 * @param year 
 * @returns 
 */
export function putTrash(pid: string, year: number) {
  return request<IResult<any>>({
    url: `/project/info/putTrash/${pid}/${year}`,
    method: 'put',
  })
}

/**
 * 上报项目
 * @param pid 
 * @param year 
 * @returns 
 */
export function reportProject(pid: string, year: number) {
  return request<IResult<any>>({
    url: `/project/info/reportProject`,
    method: 'put',
    params: {
      pid,
      year
    }
  })
}

/**
 * 项目退回
 * @param pid 
 * @returns 
 */
export function backProject(pid: string) {
  return request<IResult<any>>({
    url: `/project/info/backProject`,
    method: 'put',
    params: {
      pid,
    }
  })
}



/**
 * 仅项目上报
 * @param pid 
 * @param year 
 * @returns 
 */
export function onlyReportProject(pid: string, year: number) {
  return request<IResult<any>>({
    url: `/project/info/onlyReportProject`,
    method: 'put',
    params: {
      pid,
      year
    }
  })
}



/**
 * 恢复项目
 * @param pid 
 * @param year 
 * @returns 
 */
export function recoveryProject(pid: string, year: number) {
  return request<IResult<any>>({
    url: `/project/info/recoveryProject/${pid}/${year}`,
    method: 'put',
  })
}

/**
 * 回收站删除项目
 * @param pid 
 * @param year 
 * @returns 
 */
export function removeTrash(pid: string, year: number) {
  return request<IResult<any>>({
    url: `/project/info/removeTrash/${pid}/${year}`,
    method: 'delete',
  })
}

/**
 * 项目和计划撤回
 * @param pid 
 * @param year 
 * @returns 
 */
export function withdrawProject(pid: string, year: number) {
  return request<IResult<any>>({
    url: `/project/info/withdrawProject`,
    method: 'put',
    params: {
      pid,
      year
    }
  })
}

/**
 * 仅项目撤回
 * @param pid 
 * @returns 
 */
export function onlyWithdrawProject(pid: string) {
  return request<IResult<any>>({
    url: `/project/info/onlyWithdrawProject`,
    method: 'put',
    params: {
      pid,
    }
  })
}


/**
 * 计划调整
 * @param data 
 * @returns 
 */
export function adjustPlan(data: any) {
  return request<IResult<any>>({
    url: `/project/info/adjustPlan`,
    method: 'put',
    data: data
  })
}

/**
 * 撤销计划调整
 * @param pid 
 * @returns 
 */
export function withdrawAdjustPlan(pid: string, year: number) {
  return request<IResult<any>>({
    url: `/project/info/withdrawAdjustPlan`,
    method: 'put',
    params: {
      pid,
      year
    }
  })
}

/**
 * 查询可复制项目列表
 */
export function getReproducibleProject(params: any) {
  return request<IResult<IProjectInfoVo[]>>({
    url: '/project/info/getReproducibleProject',
    method: 'get',
    params: params
  })
}

/**
 * 复制项目
 * @param pid 
 * @returns 
 */
export function copyProject(pid: string[], year: number) {
  const formData = new FormData()
  pid.forEach(id => formData.append('pid', id))
  formData.append('year', String(year))
  return request<IResult<any>>({
    url: `/project/info/copyProject`,
    method: 'put',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

/**
 * 查询用款计划表项目列表
 */
export function getProjectInfoOfPlan(params: any) {
  return request<IResult<IProjectInfoVo[]>>({
    url: '/project/info/getProjectInfoOfPlan',
    method: 'get',
    params: params
  })
}


