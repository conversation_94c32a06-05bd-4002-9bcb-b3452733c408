import request from '@/utils/request'

export function getFileList(sourceId: string) {
  return request<IResult<ISysAttachment[]>>({
    url: '/resource/file/list',
    method: 'get',
    params: {
      sourceId
    }
  })
}

export function getFileList1(sourceId: string, primaryType: string) {
  return request<IResult<ISysAttachment[]>>({
    url: '/resource/file/list',
    method: 'get',
    params: {
      sourceId,
      primaryType
    }
  })
}

export function removeFile(fileId: string) {
  return request<IResult<any>>({
    url: '/resource/file/' + fileId,
    method: 'delete'
  })
}

export function downloadFile(fileId: string) {
  return request<any>({
    url: '/resource/file/download/' + fileId,
    responseType: 'blob',
    method: 'get'
  })
}