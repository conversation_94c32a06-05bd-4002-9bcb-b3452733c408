import request from '@/utils/request'

/**
 * 查询项目管理列表
 */
export function getList(params: any) {
  return request<IResult<IProjectInfoVo[]>>({
    url: '/project/manage/list',
    method: 'get',
    params: params
  })
}

/**
 * 获取项目入库信息
 * @param pid 
 * @returns 
 */
export function getBasicInfo(pid: string) {
  return request<IResult<IProjectInfoVo>>({
    url: '/project/manage/' + pid,
    method: 'get',
  })
}

/**
 * 添加重点项目
 * @param pid 
 * @param year 
 * @returns 
 */
export function addImportantProject(pid: string, year: number) {
  return request<IResult<any>>({
    url: '/project/manage/addImportantProject',
    method: 'put',
    params: {pid, year}
  })
}

/**
 * 取消重点项目
 * @param pid 
 * @param year 
 * @returns 
 */
export function cancelImportantProject(pid: string, year: number) {
  return request<IResult<any>>({
    url: '/project/manage/cancelImportantProject',
    method: 'put',
    params: {pid, year}
  })
}




/**
 * 添加重点项目篮子
 * @param data 
 * @returns 
 */

export function addProjectBasket(data: Partial<IProjectBasketDto>) {
  return request<IResult<any>>({
    url: '/project/manage/addProjectBasket',
    method: 'put',
    data: data
  })
}

/**
 * 更新项目入库信息
 * @param data 
 * @returns 
 */

export function updateLibInfo(data: Partial<IProjectAmountDto>) {
  return request<IResult<any>>({
    url: '/project/manage/updateLibInfo',
    method: 'put',
    data: data
  })
}

/**
 * 取消项目入库
 * @param data 
 * @returns 
 */

export function cancelLib(pid: string) {
  return request<IResult<any>>({
    url: '/project/manage/cancelLib',
    method: 'put',
    params: {pid}
  })
}

/**
 * 更新项目核定信息
 * @param data 
 * @returns 
 */

export function updateCheckInfo(data: Partial<IProjectAmountDto>) {
  return request<IResult<any>>({
    url: '/project/manage/updateCheckInfo',
    method: 'put',
    data: data
  })
}

/**
 * 取消项目核定
 * @param data 
 * @returns 
 */

export function cancelCheck(pid: string) {
  return request<IResult<any>>({
    url: '/project/manage/cancelCheck',
    method: 'put',
    params: {pid}
  })
}

/**
 * 下达计划
 * @param data 
 * @returns 
 */

export function releasePlan(data: {pid: string, year: number, amount: number}) {
  return request<IResult<any>>({
    url: '/project/manage/releasePlan',
    method: 'put',
    params: data
  })
}

/**
 * 取消下达计划
 * @param data 
 * @returns 
 */

export function cancelRelease(data: {pid: string, year: number}) {
  return request<IResult<any>>({
    url: '/project/manage/cancelRelease',
    method: 'put',
    params: data
  })
}




/**
 * 双击下达计划
 * @param data 
 * @returns 
 */

export function doubleClickRelease(data: {pid: string, year: number, amount: number}) {
  return request<IResult<any>>({
    url: '/project/manage/doubleClickRelease',
    method: 'put',
    params: data
  })
}



/**
 * 调整计划
 * @param data 
 * @returns 
 */

export function adjustPlan(data: {pid: string, year: number, amount: number}) {
  return request<IResult<any>>({
    url: '/project/manage/adjustPlan',
    method: 'put',
    params: data
  })
}

/**
 * 取消调整计划
 * @param data 
 * @returns 
 */

export function cancelAdjust(data: {pid: string, year: number}) {
  return request<IResult<any>>({
    url: '/project/manage/cancelAdjust',
    method: 'put',
    params: data
  })
}

/**
 * 双击调整计划
 * @param data 
 * @returns 
 */

export function doubleClickAdjust(data: {pid: string, year: number, amount: number}) {
  return request<IResult<any>>({
    url: '/project/manage/doubleClickAdjust',
    method: 'put',
    params: data
  })
}