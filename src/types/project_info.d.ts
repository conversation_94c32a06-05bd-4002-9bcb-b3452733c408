interface ProjectQueryConditionVo {
    /**
     * 年份
     */
    year?: number;

    /**
     * 项目单位 0：查上报单位，1：考核单位
     */
    projectUnits?: string;

    /**
     * 项目名称
     */
    name?: string;

    /**
     * 项目类型
     */
    typeId?: string;

    /**
     * 项目性质代号
     */
    natureCode?: string;

    /**
     * 项目用途ids
     */
    purposeIds?: string[];

    /**
     * 是否涉及数字化园区
     */
    isSzhyq?: string;

    /**
     * 是否仅考核
     */
    onlyAssess?: string;
}

interface IProjectManageConditionVo {
        /**
     * 年份
     */
    year?: string;

    /**
     * 考核主体
     */
    assessOrgid?: string;

    /**
     * 项目名称
     */
    name?: string;

    /**
     * 已下达年初计划
     */
    hasEarlyAmount?: string;

    /**
     * 已下达调整计划
     */
    hasAdjustAmount?: string;

    /**
     * 项目类型
     */
    typeId?: string;

    /**
     * 项目性质代号
     */
    natureCode?: string;

    /**
     * 项目用途ids
     */
    purposeIds?: string[];

    /**
     * 是否涉及数字化园区（0否，1是）
     */
    isSzhyq?: string;
}

interface IProjectInfoVo {
    /**
     * 主键;主键，确保有序
     */
    id?: string;

    /**
     * 新增年度
     */
    year?: number;

    /**
     * 项目类型（1结转项目、2经常性项目、3新增项目）
     */
    projType?: 1|2|3;

    /**
     * 项目序号;项目顺序号，递增（历史数据应兼容）
     */
    seqid?: number;

    /**
     * 项目名称;项目名称
     */
    name?: string;

    /**
     * 项目正式名称;项目正式名称，优先于项目名称
     */
    formalName?: string;

    /**
     * 申报单位;申报单位ID
     */
    applyOrgid?: string;

    /**
     * 申报单位
     */
    applyOrgname?: string;

    /**
     * 申报时间;申报单位名称
     */
    applyTime?: string;

    /**
     * 上报单位;上报单位ID
     */
    submitOrgid?: string;

    /**
     * 上报时间;上报单位名称
     */
    submitTime?: string;

    /**
     * 项目使用单位;使用单位（文本，无ID，因为使用单位可能是项目管理之外的部门）
     */
    useOrgname?: string;

    /**
     * 考核单位;考核主体ID
     */
    assessOrgid?: string;

    /**
     * 考核单位
     */
    assessOrgname?: string;

    /**
     * 项目类型ID;项目类型id（字典表）
     */
    typeId?: string;

    /**
     * 项目类型名称;项目类型名称
     */
    typeName?: string;

    /**
     * 项目性质代号;项目性质代号（JC/JD）
     */
    natureCode?: string;

    /**
     * 项目性质名称;项目性质名称（经常性项目/阶段性项目）
     */
    natureName?: string;

    /**
     * 项目负责人;项目负责人
     */
    leader?: string;

    /**
     * 项目负责人联系电话;项目负责人联系电话（手机）
     */
    leaderTel?: string;

    /**
     * 项目经办人;项目经办人
     */
    handler?: string;

    /**
     * 项目经办人联系电话;项目经办人联系电话（手机）
     */
    handlerTel?: string;

    /**
     * 项目估算金额;项目估算金额，单位：万元（2位小数）
     */
    estAmount?: number;

    /**
     * 项目入库金额;项目入库金额，单位：万元（2位小数）
     */
    libAmount?: number;

    /**
     * 项目核定金额;项目核定金额，单位：万元（2位小数）
     */
    checkAmount?: number;

    /**
     * 项目合同金额;项目合同金额，单位：万元（2位小数）
     */
    contractAmount?: number;

    /**
     * 入库标记;入库标记，默认0，已入库为1
     */
    libState?: number;

    /**
     * 入库意见;入库意见，入库依据存附件表
     */
    libReason?: string;

    /**
     * 核定标记;核定标记，默认0，已核定为1
     */
    checkState?: number;

    /**
     * 核定意见;核定意见，核定依据存附件表
     */
    checkReason?: string;

    /**
     * 项目必要性;项目必要性
     */
    necessity?: string;

    /**
     * 项目依据;项目依据
     */
    basis?: string;

    /**
     * 项目主要内容;项目主要内容
     */
    mainCnt?: string;

    /**
     * 实施计划;实施计划
     */
    actionPlan?: string;

    /**
     * 建设周期（开始）;建设周期（开始）
     */
    buildBegin?: Date;

    /**
     * 建设周期（结束）;建设周期（结束）
     */
    buildEnd?: Date;

    /**
     * 资金周期（开始）;资金周期（开始）
     */
    fundBegin?: Date;

    /**
     * 资金周期（结束）;资金周期（结束）
     */
    fundEnd?: Date;

    /**
     * 是否涉及数字化园区支出;是否涉及数字化园区支出（0否，1是）
     */
    isSzhyq?: string;

    /**
     * 是否涉及精细化管理;是否涉及精细化管理（0否，1是）
     */
    isJxhgl?: string;

    /**
     * 是否涉及政府购买服务;是否涉及政府购买服务（0否，1是）
     */
    isZfgm?: string;

    /**
     * 项目总目标（绩效）;项目总目标（绩效）
     */
    goal?: string;

    /**
     * 项目篮子ID;项目篮子ID
     */
    basketCode?: string;

    /**
     * 项目篮子名称;项目篮子名称
     */
    basketName?: string;

    /**
     * 关联项目;关联项目（如果是复制的经常性项目，存储被复制的项目ID）
     */
    baseProjid?: string;

    /**
     * 删除标识;删除标识（0否，1是）
     */
    delFlag?: string;

    /**
     * 创建时间;创建时间
     */
    createTime?: Date;

    /**
     * 创建人;创建人，存储用户名
     */
    createBy?: string;

    /**
     * 更新时间;更新时间
     */
    updateTime?: Date;

    /**
     * 更新人;更新人，存储用户名
     */
    updateBy?: string;

    /**
     * 配合单位;配合单位，扩展为可支持多个，使用json存储，格式：[{"id":"1000","name":"经发处","seqid":"1"},{"id":"1001","name":"计财处","seqid":"2"}]
     */
    cooperateOrgid?: string;

    /**
     * 项目用途;项目用途，支持多选，使用json存储，格式：[{"id":"1000","name":"用途1","seqid":"1"},{"id":"1000","name":"用途2","seqid":"1"}]
     */
    purpose?: string;

    /**
     * 分解目标（绩效）;分解目标（绩效），json存储，格式：[{"sort":1,"name":"投入和管理目标",children:[{"sort":1,"name":"dsfdf","value":"dfsdfas"},{"sort":2,"name":"dsfdf","value":"dfsdfas"}]},{"sort":2,"name":"投入和管理目标",children:[{"sort":1,"name":"dsfdf","value":"dfsdfas"},{"sort":2,"name":"dsfdf","value":"dfsdfas"}]}]
     */
    goalDetail?: string;

    /** 调整审批状态（-1退回、0草稿、1已上报、2确认调整） */
    state?: EProjectState

    /**
     * 关联的年度信息集合
     */
    yearRelation?: IProjectYearRelation[];

    /**
     * 当前年度信息
     */
    currentYearRelation?: IProjectYearRelation?;

    /**
     * 项目编号信息
     */
    projectSn?: IProjectSn;

    /**
     * 资金计划
     */
    prevFundPlan?: IProjectPlan;

    /**
     * 资金计划
     */
    fundPlan?: IProjectPlan;

    /**
     * 项目绩效信息
     */
    performance?: IProjectPerformance[];

    /**
     * 附件列表
     */
    sysAttachments?: ISysAttachment[];

    /**
     * 用款计划表
     */
    usagePlanItem?: IUsagePlanItem
}


interface IProjectBasketDto {
    /**
     * 项目ID
     */
    projId: string;

    /**
     * 项目篮子ID
     */
    basketCode: string;

    /**
     * 项目篮子名称
     */
    basketName: string;

    /**
     * 项目入库金额，单位：万元（2位小数）
     */
    libAmount: number;

    /**
     * 是否发送短信，0-否，1-是
     */
    isSendSms?: string;
}

interface IProjectAmountDto {
    /**
     * 项目ID
     */
    projId: string;

    /**
     * 项目入库金额
     */
    amount: number;

    /**
     * 入库意见
     */
    reason: string;

    /**
     * 是否发送短信，0-否，1-是
     */
    isSendSms?: string;
}