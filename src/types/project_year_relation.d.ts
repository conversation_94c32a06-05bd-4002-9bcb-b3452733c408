interface IProjectYearRelation {
    /**
     * 主键;主键，确保有序
     */
    id?: string;

    /**
     * 年度;项目年度
     */
    year?: number;

    /**
     * 项目ID;项目ID
     */
    projId?: string;

    /**
     * 项目类型;项目类型（1结转项目、2经常性项目、3新增项目）
     */
    projType?: number;

    /**
     * 排序号;排序号，默认1
     */
    seqNo?: number;

    /**
     * 更新时间;更新时间（每日定时更新，归类）
     */
    updatedTime?: Date;

    /**
     * 回收站;回收站（0，1），默认为0，归类的结项项目，如果不执行，可标记放入回收站
     */
    isRecycle?: string;

    /**
     * 结项删除;结项删除（0，1），默认为0
     */
    isClose?: string;

    /**
     * 是否重点项目;是否重点项目（0，1），默认0
     */
    impProj?: string;

    /**
     * 重点项目类型;重点项目类型
     */
    impCode?: number;

    /**
     * 重点项目类型（名称）;重点项目类型（名称）
     */
    impName?: string;
}