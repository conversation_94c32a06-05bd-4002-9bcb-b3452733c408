interface IProjectPerformanceBase {
    /**
     * 主键;主键，无意义，自增
     */
    id?: number;

    /**
     * 一级指标;一级指标
     */
    indexLevel1?: string;

    /**
     * 二级指标;二级指标
     */
    indexLevel2?: string;

    /**
     * 三级指标;三级指标
     */
    indexLevel3?: string;

    /**
     * 指标性质;指标性质
     */
    natureName?: string;

    /**
     * 方向;方向
     */
    compareArrow?: string;

    /**
     * 计量单位;计量单位
     */
    measureUnit?: string;

    /**
     * 指标值说明;指标值说明
     */
    remark?: string;

    /**
     * 删除标记;删除标记（0否，1是）
     */
    delFlag?: string;

    /**
     * 排序号;排序号
     */
    indexSort?: number;
}