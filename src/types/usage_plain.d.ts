interface IUsagePlan {
    id: string;
    /** 年度 */
    year: number;
    /** 标题 */
    title: string;
    /** 表格单位 */
    unit: string;
    /** 列定义，使用json存储，定义列的显示和表头名称，[{"sort":1,"code":"xmmc","title":"项目名称","display":true},{"sort":2,"code":"xmlz","title":"项目篮子","display":true},{"sort":3,"code":"khzt","title":"考核主体","display":true}] */
    columns: string;
    /** 是否为快照（0否，1是） */
    isSnap: string;
    /** 快照名称 */
    snapName?: string;
    /** 创建时间 */
    createTime: string;
}