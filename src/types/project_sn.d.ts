interface IProjectSn {
    /**
     * 主键;主键，无意义，确保有序
     */
    id?: string;

    /**
     * 项目ID;project_info表外键
     */
    projId?: string;

    /**
     * 项目临时年度;项目临时年度
     */
    tempYear?: number;

    /**
     * 项目临时序号;项目临时序号（和project_info表中的seqid一致）
     */
    tempSeqid?: number;

    /**
     * 项目临时编号;项目临时编号，项目暂存后即生成，规则：临-年度4位-单位编号2位-项目顺序号4位（超出4位以实际位数）（临-2025-13-1931）
     */
    tempSn?: string;

    /**
     * 临时编号产生时间;临时编号产生时间，因临时编号在初次保存时即产生，该时间和初次保存时间一致
     */
    tempTime?: Date;

    /**
     * 项目正式年度;项目正式年度，首个资金计划发布年度
     */
    formalYear?: number;

    /**
     * 项目正式序号;项目正式编号，该年度已发布的资金计划排序号，从1开始
     */
    formalSeqid?: number;

    /**
     * 项目正式编号;项目正式编号，优先于临时编号，项目发布后生成，规则：年度4位-部门编号2位-项目类型2位-项目性质2位-年度发布顺序号3位（2025-13-B2-JC-027）
     */
    formalSn?: string;

    /**
     * 正式编号产生时间;正式编号产生时间，即首个资金计划发布的时间
     */
    formalTime?: Date;
}