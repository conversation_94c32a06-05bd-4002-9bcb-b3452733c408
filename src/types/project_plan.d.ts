interface IProjectPlan {
    /**
     * 主键;主键，确保有序
     */
    id?: string;

    /**
     * 项目ID;项目ID
     */
    projId?: string;

    /**
     * 资金计划年度;资金计划年度
     */
    year?: number;

    /**
     * 计划状态;计划状态（-1退回申报人、0草稿、1已上报、2已受理、3已分送、4已审批、9已发布）
     */
    planState?: EFundPlanState;

    /**
     * 上报计划类型;上报计划类型（年初计划、调整计划）
     */
    planType?: string;

    /**
     * 上报计划金额;上报计划金额
     */
    declareAmount?: number;

    /**
     * 上报人;上报人
     */
    declareUserid?: string;

    /**
     * 上报时间;上报时间
     */
    declareTime?: Date;

    /**
     * 年初计划金额;年初计划金额（经发处下达）
     */
    earlyAmount?: number;

    /**
     * 正式计划金额;正式计划金额（经发处下达）
     */
    formalAmount?: number;

    /**
     * 调整计划金额;调整计划金额（经发处下达）
     */
    adjustAmount?: number;

    /**
     * 计划描述;计划描述
     */
    describe?: string;

    /**
     * 计划篮子ID;计划篮子ID（10待定、20原已定、25新增已定、30预备金）
     */
    basketId?: number;

    /**
     * 计划篮子名称;计划篮子名称（10待定、20原已定、25新增已定、30预备金）
     */
    basketName?: string;

    /**
     * 申请调整金额;申请调整金额
     */
    applyAdjust?: number;

    /**
     * 申请调整理由;申请调整理由
     */
    applyReason?: string;

    /**
     * 调整审批状态;调整审批状态（-1退回、0草稿、1已上报、2确认调整）
     */
    applyState?: EApplyAdjustState;

    /**
     * 调整申请人;调整申请人
     */
    applyUserid?: string;

    /**
     * 调整申请时间;调整申请时间
     */
    applyTime?: Date;

    /**
     * 调整退回理由;调整退回理由
     */
    applyRefused?: string;

    /**
     * 资金计划明细;资金计划明细，格式：[{"sort":1,"title":"项目内容","estAmount":"项目估算(decimal)","executedAmount":"截至上年底已执行资金(decimal)","planAmount":"当年上报计划(decimal)","describe":"项目建设周期及进度说明","payPercent":"合同支付比例，[0,100]表示支付合同的0%至100%，[30,60]表示支付合同的30%至60%"}]
     */
    planDetail?: string;
}

interface IProjectPlanItem {
    title: string
    estAmount: number
    executedAmount: number
    planAmount: number
    describe: string
    payPercent: number[]
    sort: number
}