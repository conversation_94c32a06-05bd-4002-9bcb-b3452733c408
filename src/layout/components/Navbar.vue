<template>
  <div class="navbar">
    <top-nav id="topmenu-container" class="topmenu-container" v-if="settingsStore.topNav" />

    <div class="right-menu">
      <template v-if="appStore.device !== 'mobile'">


        <!-- <el-tooltip content="源码地址" effect="dark" placement="bottom">
          <ruo-yi-git id="ruoyi-git" class="right-menu-item hover-effect" />
        </el-tooltip>

        <el-tooltip content="文档地址" effect="dark" placement="bottom">
          <ruo-yi-doc id="ruoyi-doc" class="right-menu-item hover-effect" />
        </el-tooltip> -->

        <!-- <screenfull id="screenfull" class="right-menu-item hover-effect" /> -->

        <!-- <el-tooltip content="主题模式（白天/黑夜）" effect="dark" placement="bottom">
          <div class="right-menu-item hover-effect theme-switch-wrapper" @click="toggleTheme">
            <svg-icon v-if="settingsStore.isDark" icon-class="sunny" />
            <svg-icon v-if="!settingsStore.isDark" icon-class="moon" />
          </div>
        </el-tooltip> -->

        <!-- <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip> -->

        <el-tooltip content="消息提醒" effect="dark" placement="bottom">
          <div class="right-menu-item hover-effect theme-switch-wrapper" @click="messageOpen = true">
            <el-badge v-if="badgeCount > 0" :value="badgeCount" :max="99" class="item mr-[10px]" :offset="[0, 15]">
              <el-icon style="margin-top: 18px;" class="text-white">
                <BellFilled />
              </el-icon>
            </el-badge>
            <el-icon v-else>
              <BellFilled />
            </el-icon>
          </div>
        </el-tooltip>

        <!-- <span style="font-size: 14px; color:cornflowerblue; font-weight: 500;">{{ userStore.name }}</span> -->
      </template>
      <el-tooltip :content="userStore.name">
        <div class="avatar-container">
          <el-dropdown @command="handleCommand" class="right-menu-item hover-effect" trigger="click">
            <div class="avatar-wrapper">
              <img :src="userStore.avatar" class="user-avatar" />
              <!-- <el-icon><caret-bottom /></el-icon> -->
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <router-link to="/user/profile">
                  <el-dropdown-item>个人中心</el-dropdown-item>
                </router-link>
                <el-dropdown-item command="setLayout" v-if="settingsStore.showSettings">
                  <span>布局设置</span>
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-tooltip>
    </div>
    <Teleport to="body">
      <el-drawer v-model="messageOpen" title="消息提醒" direction="rtl" size="500px">
        <transition-group name="message-list" tag="div" class="flex flex-col gap-3">
          <el-card v-for="(item, index) in messageList" :key="item.id">
            <template #header>
              <div class="flex justify-between py-2">
                <span>{{ dayjs().format('YYYY-MM-DD HH:mm:ss') }}</span>
                <div>
                  <el-tag type="danger" class="cursor-pointer" @click="messageList.splice(index, 1)">未读</el-tag>
                </div>
              </div>
            </template>
            <p>您有一条未读消息{{ item.id }}</p>
          </el-card>
        </transition-group>
        <template #footer>
          <div class="flex">
            <el-button type="primary" @click="messageList.length = 0">全部已读</el-button>
          </div>

        </template>
      </el-drawer>
    </Teleport>

  </div>
</template>

<script setup>
import { dayjs, ElMessageBox } from 'element-plus'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'

// import Screenfull from '@/components/Screenfull'
// import SizeSelect from '@/components/SizeSelect'
import HeaderSearch from '@/components/HeaderSearch'
// import RuoYiGit from '@/components/RuoYi/Git'
// import RuoYiDoc from '@/components/RuoYi/Doc'
import useAppStore from '@/store/modules/app'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
import { ref } from 'vue'
import settings from '@/settings'
import { casConfig, loadCasConfig } from '@/casseting';

const appStore = useAppStore()
const userStore = useUserStore()
const settingsStore = useSettingsStore()

const badgeCount = ref(999)

// console.log(userStore.name)



function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout();
      break;
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

function logout() {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    /*    userStore.logOut().then(() => {
          location.href = '/index';
        })*/
    loadCasConfig().then(() => {
      const isCasEnabled = casConfig.value.casEnable === '1';
      if (isCasEnabled) {
        // window.location.href = settings.casLogoutUrl;
        userStore.logOut().then(() => {
          window.location.href = settings.casLogoutUrl;
        });
      } else {
        userStore.logOut().then(() => {
          window.location.href = import.meta.env.VITE_BASE_URL;
        });
      }
    })
  }).catch(() => { });
}

const emits = defineEmits(['setLayout'])
function setLayout() {
  emits('setLayout');
}

function toggleTheme() {
  settingsStore.toggleTheme()
}

const messageList = ref([{ id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }, { id: 5 }, { id: 6 }, { id: 7 }, { id: 8 }, { id: 9 }])
const messageOpen = ref(false)
const handleMessageOpen = () => {
  messageOpen.value = true

}
</script>

<style lang='scss' scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;



  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 62px;
    display: flex;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: var(--navbar-text);
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }

      &.theme-switch-wrapper {
        display: flex;
        align-items: center;

        svg {
          transition: transform 0.3s;

          &:hover {
            transform: scale(1.15);
          }
        }
      }
    }

    .avatar-container {
      margin-right: 10px;

      .avatar-wrapper {
        margin-top: 0px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 36px;
          height: 36px;
          border-radius: 50%;
        }

        i {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}

.message-list-enter-active,
.message-list-leave-active {
  transition: all 0.5s ease;
}

.message-list-enter,
.message-list-leave-to {
  opacity: 0;
  transform: translateX(20px);
}
</style>
