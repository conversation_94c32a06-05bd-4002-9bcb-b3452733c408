<template>
</template>

<script setup>
import { validateLogin } from '@/api/cas';
import { getToken, setToken, removeToken } from '@/utils/auth';
import { useRouter } from 'vue-router';
import settings from '@/settings';

const service = settings.appLoginUrl;

const getQueryValue = (queryName) => {
  var query = decodeURI(window.location.search.substring(1));
  var vars = query.split("&");
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split("=");
    if (pair[0] == queryName) { return pair[1]; }
  }
  return '';
};
console.log('getQueryValue='+getQueryValue);
const ticket = getQueryValue('ticket');
console.log('ticket='+ticket);
const router = useRouter();

validateLogin(ticket, service).then(res => {
       setToken(res.token);
       router.push('/');
      }).catch(error => {
       console.error('Login failed:', error);
      });
</script>