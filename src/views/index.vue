<template>
  <div class="app-container" style="background-color: #F5FAFF;">
    <el-row :gutter="20">
      <el-col :span="12">
        <div class="p-[3px] bg-white rounded-[8px] box-card" style="height: 270px; overflow-y: auto;">
          <div class="bg rounded-[5px] p-[15px]">
            <div class="flex justify-between relative ">
              <div class="flex">
                <div class="bg-[#F1F5FC] w-[100px] h-[100px] flex justify-center items-center">
                  <span class="bg-[#007AFF] w-[60px] h-[60px] rounded-full flex justify-center items-center overflow-hidden"><img
                      src="../assets/images/yonghuming.png" /></span>
                </div>
                <h1 class="text-lg ml-[10px] text-[#444]">庄XXX，欢迎您访问本系统<br /><span class="text-base leading-[40px]">部门：专项部门</span></h1>
              </div>
              <div class="absolute right-0 top-[-10px]"><img src="../assets/images/index_pic1.png" /></div>
            </div>
            <div>
              <h1 class="text-base pb-[10px] pt-[10px] mb-0 border-t mt-[20px]">待办事项</h1>
              <div class="grid grid-cols-8 gap-3 text-[14px] text-[#666]">
                <span class="bg-[#FEDFE3] text-center p-[10px] rounded-[3px] cursor-pointer hover:shadow-md"><em
                    class="text-[#F54C7A] text-[24px] not-italic">45</em><br />待受理</span>
                <span class="bg-[#FFF2DC] text-center p-[10px] rounded-[3px] cursor-pointer hover:shadow-md"><em
                    class="text-[#FD9179] text-[24px] not-italic">12</em><br />待审批</span>
                <span class="bg-[#DBFBE5] text-center p-[10px] rounded-[3px] cursor-pointer hover:shadow-md"><em
                    class="text-[#47DB55] text-[24px] not-italic">13</em><br />退回</span>
                <span class="bg-[#F1E5FF] text-center p-[10px] rounded-[3px] cursor-pointer hover:shadow-md"><em
                    class="text-[#B76CFF] text-[24px] not-italic">16</em><br />待提交</span>
                <span class="bg-[#DCF5FF] text-center p-[10px] rounded-[3px] cursor-pointer hover:shadow-md"><em
                    class="text-[#438AFC] text-[24px] not-italic">18</em><br />待计划</span>
                <span class="bg-[#FFE2E4] text-center p-[10px] rounded-[3px] cursor-pointer hover:shadow-md"><em
                    class="text-[#FF4B23] text-[24px] not-italic">20</em><br />待受理</span>
                <span class="bg-[#DBEBFF] text-center p-[10px] rounded-[3px] cursor-pointer hover:shadow-md"><em
                    class="text-[#318AD9] text-[24px] not-italic">23</em><br />待受理</span>
                <span class="bg-[#FFF2DC] text-center p-[10px] rounded-[3px] cursor-pointer hover:shadow-md"><em
                    class="text-[#FF7D61] text-[24px] not-italic">28</em><br />待受理</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <el-card class="box-card relative" style="height: 270px; overflow-y: auto; padding: 0!important;">
          <template v-slot:header>
            <div slot="header" class="clearfix ">
              <span class="tit">通知公告</span>
              <el-button style="margin-top:-5px ;float:right" type="text">更多>></el-button>
            </div>
          </template>
          <div class="absolute bottom-0 right-0"><img src="../assets/images/index_pic2.png"/></div>
          <div v-for="n in 5" :key="n" class="gg flex justify-between text-[14px]">
            <a href="#" class="line-clamp-1 w-[70%] pl-[12px] hover:text-[#007AFF]">
              化工区公共区域配套设施综合整治提升工区公共区域配套设施综合整治提升工区公共区域配套设施综合整治提升项目（二期）
            </a>
            <em class="not-italic text-[#666]">
              2025-05-14
            </em>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card class="box-card" style="height: 550px; overflow-y: auto;">
          <template v-slot:header>
            <div slot="header" class="clearfix">
              <span class="tit">预算执行排名情况</span>
              
               <el-button style="margin-top:-5px ;float:right" type="text">更多>></el-button>
            </div>
          </template>
          <div class="mr-[10px] overflow-hidden">
            <img src='../assets/images/image1.png' style="object-fit: contain; height: 400px;width: 100%;"/>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="box-card" style="height: 550px; overflow-y: auto; ">
          <template v-slot:header>
            <div slot="header" class="clearfix">
             
               <span class="tit">项目库与资金统计</span>
               <el-button style="margin-top:-5px ;float:right" type="text">更多>></el-button>
            </div>
          </template>
          <div class="mr-[10px] overflow-hidden">
            <img src='../assets/images/image2.png' style="object-fit:contain; height: 400px;width: 100%;"/>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Index">
import { spaceItemProps } from 'element-plus';

const activeTab = ref('first')
const count1 = ref(100)
const count2 = ref(0)

const version = ref('3.8.9')

function goTarget(url) {
  window.open(url, '__blank')
}
</script>

<style scoped lang="scss">
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }

  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }

  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans",
  "Helvetica Neue",
  Helvetica,
  Arial,
  sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}

.bg {
  background: linear-gradient(90deg, #FFFFFF 0%, #EBF2FC 100%);
}

.border-t {
  border-top: solid 1px #D0DAEA;
}

.gg {
  border-bottom: solid 1px #ECECEC;
  margin-bottom: 10px;
  position: relative;
  line-height: 32px;
  height: 32px;
  overflow: hidden;
}

.gg::before {
  content: '';
  width: 5px;
  height: 5px;
  top: 12px;
  position: absolute;
  background-color: #C1DAE5;
  border-radius: 100%;
}
.tit{display:inline-block;position: relative; padding-left:10px; font-weight: 500;}
.tit::before{content: '';position: absolute; width: 4px; height:14px; background-color: #007AFF; border-radius: 10px; top:4px; left:0;}
.box-card{box-shadow: 0px 0px 5px rgba(0,0,0,0.05); border:none;}
</style>
