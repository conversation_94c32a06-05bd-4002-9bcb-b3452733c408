<template>
  <div class="login">
    <div class="login-container">
      <div class="login-image">
        <!-- <h2 style="color: #fff; text-align: center;line-height: 50px; font-weight: bolder;">上海化工区<br />专项发展资金项目管理平台</h2>
        <p style="color: #fff; text-align: center;font-size:16px">整合原项目库和资金拨付系统，使项目和资金执行深度整合。</p> -->
      </div>
      <div class="login-formbox">
        <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
          <h3 class="title">账号登录</h3>
          <div style="margin: 50px 0 0 0;">
            <el-form-item prop="username">
              <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off" placeholder="账号">
                <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input v-model="loginForm.password" type="password" size="large" auto-complete="off" placeholder="密码"
                @keyup.enter="handleLogin">
                <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
              </el-input>
            </el-form-item>
            <el-form-item prop="code" v-if="captchaEnabled" style="position: relative;">
              <el-input v-model="loginForm.code" size="large" auto-complete="off" placeholder="验证码" @keyup.enter="handleLogin">
                <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
              </el-input>
              <div class="login-code">
                <img :src="codeUrl" @click="getCode" class="login-code-img" />
              </div>
            </el-form-item>
            <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
            <el-form-item style="width:100%; margin-top: 20px;">
              <el-button :loading="loading" size="large" type="primary" style="width:100%;"
                @click.prevent="handleLogin">
                <span v-if="!loading">登 录</span>
                <span v-else>登 录 中...</span>
              </el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
  </div>
  <!--  底部  -->
  <div class="el-login-footer" style="color:#666;">
    <span>Copyright © 2025 www.scip.gov.cn All Rights Reserved.</span>
  </div>
</template>

<script setup>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
import defaultSettings from '@/settings';
import {casConfig,loadCasConfig } from '@/casseting';

/**
 * login页面，如果判断为单点登录，则跳转到cas登录页面
 */
loadCasConfig().then(() => {
  const isCasEnabled = casConfig.value.casEnable === '1';
  if (isCasEnabled) {
    window.location.href = defaultSettings.casLoginUrl;
  }
})

const loginForm = ref({
  username: "",
  password: "",
  rememberMe: false,
  code: "",
  uuid: ""
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }]
};

const codeUrl = ref("");
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

watch(route, (newRoute) => {
  redirect.value = newRoute.query && newRoute.query.redirect;
}, { immediate: true });

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 30 });
        Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 });
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      // 调用action的登录方法
      userStore.login(loginForm.value).then(() => {
        const query = route.query;
        const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
          if (cur !== "redirect") {
            acc[cur] = query[cur];
          }
          return acc;
        }, {});
        router.push({ path: redirect.value || "/", query: otherQueryParams });
      }).catch(() => {
        loading.value = false;
        // 重新获取验证码
        if (captchaEnabled.value) {
          getCode();
        }
      });
    }
  });
}

function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img;
      loginForm.value.uuid = res.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  };
}

getCode();
getCookie();
</script>

<style lang='scss' scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh);
  background-image: url("../assets/images/login_bg.png");
  background-size: cover;
}

.title {
  margin: 0px auto 30px auto;
  text-align: left;
  color: #363636;
  font-weight: bold;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;

  .el-input {
    height: 40px;

    input {
      height: 40px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  position: absolute;
  right: 0;
  height: 40px;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 40px;
  padding-left: 12px;
}
</style>

<style scoped>
.login-container {
  width: 900px;
  height: 450px;
  margin: 0 auto;
  box-shadow: 0 2px 5px 1px rgba(21,68,127,0.08);
  position: relative;
  
  /* transform: translate(-50%) translateY(-50%);
  -webkit-transform: translateX(-50%) translateY(-50%);  */
}

.login-image {
  position: absolute;
  width: 500px;
  height: 450px;
  /* background-color: #0198cd; */
  background-image: url(../assets/images/login_ad_01.png);
  background-size: cover;
  padding-top: 110px;
  font-size:20px;
}

.login-formbox {
  width: 400px;
  height: 450px;
  position: absolute;
  right: 0;
  box-sizing: border-box;
  background: #fff;
  box-shadow: 2px 9px 49px -17px #0000001a;
}
</style>
