<template>
    <el-form label-width="auto">
        <el-form-item label="年初计划上报时间规则">
            <div>
                <el-select v-model="rule1" class="w-25" placeholder="" :clearable="false">
                    <el-option label="上一年" value="p"></el-option>
                    <el-option label="当年" value="c"></el-option>
                </el-select>
                <el-date-picker v-model="ruleDate1" type="date" class="!w-40" placeholder="选择日期" format="MM-DD"
                    value-format="MM-DD" :clearable="false" />
            </div>
            <span class="mx-1">--</span>
            <div>
                <el-select v-model="rule2" class="w-25" placeholder="" :clearable="false">
                    <el-option label="上一年" value="p"></el-option>
                    <el-option label="当年" value="c"></el-option>
                </el-select>
                <el-date-picker v-model="ruleDate2" type="date" class="!w-40" placeholder="选择日期" format="MM-DD"
                    value-format="MM-DD" :clearable="false" />
            </div>
            <span class="text-red">（”当年“和”上一年“是相对项目执行年度）</span>
        </el-form-item>
        <el-form-item label="资金计划调整通道">
            <el-radio-group v-model="ajdust">
                <el-radio value="0">关闭</el-radio>
                <el-radio value="1">开启</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="双击发布联动（用款计划表）">
            <el-radio-group v-model="releasePlan">
                <el-radio value="0">初步计划</el-radio>
                <el-radio value="1">正式计划</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item>
            <div class="ml-50">
                <el-button type="primary" @click="save">保存设置</el-button>
            </div>
        </el-form-item>
    </el-form>
</template>

<script setup lang="ts">
import { useConfigStore } from '@/store/modules/config'
import { useConfig } from '@/utils/config'
// @ts-ignore
import { adjustConfig } from '@/api/system/config'
import { ElMessage } from 'element-plus'

const {
    ["sys.plan.submit.period"]: planSubmitPeriod,
    ["sys.click.releasePlan"]: clickReleasePlan,
    ["sys.plan.ajdust"]: planAjdust
} = useConfig('sys.plan.submit.period', 'sys.click.releasePlan', 'sys.plan.ajdust')

const rule1 = ref('')
const ruleDate1 = ref()
const rule2 = ref('')
const ruleDate2 = ref()
const releasePlan = ref<string>()
const ajdust = ref<string>()

watchEffect(() => {
    if (planSubmitPeriod.value) {
        const arr = planSubmitPeriod.value.split(',')
        rule1.value = arr[0].split(':')[0]
        ruleDate1.value = arr[0].split(':')[1]

        rule2.value = arr[1].split(':')[0]
        ruleDate2.value = arr[1].split(':')[1]
    }
    if (clickReleasePlan.value) {
        releasePlan.value = clickReleasePlan.value
    }
    if (planAjdust.value) {
        ajdust.value = planAjdust.value
    }
})



const save = () => {
    const data = [
        {
            key: 'sys.plan.submit.period',
            value: `${rule1.value}:${ruleDate1.value},${rule2.value}:${ruleDate2.value}`
        },
        {
            key: 'sys.click.releasePlan',
            value: releasePlan.value
        },
        {
            key: 'sys.plan.ajdust',
            value: ajdust.value
        }
    ]
    adjustConfig(data).then(() => {
        ElMessage.success('保存成功');
        useConfigStore().configMap = {}
    })
    
}
</script>