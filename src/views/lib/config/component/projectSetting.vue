<template>
    <div>
        <el-form label-width="auto" class="flex gap-3 flex-wrap">
            <el-form-item>
                <el-input v-model="searchForm.name" placeholder="请输入项目名称" class="w-50"></el-input>
            </el-form-item>
            <el-form-item>
                <el-date-picker v-model="searchForm.year" type="year" placeholder="请选择年度" class="!w-30"
                    :clearable="false" value-format="YYYY"></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-select v-model="searchForm.typeId" placeholder="请选择项目类别" class="w-40">
                    <el-option v-for="item in project_type" :label="item.remark" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select v-model="searchForm.purposeIds" multiple placeholder="请选择项目用途" class="w-40">
                    <el-option v-for="item in project_purpose" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-checkbox v-model="searchForm.isSzhyq" :true-value="'1'" :false-value="''">涉及数字化园区</el-checkbox>
            </el-form-item>
            <el-form-item>
                <el-checkbox v-model="searchForm.isJxhgl" :true-value="'1'" :false-value="''">涉及精细化管理支出</el-checkbox>
            </el-form-item>
            <el-form-item>
                <el-checkbox v-model="searchForm.isZfgm" :true-value="'1'" :false-value="''">涉及政府购买服务</el-checkbox>
            </el-form-item>
        </el-form>
    </div>
    <div>
        <el-table :data="dataList" border v-loading="loading">
            <el-table-column type="index" label="序号" align="center" width="60px"></el-table-column>
            <el-table-column label="项目名称" align="center"></el-table-column>
            <el-table-column label="申报单位" align="center" width="100px"></el-table-column>
            <el-table-column label="项目周期" align="center" width="150px"></el-table-column>
            <el-table-column label="状态" align="center" width="100px"></el-table-column>
            <el-table-column label="项目类型" align="center" width="180px">
                <template #default="{ row }">
                    <el-select v-model="searchForm.typeId" placeholder="请选择项目类别">
                        <el-option v-for="item in project_type" :label="item.remark" :value="item.value"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="项目用途" align="center" width="180px">
                <template #default="{ row }">
                    <el-select v-model="searchForm.purposeIds" multiple placeholder="请选择项目用途">
                        <el-option v-for="item in project_purpose" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="涉及数字化园区支出" align="center" width="150px">
                <template #default="{ row }">
                    <el-checkbox :true-value="'1'" :false-value="''"></el-checkbox>
                </template>
            </el-table-column>
            <el-table-column label="涉及精细化管理支出" align="center" width="150px">
                <template #default="{ row }">
                    <el-checkbox :true-value="'1'" :false-value="''"></el-checkbox>
                </template>
            </el-table-column>
            <el-table-column label="涉及政府购买服务" align="center" width="150px">
                <template #default="{ row }">
                    <el-checkbox :true-value="'1'" :false-value="''"></el-checkbox>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="暂无数据" />
            </template>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
            @pagination="search" />
    </div>
</template>

<script setup lang="ts">
import { dayjs } from 'element-plus';

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_type, project_basket_type } = proxy.useDict("project_purpose", "project_nature", 'project_type', 'project_basket_type');

const page = reactive({
    pageNum: 1,
    pageSize: 10,
})
const searchForm = reactive({
    year: '',
    name: '',
    natureCode: '',
    basketCode: '',
    typeId: '',
    purposeIds: [],
    isSzhyq: '',
    isJxhgl: '',
    isZfgm: '',
})
const loading = ref(false)
const total = ref(0)
const dataList = ref([{}])
const search = () => {

}
watch(searchForm, () => {
    search()
}, { immediate: true })
</script>