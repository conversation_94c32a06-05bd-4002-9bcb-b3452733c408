<template>
    <div class="flex flex-col items-center justify-center gap-2">
        <div>
            <div class="text-black">您即将把项目移入“<span class="text-gray-500">申报库</span>”，</div>
            <div class="text-black">请设置入库金额（单位：万元）：</div>
        </div>
        <el-input-number v-model="form.libAmount" placeholder="请输入入库金额" class="w-50" :min="0" controls-position="right"></el-input-number>
        <el-checkbox v-model="form.isSendSms" :true-value="'1'" :false-value="''">发送短信通知申报人</el-checkbox>
    </div>
    <div class="flex justify-center mt-5">
        <el-button type="primary" @click="save">确定</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
    </div>
</template>

<script setup lang="ts">
import { addProjectBasket, getBasicInfo } from '@/api/fund/project/manage';
import { ElMessage } from 'element-plus';
import { sumAmount, checkIsNumber } from '@/utils/common';

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_basket_type } = proxy.useDict('project_basket_type');


const emit = defineEmits(['close', 'cancel'])
// 申报表单
const props = defineProps<{
    projectId: string
}>()

const form = reactive({
    libAmount: 0,
    isSendSms: '1'
})
getBasicInfo(props.projectId).then(res => {
    if (checkIsNumber(res.data?.libAmount)) {
        form.libAmount = res.data?.libAmount
    } else {
        form.libAmount = res.data?.estAmount ?? 0
    }
})

const getBasketInfo = computed(() => {
    const basketInfo = project_basket_type.value.find((t: any) => t.label == '申报库')
    return {
        basketCode: basketInfo?.value,
        basketName: basketInfo?.label
    }
})

const save = () => {
    if (!getBasketInfo.value?.basketCode) {
        ElMessage.error('未查询到库名信息')
        return
    }
    addProjectBasket({
        projId: props.projectId,
        basketCode: getBasketInfo.value.basketCode,
        basketName: getBasketInfo.value.basketName,
        isSendSms: form.isSendSms,
        libAmount: form.libAmount
    }).then(res => {
        ElMessage.success('入库成功')
        emit('close')
    })
}
</script>