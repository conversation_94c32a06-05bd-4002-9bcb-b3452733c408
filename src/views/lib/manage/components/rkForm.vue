<template>
    <el-form ref="formRef" :model="form" :rules="formRules" class="flex flex-col gap-3" :inline-message="true">
        <el-descriptions :column="1" label-width="20%" border>
            <el-descriptions-item label="入库操作" width="80%">
                <div class="grid grid-cols-[50%_1fr] items-center">
                    <span>入库</span>
                    <el-checkbox v-model="form.isSendSms" :true-value="'1'" :false-value="''">发送短信通知</el-checkbox>
                </div>
            </el-descriptions-item>
            <el-descriptions-item label="入库金额">
                <el-form-item prop="amount" class="!mb-0">
                    <el-input-number v-model="form.amount" controls-position="right"></el-input-number>
                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item label="入库意见">
                <el-form-item prop="amount" class="!mb-0">
                    <el-input v-model="form.reason" type="textarea" rows="5"></el-input>
                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item label="入库依据">
                <my-file-upload :accept="fileType" :data="{
                    sourceId: props.projectId,
                    sourceType: EFileSoureType.PROJECT,
                    primaryType: primaryType.type,
                    primaryTypeName: primaryType.name
                }" @upload-success="searchFileList"></my-file-upload>
                <div class="mt-2">
                    <FileTable  :file-list="fileList" @update="searchFileList" :hiddenColumns="['index', 'primaryType']"></FileTable>
                </div>
            </el-descriptions-item>
        </el-descriptions>
    </el-form>
    <div class="flex justify-center mt-5">
        <el-button type="primary" @click="save">确定</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
    </div>
</template>

<script setup lang="ts">
// 入库表单
import { getFileList, getFileList1, removeFile } from '@/api/fund/project/file';
import { getBasicInfo, updateLibInfo } from '@/api/fund/project/manage';
import { dateFormat } from '@/utils/common';
import { EFilePrimaryType, EFileSoureType } from '@/utils/constants';
import { ElMessage, ElMessageBox, FormInstance, FormRules } from 'element-plus';
import FileTable from '@/components/Table/FileTable.vue';

const emit = defineEmits(['close', 'cancel'])

const props = defineProps<{
    projectId: string
}>()

const { proxy } = getCurrentInstance() as { proxy: any };
const { ["sys.upload.fileType"]: fileType } = proxy.useConfig("sys.upload.fileType");

const primaryType = { type: "入库依据", name: '入库依据' }
const fileList = ref<ISysAttachment[]>([]);
const searchFileList = () => {
    getFileList1(props.projectId, primaryType.type).then(res => {
        fileList.value = res.data ?? [];
    });
};
searchFileList()

const form = reactive({
    amount: 0,
    isSendSms: '1',
    reason: ''
})

getBasicInfo(props.projectId).then(res => {
    form.amount = res.data?.libAmount ?? 0

})

const formRules = reactive<FormRules<typeof form>>({
    amount: [
        { required: true, message: '请输入核定金额', trigger: 'blur' }
    ],
    reason: [
        { required: true, message: '请输入核定意见', trigger: 'blur' }
    ],
})
const formRef = ref<FormInstance>()

const save = () => {
    updateLibInfo({
        projId: props.projectId,
        amount: form.amount,
        isSendSms: form.isSendSms,
        reason: form.reason
    }).then(res => {
        ElMessage.success('入库成功');
        emit('close')
    })
}

</script>