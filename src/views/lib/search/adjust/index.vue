<template>
    <div class="app-container">
        <div>
            <el-form :inline="true" :model="queryForm" class="mb-4" label-width="120px">
                <el-form-item label="项目名称">
                    <el-input v-model="queryForm.projectName" placeholder="请输入项目名称" class="w-50" />
                </el-form-item>
                <el-form-item label="申报单位">
                    <el-select v-model="queryForm.applyOrg" placeholder="--全部--" class="w-50">
                        <el-option label="全部" value="" />
                    </el-select>
                </el-form-item>
                <el-form-item label="配合单位">
                    <el-input v-model="queryForm.partnerUnit" placeholder="输入单位名" class="w-50" />
                </el-form-item>

                <el-form-item label="项目性质">
                    <el-select v-model="queryForm.natureCode" placeholder="请选择项目性质" class="w-50">
                        <el-option v-for="item in project_nature" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="项目类别">
                    <el-select v-model="queryForm.typeId" placeholder="请选择项目类别" class="w-50">
                        <el-option v-for="item in project_type" :label="item.remark" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="项目用途">
                    <el-select v-model="queryForm.purposeIds" multiple placeholder="请选择项目用途" class="w-50">
                        <el-option v-for="item in project_purpose" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="审批状态">
                    <el-select v-model="queryForm.status" placeholder="--全部--" class="w-50">
                        <el-option label="全部" value="" />
                        <el-option label="待操作" value="待操作" />
                        <el-option label="已入库" value="已入库" />
                    </el-select>
                </el-form-item>
                <el-form-item label="执行年度">
                    <el-date-picker v-model="queryForm.year" type="year" value-format="YYYY" class="!w-25" />
                    <el-date-picker v-model="queryForm.year" type="year" value-format="YYYY" class="!w-25" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">查询</el-button>
                </el-form-item>
            </el-form>
        </div>

        <el-table :data="dataList" border v-loading="loading">
            <el-table-column prop="index" label="序号" width="60" align="center" />
            <el-table-column label="项目名称" align="center">
                <template #default="{ row }">
                    <div>
                        <div>{{ row.name }}</div>
                        <div class="text-xs text-gray-500">编号：{{ row.code }}</div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="unit" label="申报单位" align="center" />
            <el-table-column prop="amount" label="项目用途" align="center" />
            <el-table-column prop="depositAmount" label="年度" width="120" align="center" />
            <el-table-column prop="finalAmount" label="当年计划" align="center" />
            <el-table-column prop="period" label="计划调整" align="center" />
            <el-table-column prop="status" label="调整理由" align="center" />
            <el-table-column prop="basket" label="回退理由" align="center" />
            <el-table-column prop="basket" label="状态" width="120" align="center" />
            <el-table-column label="操作" width="180" align="center">
                <template #default>
                    <el-button type="primary" link icon="view">查看</el-button>
                    <el-button type="primary" link icon="guide">流程状态</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="暂无数据" />
            </template>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
            @pagination="search" />
    </div>

</template>

<script lang="ts" setup>

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_type, project_basket_type } = proxy.useDict("project_purpose", "project_nature", 'project_type', 'project_basket_type');


const page = reactive({
    pageNum: 1,
    pageSize: 10,
})
const queryForm = reactive({
    projectName: '',
    projectType: '',
    basket: '',
    applyOrg: '',
    usage: '',
    expenseType: '',
    partnerUnit: '',
    status: '',
    year: ''
})
const loading = ref(false)
const total = ref(0)
const dataList = ref([{}])
const search = () => {

}

</script>