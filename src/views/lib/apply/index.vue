<template>
  <div class="app-container" v-loading="loading">
    <div class="flex gap-3 mb-3">
      <el-radio-group v-model="isRecycle">
        <el-radio-button :value="false">项目列表（{{ getProjectCount }}）</el-radio-button>
        <el-radio-button :value="true">回收站（{{ getTrashCount }}）</el-radio-button>
      </el-radio-group>
      <div class="flex items-center">
        <span>项目年度：</span>
        <el-input-number v-model="searchForm.year" :min="2015" :max="2099" width="300">
          <template #suffix>
            <span>年</span>
          </template>
        </el-input-number>
      </div>
      <div class="flex items-center">
        <span>项目单位：</span>
        <DeptSelect v-model="searchForm.projectUnits" class="w-45"></DeptSelect>
        <el-checkbox v-model="searchForm.onlyAssess" class="ml-2" :true-value="'1'"
          :false-value="''">仅考核主体</el-checkbox>
      </div>

      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="search">
        <el-tooltip class="item" effect="dark" content="导出Excel" placement="top">
          <el-button class="ml-3" circle icon="Download" @click="handleDownloadExcel" />
        </el-tooltip>
      </right-toolbar>
      <el-drawer v-model="showSearch" title="查询">
        <el-form label-width="70px">
          <el-form-item label="项目名称">
            <el-input v-model="searchForm.name" placeholder="请输入项目名称" class="w-full"></el-input>
          </el-form-item>
          <el-form-item label="项目性质">
            <el-select v-model="searchForm.natureCode" placeholder="请选择项目性质" class="w-full">
              <el-option v-for="item in project_nature" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="项目类别">
            <el-select v-model="searchForm.typeId" placeholder="请选择项目类别" class="w-full">
              <el-option v-for="item in project_type" :label="item.remark" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="项目用途">
            <el-select v-model="searchForm.purposeIds" multiple placeholder="请选择项目用途" class="w-full">
              <el-option v-for="item in project_purpose" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="项目篮子">
            <el-select v-model="searchForm.basketCode" placeholder="请选择项目篮子" class="w-full">
              <el-option v-for="item in project_basket_type" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="searchForm.isSzhyq" :true-value="'1'" :false-value="''">涉及数字化园区</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="searchForm.isJxhgl" :true-value="'1'" :false-value="''">涉及精细化管理支出</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="searchForm.isZfgm" :true-value="'1'" :false-value="''">涉及政府购买服务</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="searchForm.libState" :true-value="'1'" :false-value="''">是否入库</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="searchForm.checkState" :true-value="'1'" :false-value="''">是否核定</el-checkbox>
          </el-form-item>
        </el-form>

        <template #footer>
          <div class="text-left">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="handleSearchReset">重置</el-button>
          </div>
        </template>
      </el-drawer>
    </div>
    <div style="table-outer">
      <el-table ref="tableRef" :data="getDataList" row-key="id" border show-summary
        :expand-row-keys="['1', '2', '3', '4']" :height="tableHeight" :header-cell-style="{ 'text-align': 'center' }"
        :row-class-name="tableRowClassName" :span-method="spanMethod" :summary-method="getSummaries" :indent="0">
        <template #empty>
          <el-empty description="暂无数据" />
        </template>
        <el-table-column type="index" label="序号" align="center" width="60px">
          <template #default="{ row, $index }">
            {{ row.index }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="项目名称" width="auto" min-width="25%">
          <template #default="{ row }">
            <ProjNameComponent :row="row" />
          </template>

        </el-table-column>
        <el-table-column prop="assessOrgname" label="考核主体" title="hello" align="center" width="auto" min-width="10%"
          v-if="columns[0].visible">
          <template #default="{ row }">
            <div :title="row.assessOrgnameTitle">
              {{ row.assessOrgname }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="handler" label="经办人" align="center" width="auto" min-width="10%"
          v-if="columns[1].visible" />
        <el-table-column prop="estAmount" label="项目估算" align="right" width="auto" min-width="10%"
          v-if="columns[2].visible" />
        <el-table-column label="项目库"
          v-if="columns[3].visible || columns[4].visible || columns[5].visible || columns[6].visible">
          <el-table-column prop="basket_name" label="项目状态" align="center" width="auto" min-width="10%"
            v-if="columns[3].visible">
            <template #default="{ row }">
              <BasketCodeComponent :row="row" />

            </template>
          </el-table-column>
          <el-table-column prop="libAmount" label="入库金额" align="right" width="auto" min-width="10%"
            v-if="columns[4].visible" />
          <el-table-column prop="checkAmount" label="核定金额" align="right" width="auto" min-width="10%"
            v-if="columns[5].visible" />
          <!-- <el-table-column prop="htAmount" label="合同金额" align="right" width="auto" min-width="10%"
            v-if="columns[6].visible" /> -->
        </el-table-column>
        <el-table-column label="已执行" v-if="columns[7].visible || columns[8].visible || columns[9].visible">
          <el-table-column prop="payedPrev" label="截至上年底" align="right" width="auto" min-width="11%"
            v-if="columns[7].visible" />
          <el-table-column prop="payedCurr" label="当年" align="right" width="auto" min-width="10%"
            v-if="columns[8].visible" />
          <el-table-column prop="payedTotal" label="合计" align="right" width="auto" min-width="10%"
            v-if="columns[9].visible" />
        </el-table-column>
        <el-table-column label="资金计划"
          v-if="columns[10].visible || columns[11].visible || columns[12].visible || columns[13].visible">
          <el-table-column prop="planSb" label="上报计划" align="right" width="auto" min-width="10%"
            v-if="columns[10].visible">
            <template #default="{ row }">
              <PlansbComponent :row="row" />

            </template>
          </el-table-column>
          <el-table-column prop="planXd" label="下达计划" align="right" width="auto" min-width="10%"
            v-if="columns[11].visible">
            <template #default="{ row }">
              <PlanXdComponent :row="row" />
            </template>

          </el-table-column>
          <el-table-column prop="planTzjh" label="调整计划" align="right" width="auto" min-width="10%"
            v-if="columns[12].visible">
            <template #default="{ row }">
              <PlanTzjhComponent :row="row" />
            </template>
          </el-table-column>
          <el-table-column prop="planTzxd" label="调整下达" align="right" width="auto" min-width="10%"
            v-if="columns[13].visible">
            <template #default="{ row }">
              <PlanTzxdComponent :row="row" />
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column prop="date" label="操作" width="auto" min-width="10%">
          <template #default="{ row }">
            <template v-if="!row.projType">
              <template v-if="row.currentYearRelation?.isRecycle == '0'">
                <template v-if="[EProjectState.draft, EProjectState.reject].includes(row.state)">
                  <el-button type="danger" link plain class="underline" @click="handleDelete(row.id)">删除</el-button>
                  <el-button type="primary" link plain class="underline !ml-0"
                    @click="handleMoveNextYear(row.id)">移到下年</el-button>
                </template>
                <template v-if="row.state == EProjectState.reported">
                  <el-button v-if="row.basketCode == 1" type="primary" link plain class="underline"
                    @click="handleInfoFix(row)">信息补正</el-button>
                  <el-button v-if="row.fundPlan?.planState != EFundPlanState.yfb" type="primary" link plain
                    class="underline !ml-0" @click="handleCfsh(row)">错发收回</el-button>
                  <el-button v-if="row.fundPlan?.applyState == EApplyAdjustState.ysb" type="primary" link plain
                    class="underline !ml-0" @click="handleCancelAdjust(row)">撤销调整</el-button>
                </template>
              </template>

              <template v-else>
                <el-button type="danger" link plain class="underline" title="恢复（已删除）"
                  @click="handleRecover(row.id)">恢复（已删除）</el-button>
                <el-button type="primary" link plain class="underline !ml-0"
                  @click="handleCompletelyDelete(row.id)">删除</el-button>
              </template>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog title="项目查看" v-model="formViewShow" width="1200px"  :close-on-click-modal="false" destroy-on-close class="relative">
      <FormView @close="formViewShow = false"></FormView>
    </el-dialog>
    <el-dialog title="项目编辑" v-model="formEditShow" width="1200px" :close-on-click-modal="false" destroy-on-close>
      <FormEdit :activate-name="formActivateName" @close="handleFormEditClose"></FormEdit>
    </el-dialog>
    <el-dialog title="计划调整" v-model="planAdjustShow" width="600px" style="height: 550px;" :close-on-click-modal="false" destroy-on-close>
      <PlanAdjust @close="handlePlanAdjustClose"></PlanAdjust>
    </el-dialog>
    <el-dialog title="项目复制" v-model="copyProjectShow" width="1000px" style="height: 800px;" :close-on-click-modal="false" destroy-on-close>
      <CopyProject :year="searchForm.year" @close="copyProjectShow = false; search()"></CopyProject>
    </el-dialog>
  </div>
</template>

<script lang="tsx" setup name="ApplyIndex">
import FormView from './components/formView.vue';
import FormEdit from './components/formEdit.vue';
import { Action, dayjs, ElMessage, ElMessageBox, TableColumnCtx } from 'element-plus';
import { getAbnormalList, getNormalList, moveNextYear, onlyWithdrawProject, putTrash, recoveryProject, removeTrash, withdrawAdjustPlan, withdrawProject } from '@/api/fund/project/info';
import { useFormStore } from './components/store/formStore';
// @ts-ignore
import useUserStore from '@/store/modules/user'
import DeptSelect from '@/components/Select/DeptSelect.vue';
import Bignumber from 'bignumber.js';
import { checkIsNumber, roundNumber2, sumAmount } from '@/utils/common';
import { EProjectState, EFundPlanState, EApplyAdjustState } from '@/utils/constants';
import { withdrawPlan } from '@/api/fund/project/plan';
import PlanAdjust from './components/planAdjust.vue';
import CopyProject from './components/copyProject.vue';

const ProjNameComponent = defineComponent({
  name: 'projNameComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    return () => {
      if (props.row.projType) {
        if (props.row.projType == 1) {
          return (<div class="inline-flex items-center">
            <span>一、结转项目</span>
            <span>({props.row.children.length})</span>
          </div>)
        }
        if (props.row.projType == 2) {
          return (<div class="inline-flex items-center">
            <span>二、经常性项目</span>
            <span>({props.row.children.length})</span>
            <el-button class="underline" type="primary" plain text onClick={() => copyProjectShow.value = true}>复制</el-button>
          </div>)
        }
        if (props.row.projType == 3) {
          return (<div class="inline-flex items-center">
            <span>三、新增项目</span>
            <span>({props.row.children.length})</span>
            <el-button class="underline" type="primary" plain text onClick={handleAddProject}>新增</el-button>
          </div>)
        }
        if (props.row.projType == 4) {
          return (<div class="inline-flex items-center">
            <span>回收站</span>
            <span>（已结项/移除的结转项目：{dataList.value.at(3)?.children.length ?? 0} 个）</span>
          </div>)
        }
      } else {
        return (<div>
          <div class="text-#056cc4 underline cursor-pointer" onClick={() => handleFormViewShow(props.row)}>{props.row.name}</div>
          <div>编号：{props.row.projectSn?.formalSn ?? props.row.projectSn?.tempSn}</div>
        </div>)
      }
    }
  }
})

const BasketCodeComponent = defineComponent({
  name: 'basketCodeComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    return () => {
      if (!props.row.projType) {
        if (props.row.basketCode) {
          return (<dict-tag options={project_basket_type.value} value={props.row.basketCode} />)
        } else {
          if (props.row.state == EProjectState.reported) {
            return (<span>已上报</span>)
          } else {
            return (<el-button type="danger" text plain class="underline"
              onClick={() => handleOpenUnreport(props.row, 1)}>未上报</el-button>)
          }
        }
      }
      return (<span></span>)
    }
  }
})

const PlansbComponent = defineComponent({
  name: 'basketCodeComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    return () => {
      if (!props.row.projType) {
        if (!props.row?.fundPlan?.planState || [EFundPlanState.reject, EFundPlanState.draft].includes(props.row?.fundPlan?.planState)) {
          return (<el-button type="danger" text plain class="underline"
            onClick={() => handleOpenUnreport(props.row, 5)}>未上报</el-button>)
        } else {
          return (<span>{roundNumber2(props.row?.fundPlan?.declareAmount)}</span>)
        }
      } else {
        return (<span>{roundNumber2(props.row.planSb)}</span>)
      }
    }
  }
})

const PlanXdComponent = defineComponent({
  name: 'planXdComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    return () => {
      if (!props.row.projType) {
        return (<span>{roundNumber2(props.row.fundPlan?.formalAmount)}</span>)
      } else {
        return (<span>{roundNumber2(props.row.planXd)}</span>)
      }
    }
  }
})

const PlanTzjhComponent = defineComponent({
  name: 'PlanTzjhComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    return () => {

      if (!props.row.projType) {
        if ([EApplyAdjustState.ysb, EApplyAdjustState.qrtz].includes(props.row.fundPlan?.applyState)) {
          return (<span>{props.row.fundPlan?.applyAdjust}</span>)
        }
        else if (props.row?.fundPlan?.planState === EFundPlanState.yfb) {
          return (<el-button type="danger" text plain class="underline"
            onClick={() => handleOpenPlanAdjust(props.row)}>调整</el-button>)
        } else {
          return (<span></span>)

        }
      } else {
        return (<span>{roundNumber2(props.row.planTzjh)}</span>)
      }
    }
  }
})

const PlanTzxdComponent = defineComponent({
  name: 'PlanTzxdComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    return () => {
      if (!props.row.projType) {
        return (<span>{roundNumber2(props.row.fundPlan?.adjustAmount)}</span>)
      } else {
        return (<div>
          <span>{roundNumber2(props.row.planTzxd)}</span><br />
          <span class="text-red">{roundNumber2(props.row.planTzxd_red)}</span>
        </div>)
      }
    }
  }
})

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_type, project_basket_type } = proxy.useDict("project_purpose", "project_nature", 'project_type', 'project_basket_type');

const userStore = useUserStore()
const formStore = useFormStore()

const copyProjectShow = ref(false)
// const num = ref(dayjs().year())
const showSearch = ref(false)

type rowType = IProjectInfoVo & { index: number; plan_formalAmount: number; plan_adjustAmount?: number }
const dataList = ref([
  {
    id: 1,
    /** 结转项目 */
    projType: 1,
    children: [] as rowType[],
    estAmount: 0,
    basketCode: 0,
    basketName: '',
    libAmount: 0,
    checkAmount: 0,
    htAmount: 0,
    payedPrev: 0,
    payedCurr: 0,
    payedTotal: 0,
    planSb: 0,
    planXd: 0,
    planTzjh: 0,
    planTzxd: 0,
    planTzxd_red: 0,
  },
  {
    id: 2,
    /** 经常性项目 */
    projType: 2,
    children: [] as rowType[],
    estAmount: 0,
    basketCode: 0,
    basketName: '',
    libAmount: 0,
    checkAmount: 0,
    htAmount: 0,
    payedPrev: 0,
    payedCurr: 0,
    payedTotal: 0,
    planSb: 0,
    planXd: 0,
    planTzjh: 0,
    planTzxd: 0,
    planTzxd_red: 0,
  },
  {
    id: 3,
    /** 新增项目 */
    projType: 3,
    children: [] as rowType[],
    estAmount: 0,
    basketCode: 0,
    basketName: '',
    libAmount: 0,
    checkAmount: 0,
    htAmount: 0,
    payedPrev: 0,
    payedCurr: 0,
    payedTotal: 0,
    planSb: 0,
    planXd: 0,
    planTzjh: 0,
    planTzxd: 0,
    planTzxd_red: 0,
  },
  {
    id: 4,
    /** 回收站项目 */
    projType: 4,
    children: [] as rowType[],
    estAmount: 0,
    basketCode: 0,
    basketName: '',
    libAmount: 0,
    checkAmount: 0,
    htAmount: 0,
    payedPrev: 0,
    payedCurr: 0,
    payedTotal: 0,
    planSb: 0,
    planXd: 0,
    planTzjh: 0,
    planTzxd: 0,
    planTzxd_red: 0,
  }
])

const isRecycle = ref(false)
const getDataList = computed(() => {
  if (isRecycle.value) {
    return dataList.value.filter(item => item.projType === 4)
  } else {
    return dataList.value.filter(item => item.projType !== 4)
  }
})


const getProjectCount = computed(() => {
  return dataList.value.slice(0, 3).flatMap(t => t.children).length
})
const getTrashCount = computed(() => {
  return dataList.value.at(3)?.children.length ?? 0
})

const searchForm = reactive({
  year: dayjs().year(),
  projectUnits: userStore.deptId,
  name: '',
  natureCode: '',
  basketCode: '',
  typeId: '',
  purposeIds: [],
  isSzhyq: '',
  isJxhgl: '',
  isZfgm: '',
  libState: '',
  checkState: '',
  onlyAssess: '1'
})
const tableRef = ref<InstanceType<typeof ElTable>>()
const loading = ref(false)
const search = () => {
  loading.value = true
  Promise.all([
    getNormalList(searchForm).then(async res => {
      dataList.value.at(0)!.children = res.data?.filter(t => t.currentYearRelation?.projType == 1) as any
      dataList.value.at(1)!.children = res.data?.filter(t => t.currentYearRelation?.projType == 2) as any
      dataList.value.at(2)!.children = res.data?.filter(t => t.currentYearRelation?.projType == 3) as any

    }),
    getAbnormalList(searchForm).then(async res => {
      dataList.value.at(3)!.children = res.data as any

    })
  ]).then(() => {
    dataList.value.forEach(row => {
      row.children.forEach((t: any, index: number) => {
        t.index = index + 1
        t.assessOrgnameTitle = formartAssessOrgnameTitle(t)
      })
      row.estAmount = sumAmount(row.children.map(t => t.estAmount)) ?? 0
      row.libAmount = sumAmount(row.children.map(t => t.libAmount)) ?? 0
      row.checkAmount = sumAmount(row.children.map(t => t.checkAmount)) ?? 0
      row.payedPrev = 0 // sumAmount(row.children.map(t => t.payedPrev)) ?? 0
      row.payedCurr = 0 //sumAmount(row.children.map(t => t.payedCurr)) ?? 0
      row.payedTotal = 0 //sumAmount(row.children.map(t => t.payedTotal)) ?? 0


      row.planSb = sumAmount(row.children.map(t => {
        if (![EFundPlanState.reject, EFundPlanState.draft].includes(t.fundPlan?.planState)) {
          return t.fundPlan?.declareAmount ?? 0
        }
        return 0
      })) ?? 0
      row.planXd = sumAmount(row.children.map(t => t.fundPlan?.formalAmount)) ?? 0

      row.planTzjh = sumAmount(
        row.children.map(t => {
          if ([EApplyAdjustState.reject, EApplyAdjustState.draft].includes(t.fundPlan?.applyState) || t.fundPlan?.applyState == null) {
            return t.fundPlan?.formalAmount
          }
          if (EApplyAdjustState.qrtz == t.fundPlan?.applyState) {
            return t.fundPlan?.applyAdjust ?? t.fundPlan?.formalAmount
          }
          return t.fundPlan?.applyAdjust
        })
      ) ?? 0
      row.planTzxd = sumAmount(
        row.children.map(t => t.fundPlan?.adjustAmount)
      ) ?? 0
      row.planTzxd_red = sumAmount(
        row.children.map(t => {
          if ([EApplyAdjustState.reject, EApplyAdjustState.draft].includes(t.fundPlan?.applyState) || t.fundPlan?.applyState == null) {
            return t.fundPlan?.formalAmount
          }
          if (t.fundPlan?.applyState == EApplyAdjustState.qrtz) {
            return t.fundPlan?.adjustAmount
          }
          return 0
        })
      ) ?? 0
    })
  }).finally(() => {
    loading.value = false
  })
}
watch(() => [searchForm.year, searchForm.projectUnits, searchForm.onlyAssess], () => {
  search()
})

search()

const formViewShow = ref(false)
const handleFormViewShow = (row: IProjectInfoVo) => {
  formViewShow.value = true
  formStore.formView.id = row.id
}
const formActivateName = ref()
const formEditShow = ref(false)
const handleOpenUnreport = (row: IProjectInfoVo, activateName?: number) => {
  // 删除项目不允许编辑
  if (row.currentYearRelation?.isRecycle == '1' || row.currentYearRelation?.isClose == '1') {
    return
  }
  formEditShow.value = true
  formActivateName.value = activateName || 1

  formStore.$reset()
  formStore.form.id = row.id
  formStore.projectYear = searchForm.year
}

const planAdjustShow = ref(false)
const handleOpenPlanAdjust = (row: IProjectInfoVo) => {
  planAdjustShow.value = true
  formStore.formView = row
  formStore.projectYear = searchForm.year
}
const handlePlanAdjustClose = () => {
  planAdjustShow.value = false
  search()
}
const handleCancelAdjust = (row: IProjectInfoVo) => {
  ElMessageBox.confirm('是否确认撤销资金计划调整？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    withdrawAdjustPlan(row.id!, searchForm.year).then(res => {
      ElMessage.success('取消调整成功')
      search()
    })
  })
}

const handleInfoFix = (row: IProjectInfoVo) => {
  formEditShow.value = true
  formActivateName.value = 1

  formStore.$reset()
  formStore.form.id = row.id
  formStore.projectYear = searchForm.year
  formStore.isXxbz = true
}
const handleAddProject = () => {
  formEditShow.value = true
  formActivateName.value = 1

  formStore.$reset()
  formStore.form.projType = 3
  formStore.form.year = searchForm.year
  formStore.projectYear = searchForm.year
}

const handleCfsh = (row: IProjectInfoVo) => {
  if (row.state === EProjectState.reported) {
    if (row.fundPlan?.planState == EProjectState.reported) {
      ElMessageBox.confirm('请选择收回的内容？', '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '收回项目库和资金计划',
        cancelButtonText: '只收回资金计划',
        type: 'warning'
      }).then(() => {
        // 错发收回逻辑
        withdrawProject(row.id!, searchForm.year).then(res => {
          ElMessage.success('收回成功')
          search()
        })
      }).catch((action: Action) => {
        if (action == 'cancel') {
          withdrawPlan(row.id!, searchForm.year).then(res => {
            ElMessage.success('收回成功')
            search()
          })
        }

      })
    } else {
      ElMessageBox.confirm('是否确认收回项目？', '提示', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 错发收回逻辑
        onlyWithdrawProject(row.id!).then(res => {
          ElMessage.success('收回成功')
          search()
        })
      })
    }

  }

}


const handleDelete = (id: string) => {
  ElMessageBox.confirm('删除后可在回收站恢复，是否确认删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    // 删除逻辑
    putTrash(id, searchForm.year).then(res => {
      ElMessage.success('移动到回收站成功')
      search()
    })
  })
}

const handleMoveNextYear = (id: string) => {
  ElMessageBox.confirm(`是否确认将该项目移至下一年？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    moveNextYear(id, searchForm.year).then(res => {
      ElMessage.success('移动成功')
      search()
    })
  })
}

const handleRecover = (id: string) => {
  ElMessageBox.confirm('是否确认恢复？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    // 恢复逻辑
    recoveryProject(id, searchForm.year).then(res => {
      ElMessage.success('恢复成功')
      search()
    })
  })
}

const handleCompletelyDelete = (id: string) => {
  ElMessageBox.confirm('是否确认删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 完全删除逻辑
    removeTrash(id, searchForm.year).then(res => {
      ElMessage.success('删除成功')
      search()
    })
  })
}


const handleFormEditClose = () => {
  formEditShow.value = false
  formStore.isXxbz = false
  search()
}

const formartAssessOrgnameTitle = (row: IProjectInfoVo) => {
  return `考核主体：${row.assessOrgname}\n申报单位：${row.applyOrgname}\n上报单位：${row.cooperateOrgid ? JSON.parse(row.cooperateOrgid).map((t: any) => t.name).join('，') : '无'
    }`
}

const handleSearchReset = () => {
  searchForm.name = ''
  searchForm.natureCode = ''
  searchForm.typeId = ''
  searchForm.purposeIds = []
  searchForm.basketCode = ''
  searchForm.isSzhyq = ''
  searchForm.isJxhgl = ''
  searchForm.isZfgm = ''
  searchForm.libState = ''
  searchForm.checkState = ''
}
//

const tableHeight = ref(window.innerHeight - 180)


// 列显隐信息
const columns = ref([
  { key: 0, label: `考核主体`, visible: true },
  { key: 1, label: `经办人`, visible: true },
  { key: 2, label: `项目估算`, visible: true },
  { key: 3, label: `项目状态`, visible: true },
  { key: 4, label: `入库金额`, visible: true },
  { key: 5, label: `核定金额`, visible: true },
  { key: 6, label: `合同金额`, visible: true },
  { key: 7, label: `截至上年底`, visible: true },
  { key: 8, label: `当年`, visible: true },
  { key: 9, label: `合计`, visible: true },
  { key: 10, label: `上报计划`, visible: true },
  { key: 11, label: `下达计划`, visible: true },
  { key: 12, label: `调整计划`, visible: true },
  { key: 13, label: `调整下达`, visible: true }
]);

const tableRowClassName = ({
  row,
  rowIndex
}: {
  row: any,
  rowIndex: number
}) => {
  if (row.projType) {
    return 'warning-row'
  }
  return ''
}

const spanMethod = ({
  row,
  column,
  rowIndex,
  columnIndex
}: {
  row: any,
  column: any,
  rowIndex: number,
  columnIndex: number
}) => {
  if (row.projType) {
    if (columnIndex === 0) return [0, 0];        // 被合并掉
    if (columnIndex === 1) return [1, 3];        // 合并3列（1、2、3）
    if (columnIndex === 3) return [0, 0];  // 被合并掉
  }
  return [1, 1]
}

// 设置底部合计行样式
onMounted(() => {
  setTimeout(() => {
    const tfoot = proxy.$el.querySelector('.el-table__footer tfoot')
    tfoot.firstChild.children[0].colSpan = 2
    tfoot.firstChild.children[1].style.display = 'none'
  }, 0);
})
interface SummaryMethodProps<T = typeof dataList.value[number] & { children?: IProjectInfoVo[] }> {
  columns: TableColumnCtx<T>[]
  data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param
  const sums: (string | VNode | number | null)[] = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = (
        <div class="text-left">
          {`合计（${!isRecycle.value ? getProjectCount.value : getTrashCount.value}个）`}
        </div>
      )
    }
    if (column.property == 'estAmount') {
      sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.estAmount)) ?? 0)
    }
    if (column.property == 'libAmount') {
      sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.libAmount)) ?? 0)
    }
    if (column.property == 'checkAmount') {
      sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.checkAmount)) ?? 0)
    }
    if (column.property == 'payedPrev') {
      sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.payedPrev)) ?? 0)
    }
    if (column.property == 'payedCurr') {
      sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.payedCurr)) ?? 0)
    }
    if (column.property == 'payedTotal') {
      sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.payedTotal)) ?? 0)
    }
    if (column.property == 'planSb') {
      sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.planSb)) ?? 0)
    }
    if (column.property == 'planXd') {
      sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.planXd)) ?? 0)
    }
    if (column.property == 'planTzjh') {
      sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.planTzjh)) ?? 0)
    }
    if (column.property == 'planTzxd') {
      sums[index] = (
        <div>
          <span>{roundNumber2(sumAmount(getDataList.value.map(t => t.planTzxd)) ?? 0)}</span><br />
          <span class="text-red">{roundNumber2(sumAmount(getDataList.value.map(t => t.planTzxd_red)) ?? 0)}</span>
        </div>
      )
    }

  })
  return sums
}

const handleDownloadExcel = () => {
  proxy.download('/project/info/download', {
    ...searchForm
  }, `项目(计划)申报${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}

</script>

<style>
.el-table .warning-row {
  /*--el-table-tr-bg-color: var(--el-color-warning-light-9);*/
  background-image: linear-gradient(to bottom, #ffffff, #f2f2f2)!important;
  font-weight: 500;
  color: #444;
}

.el-table .success-row {
  /*--el-table-tr-bg-color: var(--el-color-success-light-9);*/
  /* background-color: #f7faff; */
  background-image: linear-gradient(to bottom, #ffffff, #f2f2f2)!important;
}

.el-table .el-table__placeholder {
  display: none;
}

.el-table__footer .hidden {
  display: none;
}
.el-table__footer-wrapper tfoot td.el-table__cell{font-weight: bold; background-image: linear-gradient(to bottom, #eff7fd, #deeefb)!important;}
</style>
