<template>
    <el-descriptions :column="1" label-width="15%" border class="h-[620px] overflow-y-auto">
        <el-descriptions-item width="85%">
            <template #label>
                <div>1、必要性</div>
            </template>
            <div class="min-h-20">
                {{ formStore.formView.necessity }}
            </div>
        </el-descriptions-item>
        <el-descriptions-item width="300px">
            <template #label>
                <div>2、主要内容</div>
            </template>
            <div class="min-h-20">
                {{ formStore.formView.mainCnt }}
            </div>
        </el-descriptions-item>
        <el-descriptions-item>
            <template #label>
                <div>2、项目依据</div>
            </template>
            <div class="min-h-20">
                {{ formStore.formView.basis }}
            </div>
        </el-descriptions-item>
        <el-descriptions-item>
            <template #label>
                <div>3、项目实施计划</div>
                <div>(投资进度)</div>
            </template>
            <div class="min-h-20">
                {{ formStore.formView.actionPlan }}
            </div>
        </el-descriptions-item>
    </el-descriptions>
</template>

<script lang="ts" setup>
// 项目描述
import { useFormStore } from '../store/formStore';

const formStore = useFormStore()
</script>