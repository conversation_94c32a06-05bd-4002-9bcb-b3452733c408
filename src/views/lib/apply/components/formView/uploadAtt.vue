<template>
    <div class="relative">
        <div class="flex flex-col gap-3">
            <div class="h-[620px]">
                <div class="mb-2">
                    <span class="text-#303133 text-4 font-bold">附件列表</span>
                </div>
                <FileTable :file-list="formStore.formView.sysAttachments??[]" :hidden-columns="['del']"></FileTable>
            </div>
        </div>
    </div>

</template>

<script lang="ts" setup>
// 上传附件
import { dateFormat } from '@/utils/common';
import { useFormStore } from '../store/formStore';
import FileTable from '@/components/Table/FileTable.vue';
const formStore = useFormStore()

</script>