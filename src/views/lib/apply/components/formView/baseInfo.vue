<template>
    <div class="flex flex-col gap-3">
        <!-- <el-descriptions title="申报单位情况" :column="2" label-width="15%" border>
            <el-descriptions-item width="35%">
                <template #label>
                    申请单位名称
                </template>
                <span>{{ formStore.formView.applyOrgname }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="申报时间" width="35%">
                <span>{{ dateFormat(formStore.formView.applyTime) }}</span>
            </el-descriptions-item>
        </el-descriptions> -->
        <el-descriptions :column="2" label-width="15%" border>
            <el-descriptions-item width="35%">
                <template #label>
                    申请单位名称
                </template>
                <span>{{ formStore.formView.applyOrgname }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="申报时间" width="35%">
                <span>{{ dateFormat(formStore.formView.applyTime) }}</span>
            </el-descriptions-item>
            <el-descriptions-item :span="2" width="35%">
                <template #label>
                    项目名称
                </template>
                <span>{{ formStore.formView.name }}</span>
            </el-descriptions-item>
            <el-descriptions-item width="35%">
                <template #label>
                    项目使用单位
                </template>
                <span>{{ formStore.formView.useOrgname }}</span>
            </el-descriptions-item>
            <el-descriptions-item width="35%">
                <template #label>
                    项目金额估算（万元）
                </template>
                <span>{{ formStore.formView.estAmount }}</span>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    项目类型
                </template>
                <span>{{ formStore.formView.typeName?.replaceAll('-', '').trim() }}</span>
            </el-descriptions-item>
            <el-descriptions-item :rowspan="2">
                <template #label>
                    项目用途
                </template>
                {{ getPurpose }}
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    <div>支出类型</div>
                    <div>涉及政府购买</div>
                </template>
                <div class="flex flex-col">
                    <el-checkbox :model-value="formStore.formView.isSzhyq" :true-value="'1'"
                        :false-value="'0'">涉及数字化园区支出</el-checkbox>
                    <el-checkbox :model-value="formStore.formView.isJxhgl" :true-value="'1'"
                        :false-value="'0'">涉及精细化管理支出</el-checkbox>
                    <el-checkbox :model-value="formStore.formView.isZfgm" :true-value="'1'"
                        :false-value="'0'">涉及政府购买服务</el-checkbox>
                </div>
            </el-descriptions-item>
            <el-descriptions-item label="项目性质">
                {{ formStore.formView.natureName }}
            </el-descriptions-item>
            <el-descriptions-item label="配合单位">
                {{ getDeptNames }}
                <!-- <el-checkbox-group class="flex flex-col gap-2">
                    <el-checkbox label="1">配合单位为考核主体</el-checkbox>
                </el-checkbox-group> -->
            </el-descriptions-item>
            <el-descriptions-item label="项目负责人">
                <span>{{ formStore.formView.leader }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="手机">
                <span>{{ formStore.formView.leaderTel }}</span>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    经办人
                </template>
                <span>{{ formStore.formView.handler }}</span>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    手机
                </template>
                <span>{{ formStore.formView.handlerTel }}</span>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    项目建设周期
                </template>
                <span>{{ dateFormat(formStore.formView.buildBegin) }} 到 {{ dateFormat(formStore.formView.buildEnd) }}</span>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    资金使用周期
                </template>
                <span>{{ dateFormat(formStore.formView.fundBegin) }} 到 {{ dateFormat(formStore.formView.fundEnd) }}</span>
            </el-descriptions-item>
        </el-descriptions>
    </div>

</template>

<script lang="ts" setup>
// 基本信息
import { dateFormat } from '@/utils/common';
import { useFormStore } from '../store/formStore';

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_type } = proxy.useDict("project_purpose", "project_nature", 'project_type');


const formStore = useFormStore()

const getPurpose = computed(() => {
    if (formStore.formView.purpose) {
        return JSON.parse(formStore.formView.purpose).map((item: any) => item.name).join()
    }
})
const getDeptNames = computed(() => {
    if (formStore.formView.cooperateOrgid) {
        return JSON.parse(formStore.formView.cooperateOrgid).map((item: any) => item.name).join()
    }
})

const getProjectCode = computed(() => {
    const code = formStore.formView.projectSn?.formalSn ?? formStore.formView.projectSn?.tempSn
    return code ? `（项目编号：${code}）` : ''
})
</script>