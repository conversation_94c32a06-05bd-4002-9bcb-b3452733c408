<template>
    <el-tabs v-model="activeName" type="card" class="demo-tabs" v-loading="loading">
        <el-tab-pane label="基本信息" :name="1">
            <el-scrollbar height="700px">
                <BaseInfo ref="baseInfoRef" />
                <div class="absolute bottom-0 left-0 w-full ">
                    <div class="flex justify-center">
                        <el-button type="primary" :disabled="!formIdCheck" @click="handleNextStep">下一步</el-button>
                        <el-button type="warning" :disabled="!formIdCheck" @click="handlePrint">打印</el-button>
                        <el-button type="danger" :disabled="!formIdCheck" @click="handleExport">导出</el-button>
                        <el-button type="info" plain @click="emit('close')">关闭</el-button>
                    </div>
                    <div class="flex justify-center mt-2">
                        <el-button type="info" @click="handleBackToSbr">退回申报人</el-button>
                        <el-button type="primary" @click="handleProjectChange">项目变更</el-button>
                    </div>
                </div>
            </el-scrollbar>
        </el-tab-pane>
        <el-tab-pane label="项目概述" :name="2" :disabled="!formIdCheck">
            <el-scrollbar height="700px">
                <ProjectDesc ref="projectDescRef" />
                <div class="absolute bottom-0 left-0 w-full h-[50px] flex justify-center items-center">
                    <el-button type="info" @click="handlePrevStep">上一步</el-button>
                    <el-button type="primary" @click="handleNextStep">下一步</el-button>
                    <el-button type="warning" @click="handlePrint">打印</el-button>
                    <el-button type="danger" @click="handleExport">导出</el-button>
                    <el-button type="info" plain @click="emit('close')">关闭</el-button>
                </div>
            </el-scrollbar>
        </el-tab-pane>
        <el-tab-pane label="绩效目标" :name="3" :disabled="!formIdCheck">
            <el-scrollbar height="700px">
                <KpiTarget ref="kpiTargetRef" />
                <div class="absolute bottom-0 left-0 w-full h-[50px] flex justify-center items-center">
                    <el-button type="info" @click="handlePrevStep">上一步</el-button>
                    <el-button type="primary" @click="activeName++">下一步</el-button>
                    <el-button type="warning" @click="handlePrint">打印</el-button>
                    <el-button type="danger" @click="handleExport">导出</el-button>
                    <el-button type="info" plain @click="emit('close')">关闭</el-button>
                </div>
            </el-scrollbar>

        </el-tab-pane>
        <el-tab-pane label="附件" :name="4" :disabled="!formIdCheck">
            <el-scrollbar height="700px">
                <UploadAtt ref="uploadAttRef" />

                <div class="absolute bottom-0 left-0 w-full h-[50px] flex justify-center items-center">
                    <el-button type="info" @click="handlePrevStep">上一步</el-button>
                    <el-button type="primary" @click="handleNextStep">下一步</el-button>
                    <el-button type="warning" @click="handlePrint">打印</el-button>
                    <el-button type="danger" @click="handleExport">导出</el-button>
                    <el-button type="info" plain @click="emit('close')">关闭</el-button>
                </div>
            </el-scrollbar>

        </el-tab-pane>
        <el-tab-pane label="资金计划" :name="5" :disabled="!formIdCheck">
            <el-scrollbar height="700px">
                <FundPlan ref="fundPlanRef" />

                <div class="absolute bottom-0 left-0 w-full h-[50px] flex justify-center items-center">
                    <el-button type="info" @click="handlePrevStep">上一步</el-button>
                    <el-button type="info" plain @click="emit('close')">关闭</el-button>
                </div>
            </el-scrollbar>
        </el-tab-pane>
    </el-tabs>
    <el-dialog title="项目变更" v-model="formChangeShow" width="1200px" :close-on-click-modal="false" destroy-on-close>
        <ChangeBaseInfo ref="baseInfoEditRef"></ChangeBaseInfo>
        <div class="absolute bottom-0 left-0 w-full h-[50px] flex justify-center items-center">
            <el-button type="primary" @click="handleSave">保存</el-button>
            <el-button type="info" plain @click="formChangeShow = false">关闭</el-button>
        </div>
    </el-dialog>
    <PrintForm :form-data="formStore.formView" class="hidden" ref="pringRef"></PrintForm>
</template>

<script lang="tsx" setup>
import 'vue/jsx';
// 查看表单
import BaseInfo from './formView/baseInfo.vue';
import ProjectDesc from './formView/projectDesc.vue';
import KpiTarget from './formView/kpiTarget.vue';
import UploadAtt from './formView/uploadAtt.vue';
import FundPlan from './formView/fundPlan.vue';
import ChangeBaseInfo from './changeBaseInfo.vue';
import { add, backProject, getProjectDetail, update } from '@/api/fund/project/info';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useFormStore } from './store/formStore';
import PrintForm from './printForm.vue';

const emit = defineEmits(['close']);

const props = defineProps<{
    activateName?: number;
}>();
const activeName = ref(props.activateName || 1);
const loading = ref(false)
const formStore = useFormStore()
const searchDetail = (id: string) => {
    getProjectDetail(id, formStore.projectYear).then(res => {
        formStore.formView = res.data ?? {};
    })
}
if (formStore.formView.id) {
    searchDetail(formStore.formView.id)
} else {
    formStore.formView.natureCode = 'JC'
}
const formIdCheck = computed(() => {
    return formStore.formView.id
})
type formRefType = {
    getData: () => Promise<any>
}
const baseInfoEditRef = ref<formRefType>();
const handlePrevStep = async () => {
    activeName.value--
}
const handleNextStep = async () => {
    activeName.value++
}

const handleBackToSbr = () => {
    ElMessageBox.confirm('该操作会连同历年资金计划一起退回，是否确认？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        backProject(formStore.formView.id!).then(res => {
            ElMessage.success('退回成功');
            searchDetail(formStore.formView.id!)
            emit('close')
        })
    })


}

const formChangeShow = ref(false);
const handleProjectChange = () => {
    formStore.form = { ...formStore.formView }
    formChangeShow.value = true

}

const handleSave = async () => {
    const data = await baseInfoEditRef.value?.getData()
    loading.value = true
    update({ ...data }).then(res => {
        ElMessage.success('保存成功');
        searchDetail(formStore.formView.id!)

    }).finally(() => {
        // formChangeShow.value = false
        loading.value = false
    })
}


const handleExport = () => {
    handlePrint()
}

const pringRef = ref()
const handlePrint = () => {
    pringRef.value.print()
}
</script>