<template>
    <el-form ref="formRef" :model="form" class="flex flex-col gap-3 h-[600px] overflow-y-auto" :inline-message="true">
        <el-descriptions :column="1" label-width="15%" border>
            <el-descriptions-item width="85%">
                <template #label>
                    1、必要性
                </template>
                <el-form-item prop="necessity" class="!mb-0">
                    <el-input v-model="form.necessity" type="textarea" class="min-h-20" :rows="5"></el-input>
                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    2、主要内容
                </template>
                <el-form-item prop="mainCnt" class="!mb-0">
                    <el-input v-model="form.mainCnt" type="textarea" class="min-h-20" :rows="5"></el-input>
                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    3、项目依据
                </template>
                <el-form-item prop="basis" class="!mb-0">
                    <el-input v-model="form.basis" type="textarea" class="min-h-20" :rows="5"></el-input>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    <div>4、项目实施计划</div>
                    <div>(投资进度)</div>
                </template>
                <el-form-item prop="actionPlan" class="!mb-0">
                    <el-input v-model="form.actionPlan" type="textarea" class="min-h-20" :rows="5"></el-input>
                </el-form-item>
            </el-descriptions-item>
        </el-descriptions>
    </el-form>

</template>

<script lang="ts" setup>
import { ElMessage, FormInstance, FormRules } from 'element-plus';
import { useFormStore } from '../store/formStore';

// 项目描述
const formStore = useFormStore();
const form = reactive<IProjectInfoVo>({})
watchEffect(() => {
    form.necessity = formStore.form.necessity;
    form.mainCnt = formStore.form.mainCnt;
    form.basis = formStore.form.basis;
    form.actionPlan = formStore.form.actionPlan;
})

const formRules = reactive<FormRules<typeof form>>({
    necessity: [
        { required: true, message: '请输入必要性', trigger: 'blur' }
    ],
    mainCnt: [
        { required: true, message: '请输入主要内容', trigger: 'blur' }
    ],
    basis: [
        { required: true, message: '请输入项目依据', trigger: 'blur' }
    ],
    actionPlan: [
        { required: true, message: '请输入项目实施计划', trigger: 'blur' }
    ],
})
const formRef = ref<FormInstance>()

defineExpose({
    async getData() {
        // return formRef.value?.validate().then((res) => {
            return {
                necessity: form.necessity,
                mainCnt: form.mainCnt,
                basis: form.basis,
                actionPlan: form.actionPlan,
            }
        // })
        
    }
})
</script>