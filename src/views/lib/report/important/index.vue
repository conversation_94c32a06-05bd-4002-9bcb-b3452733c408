<template>
    <div class="app-container relative" v-loading="loading">
        <div class="grid grid-cols-3 items-center">
            <el-select v-model="searchForm.deptId" clearable class="w-45">
                <el-option v-for="item in deptList" :label="item.deptName" :value="item.deptId"></el-option>
            </el-select>
            <!-- <DeptSelect v-model="searchForm.deptId" class="w-45"></DeptSelect> -->
            <div class="flex items-center justify-center gap-2">
                <el-date-picker v-model="searchForm.year" type="year" class="!w-25" :clearable="false"
                    value-format="YYYY" :disabled-date="handleCheckDateDisabled"></el-date-picker>
                <h1 class="text-center leading-9 relative my-4">年度专项发展资金重点推进项目 </h1>
            </div>
            <div class="flex justify-end">
                <el-button type="primary" @click="projectManageShow = true">项目管理</el-button>
                <el-button type="primary" @click="planManageShow = true">计划管理</el-button>
                <right-toolbar :columns="columns" @queryTable="search" :search="false" class="!ml-3">
                    <el-tooltip class="item" effect="dark" content="预览打印" placement="top">
                        <el-button class="ml-3" circle icon="Printer" @click="handlePrint" />
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="导出Excel" placement="top">
                        <el-button class="ml-3" circle icon="Download" @click="handleDownloadExcel" />
                    </el-tooltip>
                </right-toolbar>
            </div>
        </div>

        <vxe-table ref="tableRef" :data="tableData" border :height="tableHeight" :cell-class-name="bindCellClassName"
            :merge-cells="mergeCells" @cell-click="handleCellClick" :header-cell-class-name="bindHeaderCellClassName">
            <vxe-column field="projCode" title="序号" fixed="left" width="80" align="center" />
            
            <vxe-column field="projName" title="项目名称" fixed="left" width="260" align="center">
                <template v-slot="{ row }">
                    {{ !row.projId && row.stepNo ? getProjectImpStageTypeMap.get(row.stepNo) : row.projName }}
                </template>
            </vxe-column>
            <vxe-column field="projAmount" title="项目金额" width="130" align="center" />
            <vxe-column field="planBegin" title="计划开工" width="130" align="center">
                <template v-slot="{ row }">
                    {{ dateFormat(row.planBegin, 'YYYY年M月') }}
                </template>
            </vxe-column>
            <vxe-column field="projEnd" title="计划完工" width="130" align="center">
                <template v-slot="{ row }">
                    {{ dateFormat(row.projEnd, 'YYYY年M月') }}
                </template>
            </vxe-column>
            <vxe-column field="yearPlan" :title="`${getNumbericalYear}年正式计划`" width="130" align="center" />
            <vxe-column field="" :title="`${getNumbericalYear}年调整计划`" width="130" align="center" />
            <vxe-column field="yearExecuted" :title="`${getNumbericalYear}年已执行`" width="130" align="center" />
            <vxe-column field="progressIssue" title="进展说明及需协调问题" width="180" align="center" />
            <vxe-colgroup field="quarter1" :title="`${getNumbericalYear}年第一季度`" align="center"
                :visible="columns[0].visible || columns[1].visible || columns[2].visible">
                <vxe-column field="0" title="1月" width="150" align="center" :visible="columns[0].visible">
                    <template v-slot="{ row }">
                        {{ !row.isCopy ? row.projectImportantJhVos?.at(0)?.plan :
                            row.projectImportantExceVos?.at(0)?.action }}
                    </template>
                </vxe-column>
                <vxe-column field="1" title="2月" width="150" align="center" :visible="columns[1].visible">
                    <template v-slot="{ row }">
                        {{ !row.isCopy ? row.projectImportantJhVos?.at(1)?.plan :
                            row.projectImportantExceVos?.at(1)?.action }}
                    </template>
                </vxe-column>
                <vxe-column field="2" title="3月" width="150" align="center" :visible="columns[2].visible">
                    <template v-slot="{ row }">
                        {{ !row.isCopy ? row.projectImportantJhVos?.at(2)?.plan :
                            row.projectImportantExceVos?.at(2)?.action }}
                    </template>
                </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup field="quarter2" :title="`${getNumbericalYear}年第二季度`" align="center"
                :visible="columns[3].visible || columns[4].visible || columns[5].visible">
                <vxe-column field="3" title="4月" width="150" align="center" :visible="columns[3].visible">
                    <template v-slot="{ row }">
                        {{ !row.isCopy ? row.projectImportantJhVos?.at(3)?.plan :
                            row.projectImportantExceVos?.at(3)?.action }}
                    </template>
                </vxe-column>
                <vxe-column field="4" title="5月" width="150" align="center" :visible="columns[4].visible">
                    <template v-slot="{ row }">
                        {{ !row.isCopy ? row.projectImportantJhVos?.at(4)?.plan :
                            row.projectImportantExceVos?.at(4)?.action }}
                    </template>
                </vxe-column>
                <vxe-column field="5" title="6月" width="150" align="center" :visible="columns[5].visible">
                    <template v-slot="{ row }">
                        {{ !row.isCopy ? row.projectImportantJhVos?.at(5)?.plan :
                            row.projectImportantExceVos?.at(5)?.action }}
                    </template>
                </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup field="quarter3" :title="`${getNumbericalYear}年第三季度`" align="center"
                :visible="columns[6].visible || columns[7].visible || columns[8].visible">
                <vxe-column field="6" title="7月" width="150" align="center" :visible="columns[6].visible">
                    <template v-slot="{ row }">
                        {{ !row.isCopy ? row.projectImportantJhVos?.at(6)?.plan :
                            row.projectImportantExceVos?.at(6)?.action }}
                    </template>
                </vxe-column>
                <vxe-column field="7" title="8月" width="150" align="center" :visible="columns[7].visible">
                    <template v-slot="{ row }">
                        {{ !row.isCopy ? row.projectImportantJhVos?.at(7)?.plan :
                            row.projectImportantExceVos?.at(7)?.action }}
                    </template>
                </vxe-column>
                <vxe-column field="8" title="9月" width="150" align="center" :visible="columns[8].visible">
                    <template v-slot="{ row }">
                        {{ !row.isCopy ? row.projectImportantJhVos?.at(8)?.plan :
                            row.projectImportantExceVos?.at(8)?.action }}
                    </template>
                </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup field="quarter4" :title="`${getNumbericalYear}年第四季度`" align="center"
                :visible="columns[9].visible || columns[10].visible || columns[11].visible">
                <vxe-column field="9" title="10月" width="150" align="center" :visible="columns[9].visible">
                    <template v-slot="{ row }">
                        {{ !row.isCopy ? row.projectImportantJhVos?.at(9)?.plan :
                            row.projectImportantExceVos?.at(9)?.action }}
                    </template>
                </vxe-column>
                <vxe-column field="10" title="11月" width="150" align="center" :visible="columns[10].visible">
                    <template v-slot="{ row }">
                        {{ !row.isCopy ? row.projectImportantJhVos?.at(10)?.plan :
                            row.projectImportantExceVos?.at(10)?.action }}
                    </template>
                </vxe-column>
                <vxe-column field="11" title="12月" width="150" align="center" :visible="columns[11].visible">
                    <template v-slot="{ row }">
                        {{ !row.isCopy ? row.projectImportantJhVos?.at(11)?.plan :
                            row.projectImportantExceVos?.at(11)?.action }}
                    </template>
                </vxe-column>
            </vxe-colgroup>
            <vxe-column field="yearGoal" title="年度目标" width="180" align="center" />
        </vxe-table>

        <el-dialog title="重点项目整理" v-model="projectManageShow" width="800px" :align-center="true"
            :close-on-click-modal="false" destroy-on-close>
            <ProjectManage :year="getNumbericalYear" :deptList="deptList" @close="projectManageShow = false;search()" @cancel="projectManageShow = false"></ProjectManage>
        </el-dialog>
        <el-dialog title="重点项目计划" v-model="planManageShow" width="1300px" :align-center="true"
            :close-on-click-modal="false" destroy-on-close>
            <PlanManage :year="getNumbericalYear" :deptList="deptList" @close="planManageShow = false;search()" @cancel="planManageShow = false"></PlanManage>
        </el-dialog>
        <el-dialog title="进展更新" v-model="progressUpdateShow" width="600px" :align-center="true"
            :close-on-click-modal="false" destroy-on-close @closed="month = ''">
            <ProgressUpdate :year="getNumbericalYear" :data="currentRow!" :month="month" @close="progressUpdateShow = false; search()"
                @cancel="progressUpdateShow = false"></ProgressUpdate>
        </el-dialog>
    </div>
</template>
<script lang="tsx" setup>
import { debounce, range } from 'lodash';
import DeptSelect from '@/components/Select/DeptSelect.vue';
import { dayjs } from 'element-plus';
import VxePcUI from 'vxe-pc-ui'
import { VxeTable, VxeColumn, VxeColgroup, VxeTableInstance, VxeUI, VxeTablePropTypes } from 'vxe-table';
import 'vxe-table/styles/cssvar.scss'
import ProjectManage from './component/projectManage.vue';
import PlanManage from './component/planManage.vue';
import { checkIsNumber, dateFormat } from '@/utils/common';
import { getDeptList, getList, getYear } from '@/api/fund/report/important';
import ProgressUpdate from './component/progressUpdate.vue';

// @ts-ignore
VxeUI.use(VxePcUI)

const years = (await getYear()).data
const handleCheckDateDisabled = (date: any) => {
    if (years?.includes(dayjs(date).year())) {
        return false
    }
    return true
}

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_imp_stage_type } = proxy.useDict("project_imp_stage_type");

const getProjectImpStageTypeMap = computed(() => {
    const map = new Map<number, string>()
    if (project_imp_stage_type.value) {
        project_imp_stage_type.value.forEach((t: any) => {
            map.set(parseInt(t.value), t.remark)
        })
    }
    return map
})

const tableRef = ref<VxeTableInstance>()
const tableHeight = ref(window.innerHeight - 190)
const columns = ref([
    { key: 1, label: `1月`, visible: true },
    { key: 2, label: `2月`, visible: true },
    { key: 3, label: `3月`, visible: true },
    { key: 4, label: `4月`, visible: true },
    { key: 5, label: `5月`, visible: true },
    { key: 6, label: `6月`, visible: true },
    { key: 7, label: `7月`, visible: true },
    { key: 8, label: `8月`, visible: true },
    { key: 9, label: `9月`, visible: true },
    { key: 10, label: `10月`, visible: true },
    { key: 11, label: `11月`, visible: true },
    { key: 12, label: `12月`, visible: true },
])

// 必须手动监听列隐藏事件并更新合并，否则无法合并
watch(columns, () => {
    updateMergeCells()
}, { deep: true })

const searchForm = reactive({
    year: String(years?.at(-1)),
    deptId: ''
})
const getNumbericalYear = computed(() => {
    return parseInt(searchForm.year)
})

const deptList = ref<any[]>([])
getDeptList(getNumbericalYear.value).then(res => {
    deptList.value = res.data ?? []
})


const loading = ref(false)
type dataType = (ProjectImportantVo & { isCopy?: boolean })
const tableData = ref<dataType[]>([]);
const search = () => {
    loading.value = true
    getList({ year: getNumbericalYear.value, deptId: searchForm.deptId }).then(res => {
        // tableData.value.length = 0
        const temp:dataType[] = []
        // 把项目复制一份，用于后面的行合并
        res.data?.forEach(item => {
            if (item.projId) {
                temp.push({ ...item, isCopy: false }, { ...item, isCopy: true })
            } else {
                temp.push(item)
            }
        })
        tableData.value = temp
        updateMergeCells()
    }).finally(() => {
        loading.value = false
    })
}
watch(() => [getNumbericalYear.value, searchForm.deptId], () => {
    search()
}, { immediate: true })

const projectManageShow = ref(false)

const planManageShow = ref(false)

const handleDownloadExcel = () => {
}

const handlePrint = () => {
    tableRef.value?.getPrintHtml().then(res => {
        const headHtml = `
        <h1 style="text-align: center;">${getNumbericalYear.value}年度专项发展资金重点推进项目</h1>
        `
        const html = res.html
        tableRef.value?.print({
            html: headHtml + html,
        })
    })
}


const monthMap = new Map([
    ['0', 0], ['1', 1], ['2', 2], ['3', 3], ['4', 4], ['5', 5], ['6', 6], ['7', 7],
    ['8', 8], ['9', 9], ['10', 10], ['11', 11], ['12', 12]
])
const bindCellClassName: VxeTablePropTypes.CellClassName<dataType> = ({ row, column }) => {
    if (column.field === 'progressIssue') {
        if (row.projId) {
            if (!row.progressIssue) {
                return 'progressIssue-unset'
            }
            return 'progressIssue-change'
        }
    }
    if (column.field === 'projName') {
        if (!row.projId && !row.stepName) {
            return 'stepName'
        }
    }
    if (monthMap.has(column.field)) {
        if (row.projId) {
            if (!row.isCopy) {
                return 'iscopy-false'
            } else {
                if (row.projectImportantExceVos?.at(monthMap.get(column.field)!)?.action) {
                    return 'iscopy-true-hasvalue'
                }
                return 'iscopy-true'
            }
        }

    }
}
const bindHeaderCellClassName: VxeTablePropTypes.HeaderCellClassName<any> = ({ column, $rowIndex, $columnIndex }) => {

return 'project-header-custom';
}
const mergeCells = ref<VxeTablePropTypes.MergeCells>([])
const updateMergeCells = () => {
    mergeCells.value.length = 0
    queueMicrotask(() => {
        tableData.value.forEach((row, index) => {
            if (row.projId && !row.isCopy) {
                mergeCells.value.push({ row: index, col: 0, rowspan: 2, colspan: 1 })
                mergeCells.value.push({ row: index, col: 1, rowspan: 2, colspan: 1 })
                mergeCells.value.push({ row: index, col: 2, rowspan: 2, colspan: 1 })
                mergeCells.value.push({ row: index, col: 3, rowspan: 2, colspan: 1 })
                mergeCells.value.push({ row: index, col: 4, rowspan: 2, colspan: 1 })
                mergeCells.value.push({ row: index, col: 5, rowspan: 2, colspan: 1 })
                mergeCells.value.push({ row: index, col: 6, rowspan: 2, colspan: 1 })
                mergeCells.value.push({ row: index, col: 7, rowspan: 2, colspan: 1 })
                mergeCells.value.push({ row: index, col: 7 + 13, rowspan: 2, colspan: 1 })
            }
        })
    });

}

const progressUpdateShow = ref(false)
const month = ref('')
const currentRow = ref<ProjectImportantVo>()
const handleCellClick = (data: { row: any, column: any, }) => {
    currentRow.value = data.row
    if (data.column.field === 'progressIssue') {
        progressUpdateShow.value = true
    }
    if (monthMap.has(data.column.field) && data.row.isCopy) {
        progressUpdateShow.value = true
        month.value = String(monthMap.get(data.column.field)! + 1)
    }
}
</script>

<style scoped>
:deep(.progressIssue-unset) {
    background-color: yellow;
    cursor: pointer;
}

:deep(.progressIssue-unset:hover) {
    background-color: #409EFF;
    cursor: pointer;
}

:deep(.progressIssue-change:hover) {
    background-color: #409EFF;
    cursor: pointer;
}

:deep(.stepName) {
    font-weight: 700;
}

:deep(.iscopy-false) {
    background-color: rgb(177.3, 179.4, 183.6);
}

:deep(.iscopy-true-hasvalue:hover) {
    background-color: #409EFF;
    cursor: pointer;
}

:deep(.iscopy-true) {
    background-color: yellow;
    cursor: pointer;
}

:deep(.iscopy-true:hover) {
    background-color: #409EFF;
}
:deep(.project-header-custom) {
  background: linear-gradient(to bottom, #fafdff, #deeefb)!important; /* 深蓝渐变 */
  text-align: center;
  border-right: 1px solid #d9e0eb !important;
}
</style>