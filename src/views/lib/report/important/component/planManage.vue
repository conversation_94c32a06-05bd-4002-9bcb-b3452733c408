<template>
    <div>
        <el-radio-group v-model="dept" class="gap-[5px_0]" @change="search()">
            <el-radio-button v-for="item in deptList" :label="`${item.deptName}(${item.projCount})`"
                :value="item.deptId" />
        </el-radio-group>
        <div class="mt-3">
            <el-table :data="dataList" border height="500px">
                <el-table-column label="项目名称" prop="projName" width="200px" align="center"></el-table-column>
                <el-table-column label="1月" align="center">
                    <template #default="{ row }">
                        <el-input type="textarea" v-model="row.projectImportantJhVos!.at(0).plan" autosize></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="2月" align="center">
                    <template #default="{ row }">
                        <el-input type="textarea" v-model="row.projectImportantJhVos!.at(1).plan" autosize></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="3月" align="center">
                    <template #default="{ row }">
                        <el-input type="textarea" v-model="row.projectImportantJhVos!.at(2).plan" autosize></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="4月" align="center">
                    <template #default="{ row }">
                        <el-input type="textarea" v-model="row.projectImportantJhVos!.at(3).plan" autosize></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="5月" align="center">
                    <template #default="{ row }">
                        <el-input type="textarea" v-model="row.projectImportantJhVos!.at(4).plan" autosize></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="6月" align="center">
                    <template #default="{ row }">
                        <el-input type="textarea" v-model="row.projectImportantJhVos!.at(5).plan" autosize></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="7月" align="center">
                    <template #default="{ row }">
                        <el-input type="textarea" v-model="row.projectImportantJhVos!.at(6).plan" autosize></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="8月" align="center">
                    <template #default="{ row }">
                        <el-input type="textarea" v-model="row.projectImportantJhVos!.at(7).plan" autosize></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="9月" align="center">
                    <template #default="{ row }">
                        <el-input type="textarea" v-model="row.projectImportantJhVos!.at(8).plan" autosize></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="10月" align="center">
                    <template #default="{ row }">
                        <el-input type="textarea" v-model="row.projectImportantJhVos!.at(9).plan" autosize></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="11月" align="center">
                    <template #default="{ row }">
                        <el-input type="textarea" v-model="row.projectImportantJhVos!.at(10).plan" autosize></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="12月" align="center">
                    <template #default="{ row }">
                        <el-input type="textarea" v-model="row.projectImportantJhVos!.at(11).plan" autosize></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="年度目标" align="center">
                    <template #default="{ row }">
                        <el-input type="textarea" v-model="row.yearGoal" autosize></el-input>
                    </template>
                </el-table-column>
                <template #empty>
                    <el-empty description="暂无数据" />
                </template>
            </el-table>
        </div>
        <div class="relative">
            <div class="flex justify-center mt-5">
                <el-button type="primary" @click="save">保存</el-button>
                <el-button @click="$emit('cancel')">取消</el-button>
            </div>
            <div class="absolute left-0 top-0 flex items-center">
                <span class="text-red">注：月度如有多个目标，每个目标请换行输入（回车）</span>
            </div>
        </div>
    </div>

</template>

<script lang="ts" setup>
import { jhList, saveOrUpdateJhput } from '@/api/fund/report/important';
import { ElMessage } from 'element-plus';


const emit = defineEmits(['close', 'cancel'])

const props = defineProps<{
    year: number
    deptList: any[]
}>()

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_imp_stage_type } = proxy.useDict("project_imp_stage_type");

const dept = ref(props.deptList.at(0).deptId)
const dataList = ref<ProjectImpInputVo[]>([])
const search = () => {
    jhList({ year: props.year, deptId: dept.value }).then(res => {
        dataList.value = res.data ?? []
    })
}
search()

const save = () => {
    const data = dataList.value.map(item => {
        return {
            id: item.id,
            year: item.year,
            relId: item.relId,
            projId: item.projId,
            yearGoal: item.yearGoal,
            projectImportantJhVos: item.projectImportantJhVos
        }
    })
    if (data.length > 0) {
        saveOrUpdateJhput(data).then(res => {
            ElMessage.success('保存成功')
            // emit('close')
        })
    }
}
</script>

<style scoped>
:deep(.el-table__cell) {
    padding: 0;
}
:deep(.el-table__cell .cell) {
    padding: 0;
}
/* :deep(.el-textarea__inner) {
    height: 100%!important;
} */
</style>