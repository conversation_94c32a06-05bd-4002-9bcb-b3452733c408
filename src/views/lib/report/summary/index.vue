<template>
    <div class="app-container relative">
        <div class="flex justify-end">
            <right-toolbar v-model:showSearch="showSearch" @queryTable="search" class="!ml-3">
                <el-tooltip class="item" effect="dark" content="导出Excel" placement="top">
                    <el-button class="ml-3" circle icon="Download" @click="handleDownloadExcel" />
                </el-tooltip>
            </right-toolbar>
            <el-drawer v-model="showSearch" title="查询">
                <el-form label-width="70px">
                    <el-form-item label="年度">
                        <el-date-picker v-model="searchForm.year" type="year" :clearable="false"
                            value-format="YYYY"></el-date-picker>
                    </el-form-item>

                    <el-form-item label="项目名称">
                        <el-input v-model="searchForm.name" placeholder="请输入项目名称" class="w-full"></el-input>
                    </el-form-item>
                    <el-form-item label="项目性质">
                        <el-select v-model="searchForm.natureCode" placeholder="请选择项目性质" class="w-full">
                            <el-option v-for="item in project_nature" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="项目类别">
                        <el-select v-model="searchForm.typeId" placeholder="请选择项目类别" class="w-full">
                            <el-option v-for="item in project_type" :label="item.remark" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="项目用途">
                        <el-select v-model="searchForm.purposeIds" multiple placeholder="请选择项目用途" class="w-full">
                            <el-option v-for="item in project_purpose" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="项目篮子">
                        <el-select v-model="searchForm.basketCode" placeholder="请选择项目篮子" class="w-full">
                            <el-option v-for="item in project_basket_type" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="上报主体">
                        <DeptSelect v-model="searchForm.projectUnits" class="w-45"></DeptSelect>
                    </el-form-item>
                    <el-form-item label="考核主体">
                        <DeptSelect v-model="searchForm.projectUnits" class="w-45"></DeptSelect>
                    </el-form-item>
                    <el-form-item>
                        <el-checkbox v-model="searchForm.isZero" :true-value="'1'"
                            :false-value="''">包含计划为0</el-checkbox>
                    </el-form-item>
                </el-form>

                <template #footer>
                    <div class="text-left">
                        <el-button type="primary" @click="search">查询</el-button>
                        <el-button @click="handleSearchReset">重置</el-button>
                    </div>
                </template>
            </el-drawer>
        </div>
        <div class="mt-2">
            <el-table ref="tableRef" :data="dataList" row-key="id" border show-summary
                :expand-row-keys="['1', '2', '3', '4']" :height="tableHeight"
                :header-cell-style="{ 'text-align': 'center' }" :row-class-name="tableRowClassName"
                :span-method="spanMethod" :summary-method="getSummaries" :indent="0">
                <el-table-column type="index" label="序号" align="center" width="60px">
                    <template #default="{ row, $index }">
                        {{ row.index }}
                    </template>
                </el-table-column>
                <el-table-column label="项目名称">
                    <template #default="{ row }">
                        <ProjNameComponent :row="row" />
                    </template>
                </el-table-column>
                <el-table-column label="项目类别" align="center"></el-table-column>
                <el-table-column label="项目用途" align="center"></el-table-column>
                <el-table-column label="投资金额" align="center">
                    <el-table-column label="投资估算" align="center"></el-table-column>
                    <el-table-column label="实际投资" align="center"></el-table-column>
                </el-table-column>
                <el-table-column label="截止上年度执行" align="center"></el-table-column>
                <el-table-column :label="`${searchForm.year}年`" align="center">
                    <el-table-column label="年初计划" align="center"></el-table-column>
                    <el-table-column label="调整计划" align="center"></el-table-column>
                </el-table-column>
                <el-table-column :label="`${searchForm.year}年执行`" align="center"></el-table-column>
                <el-table-column label="剩余资金需求" align="center">
                    <el-table-column label="除当年执行" align="center"></el-table-column>
                    <el-table-column label="除当年计划" align="center"></el-table-column>
                </el-table-column>
                <template #empty>
                    <el-empty description="暂无数据" />
                </template>
            </el-table>
        </div>

    </div>
</template>

<script setup lang="tsx">
import { dayjs, TableColumnCtx } from 'element-plus';
import DeptSelect from '@/components/Select/DeptSelect.vue';

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_type, project_basket_type } = proxy.useDict("project_purpose", "project_nature", 'project_type', 'project_basket_type');

const ProjNameComponent = defineComponent({
    name: 'projNameComponent',
    props: {
        row: {
            type: Object as () => any,
            required: true
        }
    },
    setup(props) {
        return () => {
            if (props.row.baskType) {
                if (props.row.id == 1) {
                    return (<div class="inline-flex items-center">
                        <span>申报库</span>
                    </div>)
                }
                if (props.row.id == 2) {
                    return (<div class="inline-flex items-center">
                        <span>储备库</span>
                    </div>)
                }
                if (props.row.id == 3) {
                    return (<div class="inline-flex items-center">
                        <span>实施库</span>
                    </div>)
                }
                if (props.row.id == 4) {
                    return (<div class="inline-flex items-center">
                        <span>其它</span>
                    </div>)
                }
            } else {
                return (<div>
                    {/* <div class="text-#056cc4 underline cursor-pointer" onClick={() => handleFormViewShow(props.row)}>{props.row.name}</div>
          <div>编号：{props.row.projectSn?.formalSn ?? props.row.projectSn?.tempSn}</div> */}
                </div>)
            }
        }
    }
})

const tableHeight = ref(window.innerHeight - 180)

const showSearch = ref(false)
const searchForm = reactive({
    year: String(dayjs().year()),
    name: '',
    natureCode: '',
    basketCode: '',
    typeId: '',
    purposeIds: [],
    isZero: '',
    projectUnits: ''
})
const dataList = ref([
    {
        id: 1,
        baskType: 1,
        children: [{ id: 5 }]
    },
    {
        id: 2,
        baskType: 2,
        children: []
    },
    {
        id: 3,
        baskType: 3,
        children: []
    },
    {
        id: 4,
        baskType: 4,
        children: []
    },
])
const search = () => {

}

const handleSearchReset = () => {
    searchForm.name = ''
    searchForm.natureCode = ''
    searchForm.typeId = ''
    searchForm.purposeIds = []
    searchForm.basketCode = ''
    searchForm.isZero = ''
}

const handleDownloadExcel = () => {
}

const tableRowClassName = ({
    row,
    rowIndex
}: {
    row: any,
    rowIndex: number
}) => {
    if (row.baskType) {
        return 'warning-row'
    }
    return ''
}

const spanMethod = ({
    row,
    column,
    rowIndex,
    columnIndex
}: {
    row: any,
    column: any,
    rowIndex: number,
    columnIndex: number
}) => {
    if (row.baskType) {
        if (columnIndex === 0) return [0, 0];        // 被合并掉
        if (columnIndex === 1) return [1, 3];        // 合并3列（1、2、3）
        if (columnIndex === 3) return [0, 0];  // 被合并掉
    }
    return [1, 1]
}


// 设置底部合计行样式
onMounted(() => {
  setTimeout(() => {
    const tfoot = proxy.$el.querySelector('.el-table__footer tfoot')
    tfoot.firstChild.children[0].colSpan = 2
    tfoot.firstChild.children[1].style.display = 'none'
  }, 0);
})
interface SummaryMethodProps<T = typeof dataList.value[number] & { children?: IProjectInfoVo[] }> {
  columns: TableColumnCtx<T>[]
  data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param
  const sums: (string | VNode | number | null)[] = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = (
        <div class="text-left">
          总计
        </div>
      )
    }
    // if (column.property == 'estAmount') {
    //   sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.estAmount)) ?? 0)
    // }
    // if (column.property == 'libAmount') {
    //   sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.libAmount)) ?? 0)
    // }
    // if (column.property == 'checkAmount') {
    //   sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.checkAmount)) ?? 0)
    // }
    // if (column.property == 'payedPrev') {
    //   sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.payedPrev)) ?? 0)
    // }
    // if (column.property == 'payedCurr') {
    //   sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.payedCurr)) ?? 0)
    // }
    // if (column.property == 'payedTotal') {
    //   sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.payedTotal)) ?? 0)
    // }
    // if (column.property == 'planSb') {
    //   sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.planSb)) ?? 0)
    // }
    // if (column.property == 'planXd') {
    //   sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.planXd)) ?? 0)
    // }
    // if (column.property == 'planTzjh') {
    //   sums[index] = roundNumber2(sumAmount(getDataList.value.map(t => t.planTzjh)) ?? 0)
    // }
    // if (column.property == 'planTzxd') {
    //   sums[index] = (
    //     <div>
    //       <span>{roundNumber2(sumAmount(getDataList.value.map(t => t.planTzxd)) ?? 0)}</span><br />
    //       <span class="text-red">{roundNumber2(sumAmount(getDataList.value.map(t => t.planTzxd_red)) ?? 0)}</span>
    //     </div>
    //   )
    // }

  })
  return sums
}
</script>

<style>
.el-table .warning-row {
    /*--el-table-tr-bg-color: var(--el-color-warning-light-9);*/
    background-color: #f3f7fd !important;
    font-weight: 500;
    color: #444;
}
</style>