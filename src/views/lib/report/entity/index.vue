<template>
    <div class="app-container relative">
        <h1 class="text-center leading-9 relative ">
            <el-date-picker v-model="searchForm.year" type="year" class="!w-25" :clearable="false"
                value-format="YYYY"></el-date-picker>
            年资金用款计划分部门汇总表
        </h1>
        <div class="absolute right-[20px] top-12">
            <el-button type="primary" @click="handleDownloadExcel">导出</el-button>
            <el-button type="primary" @click="handlePrint">打印</el-button>
        </div>
        <div>
            <vxe-table ref="tableRef" :data="tableData" style="width: 100%" border>
                <vxe-column prop="date" title="序号" align="center" width="50"></vxe-column>
                <vxe-column prop="date" title="年初计划金额" align="center"></vxe-column>
                <vxe-column prop="date" title="调整计划金额" align="center"></vxe-column>
                <vxe-column prop="date" title="已执行" align="center"></vxe-column>
                <template #empty>
                    <el-empty description="暂无数据" />
                </template>
            </vxe-table>
        </div>

    </div>
</template>

<script setup lang="ts">
import VxePcUI from 'vxe-pc-ui'
import { VxeTable, VxeColumn, VxeColgroup, VxeTableInstance, VxeUI, VxeTablePropTypes } from 'vxe-table';
import 'vxe-table/styles/cssvar.scss'
import { dayjs } from 'element-plus';
// @ts-ignore
VxeUI.use(VxePcUI)

const searchForm = reactive({
    year: String(dayjs().year()),
})

const getNumbericalYear = computed(() => {
    return parseInt(searchForm.year)
})


const tableData = ref([])

const tableRef = ref<VxeTableInstance>()
const handlePrint = () => {
    tableRef.value?.getPrintHtml().then(res => {
        const headHtml = `
        <h1 style="text-align: center;">
        ${getNumbericalYear.value}年资金用款计划分部门汇总表
        </h1>`

        const html = res.html
        tableRef.value?.print({
            html: headHtml + html,
        })
    })
}

const handleDownloadExcel = () => {
}
</script>