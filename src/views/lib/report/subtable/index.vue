<template>
    <div class="app-container relative">
        <h1 class="text-center leading-9 relative ">
            <el-date-picker v-model="searchForm.year" type="year" class="!w-25" :clearable="false"
                value-format="YYYY"></el-date-picker>
            年度化工区专项发展资金用款计划
        </h1>
        <div class="absolute right-[20px] top-12">
            <right-toolbar :search="false" :columns="columns">
                <el-tooltip class="item" effect="dark" content="打印" placement="top">
                    <el-button class="ml-3" circle icon="Printer" @click="handlePrint" />
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="导出Excel" placement="top">
                    <el-button class="ml-3" circle icon="Download" @click="handleDownloadExcel" />
                </el-tooltip>
            </right-toolbar>
        </div>
        <div>
             <div class="flex items-center gap-2">
                <el-select clearable class="w-45">
                    <el-option v-for="item in [{deptName: '综合办', deptId: 100}]" :label="item.deptName" :value="item.deptId"></el-option>
                </el-select>
                <el-select clearable class="w-45">
                    <el-option label="考核主体" value="1"></el-option>
                    <el-option label="上报单位" value="2"></el-option>
                </el-select>
            </div>
            <div class="mt-2">
                <vxe-table ref="tableRef" :data="tableData" style="width: 100%" border>
                <vxe-column prop="date" title="序号" align="center" width="50"></vxe-column>
                <template v-for="col in columns" :key="col.prop">
                    <vxe-column v-if="col.visible" :title="col.label" :field="col.prop" header-align="center"
                        :align="col.prop === 'projName' ? 'left' : 'center'" :width="col.width">
                    </vxe-column>
                </template>
                <template #empty>
                    <el-empty description="暂无数据" />
                </template>
            </vxe-table>
            </div>
        </div>

    </div>
</template>

<script setup lang="ts">
import VxePcUI from 'vxe-pc-ui'
import { VxeTable, VxeColumn, VxeColgroup, VxeTableInstance, VxeUI, VxeTablePropTypes } from 'vxe-table';
import 'vxe-table/styles/cssvar.scss'
import { dayjs } from 'element-plus';
// @ts-ignore
VxeUI.use(VxePcUI)

const searchForm = reactive({
    year: String(dayjs().year()),
})

const getNumbericalYear = computed(() => {
    return parseInt(searchForm.year)
})

const columns = ref([
    { label: '项目名称', prop: 'projName', width: 150, align: 'center', visible: true, disabled: true },
    { label: '项目篮子', prop: 'basketName', width: 100, align: 'center', visible: true },
    { label: '上报单位', prop: 'sbOrgname', align: 'center', visible: true },
    { label: '考核主体', prop: 'assessOrgname', align: 'center', visible: true },
    { label: '项目性质', prop: 'natureName', align: 'center', visible: true },
    { label: '项目估算', prop: 'estAmount', align: 'center', visible: true },
    { label: '入库金额', prop: 'libAmount', align: 'center', visible: true },
    { label: '核定金额', prop: 'checkAmount', align: 'center', visible: true },
    { label: '合同金额', prop: 'contractAmount', align: 'center', visible: true },
    { label: '至上年底累计执行', prop: 'prevAmount', align: 'center', visible: true },
    { label: '上年调整计划', prop: 'prevAdjust', align: 'center', visible: true },
    { label: '上年实际执行', prop: 'prevPayed', align: 'center', visible: true },
    { label: '当年资金需求', prop: 'currAmount', align: 'center', visible: true },
    { label: '当年初步使用计划', prop: 'currEarly', align: 'center', visible: true },
    { label: '当年正式计划', prop: 'currFormal', align: 'center', visible: true },
    { label: '当年调整计划', prop: 'currAdjust', align: 'center', visible: true },
    { label: '当年已执行', prop: 'currPayed', align: 'center', visible: true },
    { label: '开工(采购)时间', prop: 'beginDate', align: 'center', visible: true },
    { label: '竣工(完成)时间', prop: 'endDate', align: 'center', visible: true },
    { label: '备注1', prop: 'remark1', align: 'center', visible: true },
    { label: '备注2', prop: 'remark2', align: 'center', visible: true },
]);



const tableData = ref([])

const tableRef = ref<VxeTableInstance>()
const handlePrint = () => {
    tableRef.value?.getPrintHtml().then(res => {
        const headHtml = `
        <h1 style="text-align: center;">
        ${getNumbericalYear.value}年度化工区专项发展资金用款计划
        </h1>`

        const html = res.html
        tableRef.value?.print({
            html: headHtml + html,
        })
    })
}

const handleDownloadExcel = () => {
}
</script>