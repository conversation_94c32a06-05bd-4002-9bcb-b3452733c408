<template>
  <div class="app-container relative">
    <template v-if="getNumbericalYear >= 2020 && getNumbericalYear <= 2022">
      <old20202022 v-model:year="getNumbericalYear"></old20202022>
    </template>
    <template v-else>
      <h1 class="text-center leading-9 relative ">
        <el-date-picker v-model="searchForm.year" type="year" class="!w-25" :clearable="false"
          value-format="YYYY"></el-date-picker>
        年专项发展资金计划及执行汇总表
        <br />
        {{ getNumbericalYear >= dayjs().year() ? `（截至${getNumbericalYear}.${dayjs().format('M.D')}）` :
          `（截至${getNumbericalYear}.12.31）` }}
      </h1>
      <div class="absolute right-[20px] top-12">
        <el-button type="primary" @click="handleDownloadExcel">导出</el-button>
        <el-button type="primary" @click="handlePrint">打印</el-button>
      </div>
      <div class="custom-style">
        <el-segmented v-model="value" :options="options" size="large" />
      </div>
      <vxe-table ref="tableRef" :data="tableData" style="width: 100%" border :cell-class-name="bindCellClassName"
        :header-cell-class-name="bindHeaderCellClassName">
        <vxe-column prop="date" title="项目分类" >
          <template #default>
            一、投资建设类
          </template>
        </vxe-column>
        <vxe-colgroup title="用款计划(万元)" align="center">
          
          <vxe-column v-for="item in project_purpose" prop="name" :title="item.label" align="center" width="110"></vxe-column>
          <vxe-column prop="name" title="类别总计" align="center" width="110"></vxe-column>
        </vxe-colgroup>
        <vxe-column prop="date" title="占比" align="center" width="110"></vxe-column>
        <vxe-column prop="date" title="已执行" align="center" width="110"></vxe-column>
        <vxe-column prop="date" title="执行率" align="center" width="110"></vxe-column>
        <vxe-column prop="date" title="涉及数字化园区支出" align="center" width="110"></vxe-column>
        <vxe-column prop="date" title="已执行" align="center" width="110"></vxe-column>
        <vxe-column prop="date" title="执行率" align="center" width="110"></vxe-column>
      </vxe-table>
    </template>


  </div>
</template>
<script lang="ts" setup>
import VxePcUI from 'vxe-pc-ui'
import { VxeTable, VxeColumn, VxeColgroup, VxeTableInstance, VxeUI, VxeTablePropTypes } from 'vxe-table';
import 'vxe-table/styles/cssvar.scss'
import { dayjs, type TabsPaneContext } from 'element-plus'
import { checkIsNumber, dateFormat } from '@/utils/common';
import old20202022 from './component/old2020-2022.vue';

// @ts-ignore
VxeUI.use(VxePcUI)

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_type, project_basket_type } = proxy.useDict("project_purpose", "project_nature", 'project_type', 'project_basket_type');


const value = ref('1')
const options = [
  { label: '初步计划', value: '1' },
  { label: '正式计划', value: '2' },
  { label: '调整计划', value: '3' },
]

const searchForm = reactive({
  year: String(dayjs().year()),
})

const getNumbericalYear = computed<number>({
  set(val) {
    searchForm.year = String(val)
  },
  get() {
    return parseInt(searchForm.year)
  }
})



// 定义数据
const tableData = ref([
  {}, {}, {}, {}, {}, {}, {}, {}, {}, {}
]);


//为第七列添加背景色
const bindCellClassName: VxeTablePropTypes.CellClassName<any> = ({ row, column, rowIndex, $columnIndex }) => {
  if ($columnIndex == 1 + project_purpose.value.length) {
    if (rowIndex <= project_type.value.length) {
      return 'cell-zj-bg'
    }
  }
  if (rowIndex == 1 + project_purpose.value.length) {
    if ($columnIndex <= project_type.value.length) {
      return 'cell-zj-bg cell-align-center'
    }
  }
  if (rowIndex > project_purpose.value.length) {
    if ($columnIndex == 0) {
      return 'cell-align-center'
    }
  }
}
const bindHeaderCellClassName: VxeTablePropTypes.HeaderCellClassName<any> = ({ column, $rowIndex, $columnIndex }) => {
  if ($rowIndex == 1 && $columnIndex == project_purpose.value.length) {
    return 'header-cell-zj-bg'
  }
 return 'project-header-custom';
}

const tableRef = ref<VxeTableInstance>()
const handlePrint = () => {
  tableRef.value?.getPrintHtml().then(res => {
    const subTitle = getNumbericalYear.value >= dayjs().year() ? `（截至${getNumbericalYear.value}.${dayjs().format('M.D')}）` : `（截至${getNumbericalYear.value}.12.31）`
    const headHtml = `
        <h1 style="text-align: center;">
        ${getNumbericalYear.value}年专项发展资金计划及执行汇总表
        <br />
  ${subTitle}
        </h1>`

    const html = res.html
    tableRef.value?.print({
      html: headHtml + html,
    })
  })
}

const handleDownloadExcel = () => {
}

</script>

<style scoped>
.custom-style .el-segmented {
  --el-segmented-item-selected-color: white;
  --el-segmented-item-selected-bg-color: var(--el-color-primary-light-3);
  --el-border-radius-base: 16px;
}

:deep(.cell-zj-bg) {
  background-color: #f0f9eb;
  color: black;
  font-size: 16px;
  font-weight: 600;
}

:deep(.header-cell-zj-bg) {
  background-color: #f0f9eb;
}

:deep(.cell-align-center) {
  text-align: center;
}
:deep(.project-header-custom) {
  background: linear-gradient(to bottom, #fafdff, #deeefb)!important; /* 深蓝渐变 */
  text-align: center;
  border-right: 1px solid #d9e0eb !important;
}
</style>


<!-- <style scoped lang="less">
:deep(.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf) {
  border-bottom: solid 1px #000 !important;
}

:deep(.el-table--border .el-table__cell) {
  border-right: solid 1px #000 !important;
}

:deep(.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th) {
  background-color: #fff !important;
}

:deep(.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf) {
  border-bottom: solid 1px #000 !important;
}

:deep(.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th) {
  color: #000;
}

:deep(.el-table td.el-table__cell div) {
  color: #000;
}

:deep(.el-table) {
  border: solid 2px #000;
}

:deep(.el-table--border th.el-table__cell) {
  border-bottom: solid 1px #000;
}

/* 自定义行的样式 */
:deep(.highlight-row) {
  background-color: #f0f9eb;
  /* 第八行的背景色 */
  font-weight: bold;
}

/* 自定义单元格的样式 */
:deep(.highlight-cell) {
  background-color: #f0f9eb;
  /* 第七列的背景色 */
  font-weight: bold;

}

:deep(.highlight-cell1) {
  background-color: #fff;
  /* 第七列的背景色 */
  font-weight: normal;

}

:deep(.bold-text) {
  font-weight: bold;
}

:deep(.center-text) {

  text-align: center;
}

:deep(.pl-text) {
  padding-left: 15px;
}

:deep(.el-table--default .el-table__cell) {
  padding: 12px 0;
}
</style> -->