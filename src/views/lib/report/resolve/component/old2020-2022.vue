<template>
  <h1 class="text-center leading-9 relative">
    <div class="cursor-pointer inline-block" @click="type = type == 1 ? 2 : 1">
      <el-date-picker :modelValue="String(year)" @update:modelValue="(val: string) => year = parseInt(val)" type="year"
        class="!w-25" :clearable="false" value-format="YYYY"></el-date-picker>
      {{ type == 1 ? `年专项发展资金计划及执行汇总表` : `年专项发展资金计划（调整）及执行汇总表` }}
      <br />
      （截至{{ year }}.12.31）
    </div>
  </h1>
  <div class="absolute right-[20px] top-12">
    <el-button type="primary" @click="handleDownloadExcel">导出</el-button>
    <el-button type="primary" @click="handlePrint">打印</el-button>
  </div>
  <vxe-table ref="tableRef" :data="getTbaleData" style="width: 100%" border :cell-class-name="bindCellClassName"
    :header-cell-class-name="bindHeaderCellClassName">
    <vxe-column field="name" title="项目分类" header-align="center"></vxe-column>
    <vxe-colgroup title="用款计划(万元)" align="center">
      <vxe-column field="jcssjswh" title="基础设施建设维护" align="center" width="110"></vxe-column>
      <vxe-column field="aq" title="安全" align="center" width="150"></vxe-column>
      <vxe-column field="lshb" title="绿色环保" align="center" width="150"></vxe-column>
      <vxe-column field="zskc" title="招商科创" align="center" width="150"></vxe-column>
      <vxe-column field="zhyq" title="智慧园区" align="center" width="150"></vxe-column>
      <vxe-column field="qtgl" title="其他管理" align="center" width="150"></vxe-column>
      <vxe-column field="byj" title="备用金" align="center" width="150"></vxe-column>
      <vxe-column field="zj" title="总计" align="center" width="150"></vxe-column>
    </vxe-colgroup>
    <vxe-column field="yzx" title="已执行" align="center" width="150"></vxe-column>
    <vxe-column field="zxl" title="执行率" align="center" width="150"></vxe-column>
  </vxe-table>
</template>

<script setup lang="tsx">
import VxePcUI from 'vxe-pc-ui'
import { VxeTable, VxeColumn, VxeColgroup, VxeTableInstance, VxeUI, VxeTablePropTypes } from 'vxe-table';
import 'vxe-table/styles/cssvar.scss'

// @ts-ignore
VxeUI.use(VxePcUI)

const year = defineModel('year')

const type = ref(1)

const getTbaleData = computed(() => {
  if (year.value == 2020) {
    if (type.value == 1) {
      return [
        { name: '投资建设项目', jcssjswh: '8531', aq: '604', lshb: '54', zskc: '', zhyq: '', qtgl: '2701', byj: '', zj: '11890', yzx: '12010', zxl: '101.0%' },
        { name: '采购项目', jcssjswh: '4921', aq: '8903', lshb: '1288', zskc: '120', zhyq: '38', qtgl: '3043', byj: '', zj: '18313', yzx: '15463', zxl: '84.4%' },
        { name: '公共管理补贴', jcssjswh: '163', aq: '8155', lshb: '163', zskc: '', zhyq: '', qtgl: '2967', byj: '', zj: '11448', yzx: '11999', zxl: '104.8%' },
        { name: '功能性补贴', jcssjswh: '', aq: '', lshb: '', zskc: '', zhyq: '', qtgl: '1750', byj: '', zj: '1750', yzx: '1716', zxl: '98.0%' },
        { name: '政策性扶持', jcssjswh: '', aq: '200', lshb: '3600', zskc: '5000', zhyq: '1500', qtgl: '', byj: '', zj: '10300', yzx: '7348', zxl: '71.3%' },
        { name: '预备金', jcssjswh: '', aq: '', lshb: '', zskc: '', zhyq: '', qtgl: '', byj: '293', zj: '293', yzx: '0', zxl: '0.0%' },
        { name: '总计', jcssjswh: '13615', aq: '17862', lshb: '5105', zskc: '5120', zhyq: '1538', qtgl: '10460', byj: '293', zj: '54000', yzx: '--', zxl: '--' },
        { name: '已执行', jcssjswh: '14211', aq: '16561', lshb: '5151', zskc: '1672', zhyq: '1797', qtgl: '9145', byj: '0', zj: '--', yzx: '48543', zxl: '--' },
        { name: '执行率', jcssjswh: '104.4%', aq: '92.7%', lshb: '100.9%', zskc: '32.6%', zhyq: '116.8%', qtgl: '87.4%', byj: '0.0%', zj: '--', yzx: '--', zxl: '89.9%' },
      ]
    }
    if (type.value == 2) {
      return []
    }
  }
  if (year.value == 2021) {
    if (type.value == 1) {
      return []
    }
    if (type.value == 2) {
      return []
    }
  }
  if (year.value == 2022) {
    if (type.value == 1) {
      return []
    }
    if (type.value == 2) {
      return []
    }
  }
})

//为第七列添加背景色
const bindCellClassName: VxeTablePropTypes.CellClassName<any> = ({ row, column, rowIndex, $columnIndex }) => {
  if ($columnIndex == 8) {
    if (rowIndex <= 6) {
      return 'cell-zj-bg'
    }
  }
  if (rowIndex == 6) {
    if ($columnIndex == 0) {
      return 'cell-zj-bg cell-align-center'
    }
    if ($columnIndex <= 7) {
      return 'cell-zj-bg'
    }
  }
  if (rowIndex > 6) {
    if ($columnIndex == 0) {
      return 'cell-align-center'
    }
  }
}
const bindHeaderCellClassName: VxeTablePropTypes.HeaderCellClassName<any> = ({ column, $rowIndex, $columnIndex }) => {
  if ($rowIndex == 1 && $columnIndex == 7) {
    return 'header-cell-zj-bg'
  }
}

const tableRef = ref<VxeTableInstance>()
const handlePrint = () => {
  tableRef.value?.getPrintHtml().then(res => {
    const headHtml = `
        <h1 style="text-align: center;">
        ${type.value == 1 ? `年专项发展资金计划及执行汇总表` : `年专项发展资金计划（调整）及执行汇总表`}
        <br />
        （截至${year.value}.12.31）
        </h1>`

    const html = res.html
    tableRef.value?.print({
      html: headHtml + html,
    })
  })
}

const handleDownloadExcel = () => {
}
</script>

<style scoped>
:deep(.cell-zj-bg) {
  background-color: #f0f9eb;
  color: black;
  font-size: 16px;
  font-weight: 600;
}

:deep(.header-cell-zj-bg) {
  background-color: #f0f9eb;
}

:deep(.cell-align-center) {
  text-align: center;
}
</style>