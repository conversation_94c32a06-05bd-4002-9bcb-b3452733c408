<template>
    <div id="print" class="app-container relative" v-loading="loading">
        <h1 class="text-center cursor-pointer pb-2 mt-0 mb-1">
            {{ planForm.title }}</h1>
        <div class="flex justify-end">
            <span class="cursor-pointer">{{ planForm.unit }}</span>
        </div>

        <div class="mt-2">
            <VxeTable ref="tableRef" :data="getTableData" :height="tableHeight" :tree-config="bindTreeConfig"
                :row-config="bindRowConfig" :row-drag-config="bindRowDragConfig" auto-resize border>
                <vxe-column field="index" title="序号" width="50" align="center"></vxe-column>
                <template v-for="col in columns" :key="col.prop">
                    <vxe-column v-if="col.visible" :title="col.label" :field="col.prop"
                        :align="col.prop === 'projName' ? 'left' : 'center'" header-align="center" :width="col.width">
                        <template #default="{ row }">
                            <template v-if="col.prop === 'projName'">
                                <div class="flex items-center justify-center">
                                    <span
                                        :class="{ 'font-bold': [EUsageFundPlanItemType.type, EUsageFundPlanItemType.projectGroup].includes(row.itemType) }">
                                        {{ row.projName }}
                                        <br />
                                        {{ row.projSn }}
                                    </span>
                                    <span v-if="row.isImp == '1'" class="text-red">*</span>

                                </div>

                            </template>
                            <template v-else-if="col.prop == 'basketName'">
                                <dict-tag :options="project_basket_type" :value="row.basketId" />
                            </template>
                            <template v-else-if="amountProps.includes(col.prop)">
                                <template v-if="row.id == totalRowId">
                                    <span>{{ row[col.prop] }}</span>
                                </template>
                                <template v-else>
                                    <template v-if="row.itemType == EUsageFundPlanItemType.project">
                                        <span :style="{
                                            color: colorMap.get(row.id + col.prop)
                                        }">{{ row[col.prop] }}</span>
                                    </template>
                                    <template v-else>
                                        <span>{{ row[col.prop + 'Temp'] }}</span>

                                    </template>
                                </template>
                            </template>
                            <template v-else>
                                {{ row[col.prop] }}
                            </template>

                        </template>
                    </vxe-column>
                </template>
            </VxeTable>
        </div>
        <div class="absolute bottom--5 z-10 bg-white">
            <el-button type="danger" @click="$emit('close')">关闭</el-button>
            <!-- <el-button type="primary" @click="handleSave">保存</el-button> -->
            <!-- <el-button type="primary" @click="handleCreateSnap">生成快照</el-button> -->
            <el-button type="primary" @click="handlePrintView">打印</el-button>
            <el-button type="primary" @click="handleDownloadExcel">导出EXCEL</el-button>
            <!-- <el-button type="primary">考核主体汇总</el-button> -->
        </div>
    </div>

</template>

<script setup lang="ts">
import { fromArray, foreach, find, map } from 'tree-lodash'
import Bignumber from 'bignumber.js';
import { VxeTable, VxeColumn, VxeTableInstance, VxeUI } from 'vxe-table'
import VxePcUI from 'vxe-pc-ui'
import 'vxe-table/styles/cssvar.scss'
import { useResizeObserver } from '@vueuse/core';
import { EApplyAdjustState, EUsageFundPlanItemType } from '@/utils/constants';
import { checkIsNumber } from '@/utils/common';
import { dayjs } from 'element-plus';
import { getDetailById } from '@/api/fund/report/usagePlan';
// @ts-ignore
VxeUI.use(VxePcUI)

const props = defineProps<{
    id: string;
}>()

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_type, project_basket_type } = proxy.useDict("project_purpose", "project_nature", 'project_type', 'project_basket_type');


const tableRef = ref<VxeTableInstance>()
// 计算表格高度
const tableHeight = ref(0);
useResizeObserver(document.body, (entries) => {
    const tableRect = tableRef.value?.$el.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    tableHeight.value = windowHeight - tableRect.top - 120;
})
// 总计行的固定id
const totalRowId = '1'
const bindTreeConfig = {
    expandAll: true,
    transform: true,
    rowField: 'id',
    parentField: 'parentId'
}
const bindRowConfig = {
    drag: true
}
const bindRowDragConfig = {
    isCrossDrag: true
}
// 合计单元格字段
const amountProps = [
    'estAmount',
    'libAmount',
    'checkAmount',
    'contractAmount',
    'prevAmount',
    'prevAdjust',
    'prevPayed',
    'currAmount',
    'currEarly',
    'currFormal',
    'currAdjust',
    'currPayed'
];

const columns = ref([
    { label: '项目名称', prop: 'projName', width: 150, align: 'center', visible: true },
    { label: '项目篮子', prop: 'basketName', width: 100, align: 'center', visible: true },
    { label: '上报单位', prop: 'sbOrgname', align: 'center', visible: true },
    { label: '考核主体', prop: 'assessOrgname', align: 'center', visible: true },
    { label: '项目性质', prop: 'natureName', align: 'center', visible: true },
    { label: '项目估算', prop: 'estAmount', align: 'center', visible: true },
    { label: '入库金额', prop: 'libAmount', align: 'center', visible: true },
    { label: '核定金额', prop: 'checkAmount', align: 'center', visible: true },
    { label: '合同金额', prop: 'contractAmount', align: 'center', visible: true },
    { label: '至上年底累计执行', prop: 'prevAmount', align: 'center', visible: true },
    { label: '上年调整计划', prop: 'prevAdjust', align: 'center', visible: true },
    { label: '上年实际执行', prop: 'prevPayed', align: 'center', visible: true },
    { label: '当年资金需求', prop: 'currAmount', align: 'center', visible: true },
    { label: '当年初步使用计划', prop: 'currEarly', align: 'center', visible: true },
    { label: '当年正式计划', prop: 'currFormal', align: 'center', visible: true },
    { label: '当年调整计划', prop: 'currAdjust', align: 'center', visible: true },
    { label: '当年已执行', prop: 'currPayed', align: 'center', visible: true },
    { label: '开工(采购)时间', prop: 'beginDate', align: 'center', visible: true },
    { label: '竣工(完成)时间', prop: 'endDate', align: 'center', visible: true },
    { label: '备注1', prop: 'remark1', align: 'center', visible: true },
    { label: '备注2', prop: 'remark2', align: 'center', visible: true },
]);

const loading = ref(false)
type usagePlanItemTree = IUsagePlanItem & { index?: number, children?: usagePlanItemTree | IUsagePlanItem[] }
const tableData = reactive({
    totalRow: {
        id: totalRowId,
        parentId: '0',
        projName: '总计',
        seqNo: 0
    } as usagePlanItemTree,
    dataList: [] as usagePlanItemTree[],
})
const getTableData = computed(() => {
    return [tableData.totalRow, ...tableData.dataList]
});

const year = useRoute().query.year as string
const planForm = reactive<IUsagePlan>({
    // title: `${year}年度上海化学工业区用款计划表`
} as IUsagePlan)
// 存储哪些单元格文字设置红色，健为id+字段名, 值为颜色值
const colorMap = new Map<string, string>()
const originList = ref<IUsagePlanItem[]>([])
const search = () => {
    loading.value = true
    getDetailById(props.id).then(async res => {
        colorMap.clear()
        if (res.data) {
            planForm.id = res.data?.id
            planForm.year = res.data.year
            planForm.title = res.data.title
            planForm.unit = res.data.unit
            console.time("caculate")
            originList.value = res.data?.dataList ?? []

            tableData.dataList = caculate(JSON.parse(JSON.stringify(originList.value)) ?? [])

            if (res.data?.tableColumns && res.data?.tableColumns != '[]') {
                columns.value = JSON.parse(res.data?.tableColumns) ?? []
            }
            await nextTick(() => {
                resort()
                tableRef.value?.setAllTreeExpand(true)
            })
            console.timeEnd("caculate")
        }


    }).finally(() => loading.value = false)
}
search()



/**
 * 统计金额
 * @param list 
 */
const caculate = (list: IUsagePlanItem[]) => {
    const tree = fromArray(list, { itemKey: 'id', parentKey: 'parentId' })
    foreach(tree, (node) => {
        amountProps.forEach(prop => {
            const tempProp = prop + 'Temp'
            if (node[prop] != null) {
                node[tempProp] = node[prop]
            } else {
                if (node.children) {
                    node[tempProp] = sumAmount(node.children.map((child: IUsagePlanItem) => {
                        if (checkIsNumber(child[tempProp]))
                            return child[tempProp]
                        return 0
                    }))
                }
            }


        })
    }, { strategy: 'post' })

    amountProps.forEach(prop => {
        tableData.totalRow[prop] = sumAmount(tree.map((t: any) => checkIsNumber(t[prop + 'Temp']) ? t[prop + 'Temp'] : 0))
    })
    return list
}

/**
 * 对表格和项目进行排序
 */
const resort = () => {
    let index = 1
    const map = new Map<string, string>()
    foreach(tableRef.value?.getTableData().fullData!, (item) => {
        if (item.itemType == EUsageFundPlanItemType.projectGroup) {
            map.set(item.id, '1')
            if (!map.has(item.parentId)) {
                item.index = index
                index++
            }

        }
        if (item.itemType == EUsageFundPlanItemType.type) {
            if (map.has(item.parentId)) {
                map.set(item.id, '1')
            }
        }
        if (item.itemType == EUsageFundPlanItemType.project) {
            if (!map.has(item.parentId)) {
                item.index = index
                index++
            }

        }
        if (item.children && item.children.length > 0) {
            item.children.sort((a: any, b: any) => a.seqNo ?? 0 - b.seqNo ?? 0)
        }
    })
}



const sumAmount = (nums?: number[]) => {
    if (!nums || nums.length === 0) {
        return '';
    }
    const sum = nums.reduce((acc, num) => {
        if (num === undefined || num === null || isNaN(num)) {
            return acc;
        }
        return acc.plus(new Bignumber(num));
    }, new Bignumber(0));
    return sum.isZero() ? '' : sum.toString();
}


const handlePrintView = () => {
    // 打印的时候如果不把children数据删掉，会重复，这里先暂存children数据，等会再恢复
    const map = new Map<string, any[]>()
    tableRef.value?.getPrintHtml({
        dataFilterMethod: (data: { row: any }) => {
            map.set(data.row.id, data.row.children)
            data.row.children = []
            return true
        }
    }).then(res => {
        const headHtml = `
        <h1 style="text-align: center;">${planForm.title}</h1>
        <div style="text-align: right;margin-bottom: 5px;">${planForm.unit}</div>
        `
        const html = res.html.replaceAll('undefined', '').replaceAll(`style="width:64px"`, `style="width:20px"`)

        tableRef.value?.print({
            html: headHtml + html,
        })
    }).finally(() => {
        tableData.dataList.forEach((item) => {
            if (map.has(item.id!)) {
                item.children = map.get(item.id!)
            }
        })
    })
}

const handleDownloadExcel = () => {
    proxy.download('/project/usagePlan/download', {
    id: props.id
  }, `用款计划表${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}
</script>

<style scoped>
:deep(.cell-change:hover) {
    background-color: #409EFF !important;
    color: white;
    cursor: pointer;
}

:deep(.vxe-cell--tree-node) {
    /* padding-left: 0!important; */
}
</style>
