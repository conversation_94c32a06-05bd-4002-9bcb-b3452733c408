<template>
    <div class="app-container">
        <div class="flex gap-2">
            <el-date-picker v-model="year" type="year" placeholder="选择年份" format="YYYY年" value-format="YYYY"
                class="mb-4" />
            <el-button type="primary" class="mb-4" @click="handleAdd">新增计划</el-button>
        </div>
        <el-table :data="dataList" border row-key="id">
            <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
            <el-table-column prop="year" label="年度" align="center" width="120"></el-table-column>
            <el-table-column prop="title" label="标题" align="center"></el-table-column>
            <el-table-column prop="projectCount" label="项目数量" align="center" width="120"></el-table-column>
            <el-table-column prop="snapCount" label="快照数量" align="center" width="120"></el-table-column>
            <el-table-column prop="updateTime" label="更新时间" align="center" width="180" :formatter="(row: any) => dateFormat(row.updateTime)"></el-table-column>
            <el-table-column prop="closed" label="开放状态" align="center" width="150">
                <template #default="{ row }">
                    <el-switch v-model="row.closed" active-value="0" inactive-value="1"
                        @change="handleClosedChange(row.id)"></el-switch>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="220">
                <template #default="{ row }">
                    <el-button type="primary" link icon="edit"
                        @click="$router.push({ path: 'table/detail', query: { year: row.year } })">编辑</el-button>
                    <el-button type="danger" link icon="delete" @click="handleRemove(row)">删除</el-button>
                    <el-button type="primary" link icon="picture" @click="handleSnapListShow(row)">快照</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-dialog title="快照" v-model="snapListShow" width="500px" :close-on-click-modal="false" destroy-on-close>
            <el-scrollbar height="500px">
                <div v-if="snapList.length > 0" class="flex flex-col gap-3">
                    <div v-for="item in snapList"
                        class="border border-solid border-[var(--el-color-info-light-5)] rounded-sm p-3 cursor-pointer hover:bg-[var(--el-color-primary-light-3)] hover:text-white"
                        @click="handleClickSnap(item)">
                        <span>[{{ dateFormat(item.createTime) }}]</span>
                        <span class="ml-3">{{ item.snapName }}</span>
                    </div>
                </div>
                <div v-else class="text-center">
                    <span class="mt-20">暂无快照</span>
                </div>
            </el-scrollbar>
        </el-dialog>
        <el-dialog title="快照" v-model="snapViewShow" fullscreen :close-on-click-modal="false" destroy-on-close>
            <Preview :id="snapData!.id" @close="snapViewShow = false"></Preview>
        </el-dialog>
    </div>

</template>

<script setup lang="ts">
import { getList, updateTableClose, delByYear, getListSnap } from '@/api/fund/report/usagePlan';
import { dateFormat } from '@/utils/common';
import { ElMessage, ElMessageBox, formatter } from 'element-plus';
import Preview from './preview.vue';

const year = ref<number>()
const router = useRouter()

const dataList = ref<IUsagePlan[]>([]);
const search = () => {
    getList().then(res => {
        dataList.value = res.data ?? [];
    })
}
search()
const handleAdd = () => {
    if (!year.value) {
        ElMessage.error('请选择年份');
        return
    }
    router.push({ path: 'table/detail', query: { year: year.value } });
}

const handleClosedChange = (id: string) => {
    updateTableClose(id).then(res => {
        ElMessage.success('操作成功')
    })
}

const snapListShow = ref(false)
const snapList = ref<IUsagePlan[]>([])
const handleSnapListShow = (row: IUsagePlan) => {
    snapListShow.value = true

    getListSnap(row.year).then(res => {
        snapList.value = res.data ?? []
    })
}

const snapData = ref<IUsagePlan>()
const snapViewShow = ref(false)
const handleClickSnap = (row: IUsagePlan) => { 
    snapViewShow.value = true
    snapData.value = row
}

const handleRemove = (row: IUsagePlan) => {
    ElMessageBox.confirm('是否确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        delByYear(row.year).then(res => {
            ElMessage.success('操作成功')
            search()
        })
    })
}
</script>