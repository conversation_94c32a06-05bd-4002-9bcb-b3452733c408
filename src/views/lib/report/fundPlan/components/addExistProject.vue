<template>
    <div>
        <el-form ref="formRef" :model="searchForm" label-width="80px">
            <el-form-item label="项目名称" prop="name" class="inline-flex">
                <el-input v-model="searchForm.name" placeholder="请输入项目名称" class="w-40"></el-input>
            </el-form-item>
            <el-form-item label="项目单位" prop="projectUnits" class="inline-flex">
                <DeptSelect v-model="searchForm.projectUnits" class="w-40" placeholder="请选择考核主体" clearable></DeptSelect>
            </el-form-item>
            <el-form-item label="考核主体" prop="assessOrgid" class="inline-flex">
                <DeptSelect v-model="searchForm.assessOrgid" class="w-40" placeholder="请选择考核主体" clearable></DeptSelect>
            </el-form-item>
            <el-form-item label="项目用途" prop="purposeIds" class="inline-flex">
                <el-select v-model="searchForm.purposeIds" multiple placeholder="请选择项目用途" class="w-40" clearable>
                    <el-option v-for="item in project_purpose" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="项目类别" prop="typeId" class="inline-flex">
                <el-select v-model="searchForm.typeId" placeholder="请选择项目类别" class="w-40" clearable>
                    <el-option v-for="item in project_type" :label="item.remark" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item class="inline-flex" label-width="50px">
                <el-button type="primary" icon="Search" @click="search">查询</el-button>
                <el-button icon="Refresh" @click="handleClear">重置</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="dataList" size="small" height="500px" border @row-click="handleRowClick">
            <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
            <el-table-column label="项目名称" prop="name" align="center"></el-table-column>
            <el-table-column label="项目单位" prop="assessOrgname" align="center"></el-table-column>
            <el-table-column label="考核主体" prop="assessOrgname" align="center"></el-table-column>
            <el-table-column label="项目(资金)周期" align="center" width="150">
                <template #default="{ row }">
                    起：{{ dateFormat(row.beginDate) }}<br>
                    止：{{ dateFormat(row.endDate) }}
                </template>
            </el-table-column>
            <el-table-column label="项目金额" prop="estAmount" align="center"></el-table-column>
            <el-table-column label="选择" align="center" width="100">
                <template #default="{ row }">
                    <el-icon v-if="selectIds.includes(row.id)" class="text-green text-8"><Select /></el-icon>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <div class="flex justify-center mt-5">
        <el-button type="primary" @click="save">确定</el-button>
        <el-button @click="$emit('close')">取消</el-button>
    </div>
</template>

<script setup lang="ts">
import { getProjectInfoOfPlan } from '@/api/fund/project/info';
import DeptSelect from '@/components/Select/DeptSelect.vue';
import { dateFormat } from '@/utils/common';
import { EUsageFundPlanItemType } from '@/utils/constants';
import { ElMessage, FormInstance } from 'element-plus';
import { v4 as uuidv4 } from 'uuid'

const emit = defineEmits(['close', 'cancel', 'add']);

const props = defineProps<{
    parentId: string;
    planForm: IUsagePlan,
    data?: IUsagePlanItem & { children?: IUsagePlanItem[] }
}>();

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_type, project_basket_type } = proxy.useDict("project_purpose", "project_nature", 'project_type', 'project_basket_type');



const searchForm = reactive({
    year: props.planForm.year,
    projectUnits: '',
    assessOrgid: '',
    purposeIds: [],
    name: '',
    typeId: '',
});
const dataList = ref<IProjectInfoVo[]>([])
const search = () => {
    getProjectInfoOfPlan(searchForm).then(res => {
        dataList.value = res.data ?? [];
    });
}
search()

const formRef = ref<FormInstance>()
const handleClear = () => {
    formRef.value?.resetFields();
    search()
}
const selectIds = ref<string[]>([])
const handleRowClick = (row: IProjectInfoVo) => {
    const index = selectIds.value.indexOf(row.id!)
    if (index !== -1) {
        selectIds.value.splice(index, 1)
    } else {
        selectIds.value.push(row.id!)
    }
}
const save = () => {
    if (selectIds.value.length == 0) {
        ElMessage.error('请选择项目')
        return
    }
    let lastIndex: number
    if (props.data?.children && props.data.children.length > 0) {
        lastIndex = props.data.children.at(-1)?.seqNo ?? 0
    }

    const list = selectIds.value.map(id => {
        return {
            id: uuidv4().replace(/-/g, ''),
            projId: id,
            parentId: props.parentId,
            seqNo: (lastIndex ?? 0) + 1,
            itemType: EUsageFundPlanItemType.project,
        }
    })
    emit('add', list)
    emit('close')
}
</script>