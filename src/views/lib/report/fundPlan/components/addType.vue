<template>
    <el-form ref="formRef" :model="form" :rules="formRules" label-width="auto">
        <el-form-item label="类别名称">
            <el-input v-model="form.projName"></el-input>
        </el-form-item>
        <el-form-item label="汇总方式">
            <el-select v-model="form.sumMode" placeholder="请选择汇总方式">
                <el-option label="自动累加" value="1"></el-option>
                <el-option label="自定义" value="2"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="排序号">
            <el-input-number v-model="form.seqNo" :controls="false"></el-input-number>
        </el-form-item>
    </el-form>
    <div class="flex justify-center mt-5">
        <el-button type="primary" @click="save">确定</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
    </div>
</template>

<script setup lang="ts">
import { EUsageFundPlanItemType } from '@/utils/constants';
import { v4 as uuidv4 } from 'uuid'

const emit = defineEmits(['close', 'cancel', 'add']);
const props = defineProps<{
    parentId: string;
}>();

const form = reactive({
    projName: '',
    sumMode: '1',
    seqNo: ''
});
const formRules = {
    projName: [
        { required: true, message: '请输入类别名称', trigger: 'blur' }
    ],
    sumMode: [
        { required: true, message: '请选择汇总方式', trigger: 'change' }
    ],
    seqNo: [
        { required: true, message: '请输入排序号', trigger: 'blur' }
    ]
};

const formRef = ref();
const save = () => {
    formRef.value.validate((valid: boolean) => {
        if (valid) {
            // 提交表单数据
            emit('add', [
                {
                    id: uuidv4().replace(/-/g, ''),
                    parentId: props.parentId,
                    projName: form.projName,
                    sumMode: form.sumMode,
                    seqNo: form.seqNo,
                    itemType: EUsageFundPlanItemType.type,
                    children: [],
                }
            ]);
            // 关闭弹窗
            emit('close');
        } else {
            console.log('表单验证失败');
            return false;
        }
    });
};
</script>