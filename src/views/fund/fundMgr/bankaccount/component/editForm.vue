<template>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="0" label-position="top">
            <el-descriptions :column="1" border label-width="25%">
                <!-- 开户银行（必填） -->
                <el-descriptions-item :span="2">
                    <template #label>
                        <span class="text-red">*</span> 开户银行
                    </template>
                    <el-form-item prop="bank" class="!mb-0">
                        <el-input v-model="form.bank" placeholder="请输入开户银行" />
                    </el-form-item>
                </el-descriptions-item>

                <!-- 开户网点代码（可留空） -->
                <el-descriptions-item :span="2">
                    <template #label>
                        开户网点
                    </template>
                    <el-form-item prop="branchCode" class="!mb-0">
                        <el-input v-model="form.branchCode" placeholder="可由计财处补充" />
                    </el-form-item>
                    <div style="color: #e6a23c; font-size: 12px; margin-top: 4px;">
                        可留空由计财处补充
                    </div>
                </el-descriptions-item>

                <!-- 户名（必填） -->
                <el-descriptions-item :span="1">
                    <template #label>
                        <span class="text-red">*</span> 户名
                    </template>
                    <el-form-item prop="accountName" class="!mb-0">
                        <el-input v-model="form.accountName" placeholder="请输入户名" />
                    </el-form-item>
                </el-descriptions-item>

                <!-- 户(卡)号（必填） -->
                <el-descriptions-item :span="1">
                    <template #label>
                        <span class="text-red">*</span> 户（卡）号
                    </template>
                    <el-form-item prop="cardNumber" class="!mb-0">
                        <el-input v-model="form.cardNumber" placeholder="请输入账号或卡号" />
                    </el-form-item>
                </el-descriptions-item>

            </el-descriptions>
        </el-form>
    <!-- 操作按钮 -->
    <div class="absolute bottom-0 left-0 w-full h-[50px] flex justify-center items-center">
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="emit('cancel')">关闭窗口</el-button>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { FormRules, FormInstance } from 'element-plus'

const emit = defineEmits(['close', 'cancel'])

const formRef = ref<FormInstance>()
const form = ref({
    bank: '',
    branchCode: '',
    accountName: '',
    cardNumber: '',
    responsible: '',
    contact: '',
})

const rules: FormRules = {
    bank: [{ required: true, message: '请输入开户银行', trigger: 'blur' }],
    accountName: [{ required: true, message: '请输入户名', trigger: 'blur' }],
    cardNumber: [{ required: true, message: '请输入账号或卡号', trigger: 'blur' }],
}

const submitForm = () => {
    formRef.value?.validate((valid) => {
        if (valid) {
            console.log('提交表单：', form.value)
        }
    })
}

const closeWindow = () => {
    console.log('关闭窗口')
}
</script>
