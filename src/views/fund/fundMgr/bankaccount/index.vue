<template>
    <div class="app-container">
        <!-- 搜索区域 -->
        <el-form :inline="true" :model="searchForm">
            <el-form-item label="管理单位">
                <el-input v-model="searchForm.unit" placeholder="请输入管理单位" />
            </el-form-item>
            <el-form-item label="银行名称">
                <el-input v-model="searchForm.bank" placeholder="请输入银行名称" />
            </el-form-item>
            <el-form-item label="账户名称/账号">
                <el-input v-model="searchForm.accountKeyword" placeholder="请输入账户名称或账号" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button type="success" @click="handleCreate">新增收款账户</el-button>
            </el-form-item>
        </el-form>

        <!-- 表格区域 -->
        <el-table :data="tableData" border style="width: 100%">
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="unit" label="管理单位" align="center" />
            <el-table-column prop="bank" label="银行名称" align="center" />
            <el-table-column prop="accountName" label="账户名称" align="center" />
            <el-table-column prop="cardNumber" label="账号" align="center" />
            <el-table-column prop="branchCode" label="开户行联行号" align="center" />
            <el-table-column prop="responsible" label="财务负责人" align="center" />
            <el-table-column prop="contact" label="联系电话" align="center" />
            <el-table-column label="操作" width="150" align="center">
                <template #default="scope">
                    <el-button type="primary" icon="edit" link @click="editRow(scope.row)">编辑</el-button>
                    <el-button type="danger" icon="delete" link @click="deleteRow(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页控件 -->
        <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
            @pagination="handleSearch" />

        <!-- 编辑对话框 -->
        <el-dialog title="账户编辑" v-model="formEditShow" width="600px" :close-on-click-modal="false" destroy-on-close>
            <EditForm />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import EditForm from './component/editForm.vue'


const searchForm = ref({
    unit: '',
    bank: '',
    accountKeyword: ''
})
const page = reactive({
    pageNum: 1,
    pageSize: 10
})
const total = ref(0)
const tableData = ref([])
const formEditShow = ref(false)

const handleSearch = () => {
    console.log('搜索条件：', searchForm.value)
}

const handleCreate = () => {
    formEditShow.value = true
}

const editRow = (row: any) => {
    console.log('编辑账户：', row)
    formEditShow.value = true
}

const deleteRow = (row: any) => {
    console.log('删除账户：', row)
}
</script>
