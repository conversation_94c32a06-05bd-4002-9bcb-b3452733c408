<template>
    <div class="app-container">
        <!-- 搜索区域 -->
        <el-form :inline="true" :model="searchForm" class="mb-4">
            <el-form-item label="项目名称">
                <el-input v-model="searchForm.projectName" placeholder="请输入项目名称" />
            </el-form-item>
            <el-form-item label="项目类型">
                <el-select v-model="searchForm.projectType" placeholder="请选择" class="w-40">
                    <el-option label="科研类" value="research" />
                    <el-option label="基建类" value="construction" />
                </el-select>
            </el-form-item>
            <el-form-item label="项目用途">
                <el-select v-model="searchForm.projectPurpose" placeholder="请选择" class="w-40">
                    <el-option label="专项资金" value="funded" />
                    <el-option label="自有资金" value="self" />
                </el-select>
            </el-form-item>
            <el-form-item label="项目单位">
                <el-select v-model="searchForm.projectUnit" placeholder="请选择" class="w-40">
                    <el-option label="单位A" value="unitA" />
                    <el-option label="单位B" value="unitB" />
                </el-select>
            </el-form-item>
            <el-form-item label="收款单位">
                <el-input v-model="searchForm.receivingUnit" placeholder="请输入收款单位" />
            </el-form-item>
            <el-form-item label="状态">
                <el-select v-model="searchForm.status" placeholder="请选择" class="w-40">
                    <el-option label="草稿" value="draft" />
                    <el-option label="已提交" value="submitted" />
                </el-select>
            </el-form-item>
            <el-form-item label="申请日期">
                <el-date-picker v-model="searchForm.dateRange" type="daterange" start-placeholder="开始日期"
                    end-placeholder="结束日期" range-separator="至" class="!w-60" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button type="success" @click="handleCreate">新建报款单</el-button>
                <el-button type="danger" @click="handleExport">数据导出</el-button>
                <el-button type="info" @click="handleMine">我的报款单</el-button>
            </el-form-item>
        </el-form>

        <!-- 表格区域 -->
        <el-table :data="tableData" border style="width: 100%">
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="projectName" label="项目名称" align="center" />
            <el-table-column prop="paymentCategory" label="支付分类" align="center" />
            <el-table-column prop="reimbursementType" label="报款类型" align="center" />
            <el-table-column prop="projectType" label="项目类型" align="center" />
            <el-table-column prop="projectPurpose" label="项目用途" align="center" />
            <el-table-column prop="receivingUnit" label="收款单位" align="center" />
            <el-table-column prop="applicationAmount" label="申请金额" align="center" />
            <el-table-column prop="approvedAmount" label="核准金额" align="center" />
            <el-table-column prop="applicationDate" label="申请日期" align="center" />
            <el-table-column prop="status" label="状态" align="center" />
            <el-table-column label="操作" width="150" align="center">
                <template #default="scope">
                    <el-button type="primary" link icon="edit" @click="editRow(scope.row)">编辑</el-button>
                    <el-button type="danger" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
            @pagination="handleSearch" />

        <!-- 编辑弹窗 -->
        <el-dialog title="报款单编辑" v-model="formEditShow" width="1200px" :close-on-click-modal="false" destroy-on-close>
            <PayBillForm />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import PayBillForm from './component/payBillForm.vue'

const searchForm = ref({
    projectName: '',
    projectType: '',
    projectPurpose: '',
    projectUnit: '',
    receivingUnit: '',
    status: '',
    dateRange: []
})

const page = reactive({
    pageNum: 1,
    pageSize: 10
})

const total = ref(0)
const tableData = ref([])
const formEditShow = ref(false)

const handleSearch = () => {
    console.log('查询条件：', searchForm.value)
}

const handleCreate = () => {
    formEditShow.value = true
}

const handleExport = () => {
    console.log('导出数据')
}

const handleMine = () => {
    console.log('查看我的报款单')
}

const editRow = (row: any) => {
    console.log('编辑报款单：', row)
    formEditShow.value = true
}

const deleteRow = (row: any) => {
    console.log('删除报款单：', row)
}
</script>
