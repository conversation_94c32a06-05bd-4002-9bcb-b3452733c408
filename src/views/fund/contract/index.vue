<template>
    <div class="app-container">
        <!-- 搜索区域 -->
        <el-form :inline="true" :model="searchForm">
            <el-form-item label="甲方">
                <el-input v-model="searchForm.partyA" placeholder="请输入甲方" />
            </el-form-item>
            <el-form-item label="乙方">
                <el-input v-model="searchForm.partyB" placeholder="请输入乙方" />
            </el-form-item>
            <el-form-item label="所属项目">
                <el-input v-model="searchForm.project" placeholder="请输入所属项目" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button type="success" @click="handleCreate">新建合同</el-button>
            </el-form-item>

        </el-form>

        <!-- 合同表格 -->
        <el-table :data="tableData" border style="width: 100%" @cell-click="handleCellClick">
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="contractNo" label="合同编号" align="center" />
            <el-table-column prop="contractName" label="合同名称" align="center" />
            <el-table-column prop="project" label="所属项目" align="center" />
            <el-table-column prop="partyB" label="乙方" align="center" />
            <el-table-column prop="amount" label="合同金额" align="center" />
            <el-table-column prop="paidTill2019" label="截至2019年底累计拨付" align="center" />
            <el-table-column prop="totalPaid" label="累计拨付总额" align="center" />
            <el-table-column label="操作" width="150" align="center">
                <template #default="scope">
                    <el-button type="primary" icon="edit" link @click="editRow(scope.row)">编辑</el-button>
                    <el-button type="danger" icon="delete" link @click="deleteRow(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
            @pagination="handleSearch" />
    </div>

    <el-dialog title="合同编辑" v-model="formEditShow" width="800px" :close-on-click-modal="false" destroy-on-close>
        <EditForm ref="editFormRef" @cancel="formEditShow = false">
        </EditForm>
        <footer class="el-dialog__footer h-10"></footer>
    </el-dialog>
    <el-dialog title="合同拨款明细" v-model="fundDetailShow" width="1000px" :close-on-click-modal="false" destroy-on-close>
        <FundDetail></FundDetail>
    </el-dialog>

</template>

<script setup lang="ts">
import { ElMessageBox } from 'element-plus'
import EditForm from './component/editForm.vue'
import FundDetail from './component/fundDetail.vue'

const page = reactive({
    pageNum: 1,
    pageSize: 10,
})
const searchForm = ref({
    partyA: '',
    partyB: '',
    project: '',
})
const loading = ref(false)
const total = ref(0)
const tableData = ref([
])

const handleSearch = () => {
    console.log('搜索条件：', searchForm.value)
}

const fundDetailShow = ref(false)
const handleCellClick = (row: any, column: any,) => {
    if (column.property === 'totalPaid') {
        fundDetailShow.value = true
    }
    if (column.property === 'paidTill2019') {
        ElMessageBox.prompt('请输入累计拨付金额', '', {
            type: 'info'
        }).then(res => {

        })
    }
}

const formEditShow = ref(false)
const handleCreate = () => {
    formEditShow.value = true
}

const editRow = (row: any) => {
    console.log('编辑合同：', row)
}

const deleteRow = (row: any) => {
    console.log('删除合同：', row)
}
</script>
