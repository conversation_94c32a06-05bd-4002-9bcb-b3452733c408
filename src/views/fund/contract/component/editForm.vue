<template>
    <el-scrollbar height="500px">
        <el-form :model="form" :rules="rules" ref="formRef" label-position="top" label-width="0" :inline-message="true">
            <el-descriptions :column="2" border label-width="15%" class="equal-width-columns">

                <!-- 合同编号（自动生成） -->
                <el-descriptions-item width="35%">
                    <template #label>合同编号</template>
                    <el-form-item prop="contractNo" class="!mb-0">
                        <el-input v-model="form.contractNo" disabled />
                    </el-form-item>
                </el-descriptions-item>

                <!-- 合同名称（必填） -->
                <el-descriptions-item width="35%">
                    <template #label><span class="text-red">*</span> 合同名称</template>
                    <el-form-item prop="name" class="!mb-0">
                        <el-input v-model="form.name" placeholder="请输入合同名称" />
                    </el-form-item>
                </el-descriptions-item>

                <!-- 甲方（固定） -->
                <el-descriptions-item :span="1">
                    <template #label>甲方</template>
                    <el-form-item prop="partyA" class="!mb-0">
                        <el-input v-model="form.partyA" disabled />
                    </el-form-item>
                </el-descriptions-item>

                <!-- 乙方（必填） -->
                <el-descriptions-item :span="1">
                    <template #label>
                        <span class="text-red">*</span> 乙方
                    </template>
                    <el-form-item prop="partyB" class="!mb-0">
                        <el-input v-model="form.partyB" placeholder="请输入乙方名称" />
                    </el-form-item>
                </el-descriptions-item>

                <!-- 所属项目（必填，可弹窗选择） -->
                <el-descriptions-item :span="1">
                    <template #label><span class="text-red">*</span> 所属项目</template>
                    <el-form-item prop="projectName" class="!mb-0">
                        <el-input v-model="form.projectName" placeholder="请选择所属项目" readonly>
                            <template #suffix>
                                <el-icon>
                                    <Search />
                                </el-icon>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-descriptions-item>

                <!-- 合同金额（必填） -->
                <el-descriptions-item :span="1">
                    <template #label><span class="text-red">*</span> 合同金额 (元)</template>
                    <el-form-item prop="amount" class="!mb-0">
                        <el-input v-model="form.amount" placeholder="请输入合同金额" />
                    </el-form-item>
                </el-descriptions-item>

                <!-- 签订日期 -->
                <el-descriptions-item :span="1">
                    <template #label>签订日期</template>
                    <el-form-item prop="signDate" class="!mb-0">
                        <el-date-picker v-model="form.signDate" type="date" placeholder="选择签订日期" style="width: 100%" />
                    </el-form-item>
                </el-descriptions-item>

                <!-- 起止日期 -->
                <el-descriptions-item :span="1">
                    <template #label>起止日期</template>
                    <el-form-item prop="dateRange" class="!mb-0">
                        <el-date-picker v-model="form.dateRange" type="daterange" range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期" style="width: 100%" />
                    </el-form-item>
                </el-descriptions-item>

                <!-- 合同内容 -->
                <el-descriptions-item :span="2">
                    <template #label>合同内容</template>
                    <el-form-item prop="content" class="!mb-0">
                        <el-input v-model="form.content" type="textarea" rows="4" placeholder="请输入合同主要内容" />
                    </el-form-item>
                </el-descriptions-item>

                <!-- 合同附件 -->
                <el-descriptions-item :span="2">
                    <template #label>合同附件</template>
                    <my-file-upload accept=".doc,.docx,.pdf" :data="{
                        sourceId: form.id,
                        sourceType: EFileSoureType.CONTRACT,
                    }" @upload-success="searchFileList"></my-file-upload>
                    <div class="mt-2">
                        <FileTable :file-list="fileList" @update="searchFileList"
                            :hiddenColumns="['index', 'primaryType']">
                        </FileTable>
                    </div>
                </el-descriptions-item>
            </el-descriptions>

        </el-form>
    </el-scrollbar>
    <!-- 操作按钮 -->
    <div class="absolute bottom-0 left-0 w-full h-[50px] flex justify-center items-center">
        <el-button type="success" @click="submitForm">保存</el-button>
        <el-button @click="closeWindow">关闭窗口</el-button>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Search } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { EFileSoureType } from '@/utils/constants'
import { getFileList1 } from '@/api/fund/project/file';
import FileTable from '@/components/Table/FileTable.vue';

const emit = defineEmits(['close', 'cancel'])

const formRef = ref<FormInstance>()
const form = reactive({
    id: '',
    contractNo: '自动生成',
    name: '',
    partyA: '上海化学工业区管理委员会',
    partyB: '',
    projectName: '',
    amount: '',
    signDate: '',
    dateRange: [],
    content: '',
})

const rules = reactive<FormRules<typeof form>>({
    name: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
    partyB: [{ required: true, message: '请输入乙方', trigger: 'blur' }],
    projectName: [{ required: true, message: '请选择所属项目', trigger: 'change' }],
    amount: [{ required: true, message: '请输入合同金额', trigger: 'blur' }],
})

const submitForm = () => {
    formRef.value?.validate((valid) => {
        if (valid) {
            //   console.log('提交表单：', form.value)
        }
    })
}

const primaryType = { type: '合同', name: '合同' }
const fileList = ref<ISysAttachment[]>([{}, {}, {}]);
const searchFileList = () => {
    getFileList1(form.id, primaryType.type).then(res => {
        fileList.value = res.data ?? [];
    });
};
searchFileList()

const closeWindow = () => {
    emit('cancel')
}

defineExpose({
    closeWindow,
})

</script>


<style scoped>
/* 深度选择器 */
.equal-width-columns :deep(.el-descriptions__table) {
    table-layout: fixed;
    width: 100%;
}

.equal-width-columns :deep(.el-descriptions__table tr > td) {
    width: 50%;
}
</style>
