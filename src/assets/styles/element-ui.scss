// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse
  > div
  > .el-submenu
  > .el-submenu__title
  .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link{
  color: var(--el-color-primary) !important;
}
.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf{
  border-bottom:solid 1px #d9e0eb;
}
.el-table--border .el-table__cell{border-right:solid 1px #d9e0eb;}
.el-descriptions__label.el-descriptions__cell.is-bordered-label{background-color: #f7fafe;}

// .vxe-table--render-default.border--full .vxe-header--column{background-image: linear-gradient(to bottom, #fafdff, #deeefb)!important;background-size:cover!important }
/* 表头样式 */


/* 边框样式 */
.vxe-table--render-default.border--full .vxe-table--border-line {
  border: 1px solid #d9e0eb !important;
}
.vxe-header--column {
  background-image: linear-gradient(#d9e0eb, #d9e0eb), 
                   linear-gradient(#d9e0eb, #d9e0eb) !important;
  background-size: 100% 1px, 1px 100%;
  color: #333 !important;
}
:deep(.vxe-table--header-wrapper),
:deep(.vxe-header--row),
:deep(.vxe-header--column) {
  background-color: #2C3846 !important; /* 深蓝色背景 */
}
.vxe-header--row{background-image: linear-gradient(to bottom, #fafdff, #deeefb)!important; }
.vxe-body--column{color: #606266; }
