<template>
  <water-mark>
      <router-view />
  </water-mark>  
</template>

<script setup>
import useSettingsStore from '@/store/modules/settings'
import { handleThemeStyle } from '@/utils/theme'
// import WaterMark from './components/WaterMark'

onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme)
  })
})
</script>

<style>
.pagination-container{
  margin-top: 15px!important;
}
.el-drawer__header{
  margin-bottom: 15px!important;
}
.splitpanes__splitter{
  margin-left: 0!important;
  border-left: 0!important;
}
.el-scrollbar__view>.router-link-active{
  border-radius: 10px;
}
/* .el-scrollbar>.scrollbar-wrapper, .sidebar-logo-container, .submenu-title-noDropdown, .el-sub-menu__title{
  background-color: #e3f0ff!important;
} */
#app .sidebar-container{ width:auto!important;}
</style>
