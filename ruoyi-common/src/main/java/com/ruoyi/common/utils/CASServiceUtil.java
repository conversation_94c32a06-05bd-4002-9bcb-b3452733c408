package com.ruoyi.common.utils;

import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.http.HttpHeaders;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.security.cert.X509Certificate;

public class CASServiceUtil {
	
	public static void main(String[] args) {
        String cookie = "CASPRIVACY=\"\"; CASTGC=TGT-53-Dez9BzTXVPRPmrCEdMar4mqeXB46bjq0WdWQUYA1tqXavIn5nT-cas01.example.org; JSESSIONID=JSESSIONID=********************************";
        String url = "http://*************:8085/hn-cas/login?service=http://localhost:8088";
        try {
            sendGetByCookie(url,cookie);
        }catch (Exception e){
            e.printStackTrace();
        }

    }
	
	
	/**
     * 验证ST
     */
    public static String getSTValidate(String url,String st, String service){
		try {
			url = url+"?service="+service+"&ticket="+st;
			CloseableHttpClient httpclient = createHttpClientWithNoSsl();
			HttpGet httpget = new HttpGet(url);
			HttpResponse response = httpclient.execute(httpget);
	        String res = readResponse(response);
	        return res == null ? null : (res == "" ? null : res);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "";
	}

    
    /**
     * 读取 response body 内容为字符串
     *
     * @param response
     * @return
     * @throws IOException
     */
    private static String readResponse(HttpResponse response) throws IOException {
        BufferedReader in = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
        String result = new String();
        String line;
        while ((line = in.readLine()) != null) {
            result += line;
        }
        return result;
    }
    
    
    /**
     * 创建模拟客户端（针对 https 客户端禁用 SSL 验证）
     *
     * @return
     * @throws Exception
     */
    private static CloseableHttpClient createHttpClientWithNoSsl() throws Exception {
        // Create a trust manager that does not validate certificate chains
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }

                    @Override
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                        // don't check
                    }

                    @Override
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                        // don't check
                    }
                }
        };

        SSLContext ctx = SSLContext.getInstance("TLS");
        ctx.init(null, trustAllCerts, null);
        LayeredConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(ctx);
        return HttpClients.custom()
                .setSSLSocketFactory(sslSocketFactory)
                .build();
    }

    public static String sendGetByCookie(String url, String cookies) throws Exception {
        try {
            CloseableHttpClient httpclient = createHttpClientWithNoSsl();
            HttpGet httpget = new HttpGet(url);
            httpget.addHeader(HttpHeaders.COOKIE,  cookies);
            HttpResponse response = httpclient.execute(httpget);
            String res = readResponse(response);
            return res == null ? null : (res == "" ? null : res);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

}
