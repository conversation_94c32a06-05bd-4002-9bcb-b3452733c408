package com.ruoyi.common.utils;

/**
 * <AUTHOR>
 * @date 2025/7/1 09:54
 */
public class FillObjectHolder {
    private static final ThreadLocal<Object> CURRENT_OBJECT = new ThreadLocal<>();

    public static void set(Object obj) {
        CURRENT_OBJECT.set(obj);
    }

    public static Object get() {
        return CURRENT_OBJECT.get();
    }

    public static void clear() {
        CURRENT_OBJECT.remove();
    }
}

