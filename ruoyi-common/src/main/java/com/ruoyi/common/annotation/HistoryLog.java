package com.ruoyi.common.annotation;

import com.ruoyi.common.constant.CommonConstant;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2025/6/11 17:00
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface HistoryLog {

    /**
     * 类型
     */
    CommonConstant.ProjectOrPlan Type();

    /**
     * 变更原因
     */
    String updateReason() default "";

}
