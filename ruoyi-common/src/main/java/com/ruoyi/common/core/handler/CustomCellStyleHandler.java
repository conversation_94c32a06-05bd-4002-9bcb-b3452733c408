package com.ruoyi.common.core.handler;


import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/17 21:23
 */
public class CustomCellStyleHandler implements CellWriteHandler {

    @Override
    public void afterCellDispose(CellWriteHandlerContext context) {
        // 获取填充的值
        Object cellValue = context.getOriginalValue();

        WriteCellData<?> cellData = context.getFirstCellData();
        WriteCellStyle cellStyle = cellData.getOrCreateStyle();

        int columnIndex = context.getColumnIndex();

        // 示例1：预算列为红色字体
//        if (columnIndex == 6 && cellValue instanceof BigDecimal) {
//            BigDecimal budget = (BigDecimal) cellValue;
//            if (budget.compareTo(new BigDecimal("3000000")) > 0) {
//                WriteFont font = new WriteFont();
//                font.setColor(IndexedColors.GREY_25_PERCENT.getIndex());
//                cellStyle.setWriteFont(font);
//            }
//        }

        // 示例2：“未上报”列为红色字体
        if ((columnIndex == 5 || columnIndex == 11) && cellValue instanceof String val) {
            if (val.contains("未上报")) {
                WriteFont font = new WriteFont();
                font.setColor(IndexedColors.RED.getIndex());
                cellStyle.setWriteFont(font);
//                cellStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
//                cellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
            }
        }


        cellData.setWriteCellStyle(cellStyle);
    }
}
