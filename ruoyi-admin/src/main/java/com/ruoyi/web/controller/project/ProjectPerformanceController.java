package com.ruoyi.web.controller.project;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.web.DynamicQueryHelper;
import com.ruoyi.common.web.RequestHelper;
import com.ruoyi.system.domain.dto.ProjectPerformanceDto;
import com.ruoyi.system.domain.project.ProjectPerformance;
import com.ruoyi.system.service.project.ProjectPerformanceService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 项目绩效目标Controller
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Api(value = "项目绩效目标控制器", tags = {"项目绩效目标管理"})
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@RequestMapping("/project/performance")
public class ProjectPerformanceController {

    private final ProjectPerformanceService projectPerformanceService;

    /**
     * 查询统计局特色参考值数据列表
     */
    @ApiOperation("项目绩效目标数据列表")
    @GetMapping("/list")
    public R<List<ProjectPerformance>> list(RequestHelper requestHelper) {
        LambdaQueryWrapper<ProjectPerformance> wrapper = DynamicQueryHelper.lambdaQueryWrapper(requestHelper);
        return R.ok(projectPerformanceService.list(wrapper));
    }

    /**
     * 获取项目绑定的项目绩效目标信息
     */
    @ApiOperation("获取项目绑定的项目绩效目标信息")
    @GetMapping("/{pid}")
    public R<ProjectPerformanceDto> getInfo(@ApiParam("主键")
                                            @NotNull(message = "主键不能为空")
                                            @PathVariable("pid") String pid) {
        return R.ok(projectPerformanceService.getPerformanceInfo(pid));
    }

    /**
     * 新增或修改项目绩效目标数据
     */
    @ApiOperation("新增或修改项目绩效目标数据")
    @PostMapping()
    @RepeatSubmit(interval = 1000, message = "请勿重复提交")
    public R<Void> add(@Validated @RequestBody ProjectPerformanceDto entity) {
        return projectPerformanceService.saveOrUpdateInfo(entity) ? R.ok() : R.fail();
    }


    /**
     * 删除项目绩效目标数据
     */
    @ApiOperation("删除项目绩效目标数据")
    @DeleteMapping("/{uids}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable String[] uids) {
        return projectPerformanceService.removeBatchByIds(Arrays.asList(uids)) ? R.ok() : R.fail();
    }
}
