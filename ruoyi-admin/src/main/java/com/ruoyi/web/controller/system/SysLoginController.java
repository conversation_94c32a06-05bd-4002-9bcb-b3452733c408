package com.ruoyi.web.controller.system;

import java.util.List;
import java.util.Set;

import com.ruoyi.framework.config.properties.CasProperties;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysUserService;
import io.swagger.annotations.ApiOperation;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.CASServiceUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.XmlUtils;

import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.system.service.ISysMenuService;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;


/**
 * 登录验证
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
public class SysLoginController
{
    @Resource
    private SysLoginService loginService;

    @Resource
    private ISysMenuService menuService;

    @Resource
    private SysPermissionService permissionService;

    @Resource
    private ISysDeptService deptService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private CasProperties casProperties;

    /**
     * 登录方法
     * 
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    @ApiOperation("常规登录接口")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     * 
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }

    @GetMapping("/cas/login")
    @ApiOperation("单点登录接口")
    public AjaxResult validateLogin(@RequestParam(name="ticket") String ticket,
                                    @RequestParam(name="service") String service,
                                    HttpServletRequest request) throws Exception {
        log.info("Rest api login.");
        try {
            String validateUrl = casProperties.getCasServerUrl()+"/p3/serviceValidate";
            String res = CASServiceUtil.getSTValidate(validateUrl, ticket, service);
            log.info("res."+res);
            final String error = XmlUtils.getTextForElement(res, "authenticationFailure");
            if(StringUtils.isNotEmpty(error)) {
                throw new Exception(error);
            }
            final String principal = XmlUtils.getTextForElement(res, "user");

            String authcodes = XmlUtils.getTextForElement(res, "authcodes");
            log.info("-------user----authcodes---"+authcodes);
            if (StringUtils.isEmpty(principal)) {
                throw new Exception("No principal was found in the response from the CAS server.");
            }
            log.info("-------token----username---"+principal);
            //1. 校验用户是否有效
            SysUser sysUser = sysUserService.selectUserByUserName(principal);

            if(sysUser ==null){
                //新增user对象
                sysUser = new SysUser();
                sysUser.setUserName(principal);
                sysUser.setNickName("新用户");
                sysUser.setPassword(SecurityUtils.encryptPassword("123456"));
                sysUserService.insertUser(sysUser);
            }else{
                if(StringUtils.equals(sysUser.getStatus(), "1")) {
                    return AjaxResult.error("账户已禁用!!!");
                }
            }

            String token = loginService.casLogin(sysUser.getUserName(), request);
            // 设置超时时间
//	 		redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
//	 		redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME*2 / 1000);

            AjaxResult ajax = AjaxResult.success();
            ajax.put(Constants.TOKEN, token);
            return ajax;
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
    }
    @GetMapping("/logout")
    @ApiOperation("登出接口")
    public void logout(HttpServletRequest request, HttpServletResponse response) throws Exception {
        loginService.logout(request,response);
    }
}
