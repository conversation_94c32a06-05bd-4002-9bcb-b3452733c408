package com.ruoyi.web.controller.test;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.service.ISysUserService;
import jakarta.annotation.Resource;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@RestController
@RequestMapping("/test")
public class TestMController {

    private ExecutorService executorService = Executors.newThreadPerTaskExecutor(Thread.ofVirtual().name("virtualthread#", 1).factory());

    @Resource
    ISysUserService userService;

    @Anonymous
    @GetMapping("test")
    public AjaxResult test() throws InterruptedException, ExecutionException {
//        Thread t1 = Thread.ofVirtual().name("t1").factory().newThread(() -> {
//            lists.add(userService.selectUserByUserName("admin"));
//        });
//        t1.start();
//        t1.
        CompletableFuture<SysUser> admin = getUser("admin");
        CompletableFuture<SysUser> ry = getUser("ry");
        CompletableFuture<SysUser> sd = getUser("sd");
        return AjaxResult.success(admin.thenApply(a -> {
            try {
                return sd.get().getUserId();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }
        }).get());
    }

    @Anonymous
    @GetMapping("test1")
    public AjaxResult test1() {
        List<SysUser> lists = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            lists.add(userService.selectUserByUserName("admin"));
        }
        return AjaxResult.success(lists);
    }

    @Async
    public CompletableFuture<SysUser> getUser(String userName) throws InterruptedException {
        return CompletableFuture.supplyAsync(()-> {
            return userService.selectUserByUserName(userName);
        }, executorService);
    }
}
