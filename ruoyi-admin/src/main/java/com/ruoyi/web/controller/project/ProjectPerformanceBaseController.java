package com.ruoyi.web.controller.project;

import java.util.Arrays;
import java.util.List;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.domain.project.ProjectPerformanceBase;
import com.ruoyi.system.service.project.ProjectPerformanceBaseService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 项目绩效指标库Controller
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Api(value = "项目绩效指标库控制器", tags = {"项目绩效指标库管理"})
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@RequestMapping("/project/performanceBase")
public class ProjectPerformanceBaseController {

    private final ProjectPerformanceBaseService projectPerformanceBaseService;

    /**
     * 查询项目绩效指标库列表
     */
    @ApiOperation("查询项目绩效指标库列表")
    @GetMapping("/list")
    public R<List<ProjectPerformanceBase>> list() {
        return R.ok(projectPerformanceBaseService.list());
    }

    /**
     * 获取项目绩效指标库详细信息
     */
    @ApiOperation("获取项目绩效指标库详细信息")
    @GetMapping("/{id}")
    public R<ProjectPerformanceBase> getInfo(@ApiParam("主键")
                                             @NotNull(message = "主键不能为空")
                                             @PathVariable("id") Integer id) {
        return R.ok(projectPerformanceBaseService.getById(id));
    }

    /**
     * 新增项目绩效指标库
     */
    @ApiOperation("新增项目绩效指标库")
    @PostMapping()
    public R<Void> add(@RequestBody ProjectPerformanceBase entity) {
        return projectPerformanceBaseService.save(entity) ? R.ok() : R.fail();
    }


    /**
     * 修改项目绩效指标库
     */
    @ApiOperation("修改项目绩效指标库")
    @PutMapping()
    public R<Void> edit(@RequestBody ProjectPerformanceBase entity) {
        return projectPerformanceBaseService.updateById(entity) ? R.ok() : R.fail();
    }

    /**
     * 删除项目绩效指标库
     */
    @ApiOperation("删除项目绩效指标库")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Integer[] ids) {
        return projectPerformanceBaseService.removeBatchByIds(Arrays.asList(ids)) ? R.ok() : R.fail();
    }
}
