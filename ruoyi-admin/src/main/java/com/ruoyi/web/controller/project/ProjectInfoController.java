package com.ruoyi.web.controller.project;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.handler.CustomCellStyleHandler;
import com.ruoyi.system.domain.dto.AdjustPlanDto;
import com.ruoyi.system.domain.dto.ProjectSummaryDto;
import com.ruoyi.system.domain.excel.ProjectInfoExcel;
import com.ruoyi.system.domain.excel.ProjectInfoMainExcel;
import com.ruoyi.system.domain.excel.ProjectInfoTotalExcel;
import com.ruoyi.system.domain.project.ProjectInfo;
import com.ruoyi.system.domain.vo.project.*;
import com.ruoyi.system.service.project.ProjectInfoService;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/06/03 16:43
 */
@RestController
@RequestMapping("/project/info")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ProjectInfoController {

    private final ProjectInfoService projectInfoService;

    @ApiOperation("查询正常项目列表")
    @GetMapping("/normalList")
    public R<List<ProjectInfoVo>> normalList(ProjectQueryConditionVo vo) {
        return R.ok(projectInfoService.getNormalList(vo));
    }

    @ApiOperation("查询回收站项目列表")
    @GetMapping("/abnormalList")
    public R<List<ProjectInfoVo>> abnormalList(ProjectQueryConditionVo vo) {
        return R.ok(projectInfoService.getAbnormalList(vo));
    }

    @ApiOperation("查询项目详情")
    @GetMapping("/{id}/{year}")
    public R<ProjectInfoVo> getInfo(@PathVariable("id") String id, @PathVariable("year") Integer year) {
        return R.ok(projectInfoService.getProjectDetail(id, year));
    }

    @ApiOperation("新增项目信息")
    @PostMapping()
    @RepeatSubmit(interval = 1000, message = "请勿重复提交")
    public R<Map<String, String>> add(@RequestBody ProjectInfoVo entity) {
        return R.ok(projectInfoService.saveProjectInfo(entity));
    }

    @ApiOperation("修改项目信息")
    @PutMapping("/info")
    public R<Void> editInfo(@RequestBody ProjectInfo entity) {
        return projectInfoService.updateProjectInfo(entity) ? R.ok() : R.fail();
    }

    @ApiOperation("修改项目概述")
    @PutMapping("/summary")
    public R<Void> editSummary(@Validated @RequestBody ProjectSummaryDto entity) {
        return projectInfoService.updateProjectSummary(entity) ? R.ok() : R.fail();
    }

    @ApiOperation("查询可复制项目列表")
    @GetMapping("/getReproducibleProject")
    public R<List<ProjectInfoCopyVo>> getReproducibleProject(ProjectQueryConditionVo vo) {
        return R.ok(projectInfoService.getReproducibleProject(vo));
    }

    @ApiOperation("复制项目")
    @PutMapping("/copyProject")
    @RepeatSubmit(interval = 1000, message = "请勿重复提交")
    public R<Void> copyProject(@NotNull(message = "项目id不能为空") String[] pid, @NotNull(message = "年不能为空") Integer year) {
        return projectInfoService.copyProject(pid, year) ? R.ok() : R.fail();
    }

    @ApiOperation("移动下年")
    @PutMapping("/moveNextYear")
    public R<Void> moveNextYear(@NotNull(message = "项目id不能为空") String pid, @NotNull(message = "年不能为空") Integer year) {
        return projectInfoService.moveNextYear(pid, year) ? R.ok() : R.fail();
    }

    @ApiOperation("项目和计划同时上报")
    @PutMapping("/reportProject")
    public R<Void> reportProject(@NotNull(message = "项目id不能为空") String pid, @NotNull(message = "年不能为空") Integer year) {
        return projectInfoService.reportProject(pid, year) ? R.ok() : R.fail();
    }

    @ApiOperation("仅项目上报")
    @PutMapping("/onlyReportProject")
    public R<Void> onlyReportProject(@NotNull(message = "项目id不能为空") String pid) {
        return projectInfoService.onlyReportProject(pid) ? R.ok() : R.fail();
    }

    @ApiOperation("项目和计划撤回")
    @PutMapping("/withdrawProject")
    public R<Void> withdrawProject(@NotNull(message = "项目id不能为空") String pid, @NotNull(message = "年不能为空") Integer year) {
        return projectInfoService.withdrawProject(pid, year) ? R.ok() : R.fail();
    }

    @ApiOperation("仅项目撤回")
    @PutMapping("/onlyWithdrawProject")
    public R<Void> onlyWithdrawProject(@NotNull(message = "项目id不能为空") String pid) {
        return projectInfoService.onlyWithdrawProject(pid) ? R.ok() : R.fail();
    }

    @ApiOperation("移动到回收站")
    @PutMapping("/putTrash/{pid}/{year}")
    public R<Void> putTrash(@NotNull(message = "项目id不能为空") @PathVariable("pid") String pid, @NotNull(message = "年不能为空") @PathVariable("year") Integer year) {
        return projectInfoService.putTrash(pid, year) ? R.ok() : R.fail();
    }

    @ApiOperation("恢复项目")
    @PutMapping("/recoveryProject/{pid}/{year}")
    public R<Void> recoveryProject(@NotNull(message = "项目id不能为空") @PathVariable("pid") String pid, @NotNull(message = "年不能为空") @PathVariable("year") Integer year) {
        return projectInfoService.recoveryProject(pid, year) ? R.ok() : R.fail();
    }

    @ApiOperation("回收站删除项目")
    @DeleteMapping("/removeTrash/{pid}/{year}")
    public R<Void> remove(@NotNull(message = "项目id不能为空") @PathVariable("pid") String pid, @NotNull(message = "年不能为空") @PathVariable("year") Integer year) {
        return projectInfoService.removeYearRelationByPid(pid, year) ? R.ok() : R.fail();
    }

    @ApiOperation("删除项目")
    @DeleteMapping("/remove/{pid}")
    public R<Void> remove(@NotNull(message = "项目id不能为空") @PathVariable("pid") String pid) {
        return projectInfoService.removeProjectById(pid) ? R.ok() : R.fail();
    }

    @ApiOperation("项目退回")
    @PutMapping("/backProject")
    public R<Void> backProject(@NotNull(message = "项目id不能为空") String pid) {
        return projectInfoService.backProject(pid) ? R.ok() : R.fail();
    }

    @ApiOperation("计划调整")
    @PutMapping("/adjustPlan")
    public R<Void> adjustPlan(@RequestBody AdjustPlanDto dto) {
        return projectInfoService.adjustPlan(dto) ? R.ok() : R.fail();
    }

    @ApiOperation("撤销计划调整")
    @PutMapping("/withdrawAdjustPlan")
    public R<Void> withdrawAdjustPlan(@NotNull(message = "项目id不能为空") String pid, @NotNull(message = "年不能为空") Integer year) {
        return projectInfoService.withdrawAdjustPlan(pid, year) ? R.ok() : R.fail();
    }

    @ApiOperation("查询用款计划表项目列表")
    @GetMapping("/getProjectInfoOfPlan")
    public R<List<ProjectInfoOfPlanVo>> getProjectInfoOfPlan(ProjectQueryOfUsagePlanConditionVo vo) {
        return R.ok(projectInfoService.getProjectInfoOfPlan(vo));
    }

    @ApiOperation("列表导出")
    @PostMapping("/download")
    public void exportExcel(ProjectQueryConditionVo vo, HttpServletResponse response) throws IOException {

        ProjectInfoMainExcel mainExcel = projectInfoService.getProjectInfoMainExcel(vo);
        List<ProjectInfoExcel> jzList = mainExcel.getJzList();
        ProjectInfoTotalExcel jzTotal = mainExcel.getJzTotal();
        List<ProjectInfoExcel> jcList = mainExcel.getJcList();
        ProjectInfoTotalExcel jcTotal = mainExcel.getJcTotal();
        List<ProjectInfoExcel> xzList = mainExcel.getXzList();
        ProjectInfoTotalExcel xzTotal = mainExcel.getXzTotal();
        ProjectInfoTotalExcel total = mainExcel.getTotal();

        String encodedFilename = URLEncoder.encode("项目(计划)申报.xlsx", StandardCharsets.UTF_8);
        // 设置响应头
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + encodedFilename + "\"");
        // 定义模板位置
        InputStream template = getClass().getClassLoader().getResourceAsStream("excel/申报模板.xlsx");
        assert template != null;
        Workbook workbook = WorkbookFactory.create(template);
        Sheet sheet = workbook.getSheetAt(0);

        // Excel索引从0开始
        if (xzList.isEmpty() && sheet.getRow(10) != null) {
            removeRow(sheet,10);
        }
        if (jcList.isEmpty() && sheet.getRow(7) != null) {
            removeRow(sheet,7);
        }
        if (jzList.isEmpty() && sheet.getRow(4) != null) {
            removeRow(sheet,4);
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        template.close();
        workbook.close();
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(outputStream.toByteArray());
        // 使用EasyExcel写出
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerWriteHandler(new CustomCellStyleHandler())
                .withTemplate(byteArrayInputStream)
                .build();
        byteArrayInputStream.close();
        WriteSheet writeSheet = EasyExcel.writerSheet()
                .build();

        FillConfig fillConfig = FillConfig.builder().forceNewRow(true).build();
        // 填充数据
        excelWriter.fill(mainExcel, writeSheet);

        if (!xzList.isEmpty()) {
            excelWriter.fill(new FillWrapper("xzList", xzList), fillConfig, writeSheet);
        }
        if (!jzList.isEmpty()){
            excelWriter.fill(new FillWrapper("jzList", jzList), fillConfig, writeSheet);
        }
        if (!jcList.isEmpty()){
            excelWriter.fill(new FillWrapper("jcList", jcList), fillConfig, writeSheet);
        }
        ArrayList<ProjectInfoTotalExcel> arr1 = new ArrayList<>();
        arr1.add(jzTotal);
        ArrayList<ProjectInfoTotalExcel> arr2 = new ArrayList<>();
        arr2.add(jcTotal);
        ArrayList<ProjectInfoTotalExcel> arr3 = new ArrayList<>();
        arr3.add(xzTotal);
        ArrayList<ProjectInfoTotalExcel> arr4 = new ArrayList<>();
        arr4.add(total);
        excelWriter.fill(new FillWrapper("jzTotal", arr1), fillConfig, writeSheet);
        excelWriter.fill(new FillWrapper("jcTotal", arr2), fillConfig, writeSheet);
        excelWriter.fill(new FillWrapper("xzTotal", arr3), fillConfig, writeSheet);
        excelWriter.fill(new FillWrapper("total", arr4), fillConfig, writeSheet);
        excelWriter.finish();

    }

    // 删除指定行并上移后续行
    private static void removeRow(Sheet sheet, int rowIndex) {
        int lastRowNum = sheet.getLastRowNum();
        if (rowIndex >= 0 && rowIndex < lastRowNum) {
            sheet.shiftRows(rowIndex + 1, lastRowNum, -1);
        } else if (rowIndex == lastRowNum) {
            Row removingRow = sheet.getRow(rowIndex);
            if (removingRow != null) {
                sheet.removeRow(removingRow);
            }
        }
    }
}
