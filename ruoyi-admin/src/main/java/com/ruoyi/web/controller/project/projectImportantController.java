package com.ruoyi.web.controller.project;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.domain.project.ProjectImportant;
import com.ruoyi.system.domain.vo.project.*;
import com.ruoyi.system.service.project.ProjectImportantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

@Api(value = "重点项目清单表控制器", tags = {"重点项目清单表管理"})
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@RequestMapping("/project/important")
public class projectImportantController {

    private final ProjectImportantService projectImportantService;

    /**
     * 查查询重点项目清单
     */
    @ApiOperation("查询重点项目清单")
    @GetMapping("/list")
    public R<List<ProjectImportantVo>> List(ProjectImportantConditionVo vo) {
        return R.ok(projectImportantService.getList(vo));
    }

    /**
     * 获取重点项目详情，由于列表有些记录是表外记录，无法全部用ID，只能用relId获取
     * @param relId
     * @return
     */
    @ApiOperation("查询重点项目详情")
    @GetMapping("/{relId}")
    public R<ProjectImportantVo> getImportInfo(@PathVariable("relId") String relId) {
        return R.ok(projectImportantService.getImportProjDetail(relId));
    }

    /**
     * 保存或修改重点项目记录数据
     * @param entity
     * @return
     */
    @ApiOperation("保存或修改重点项目记录数据")
    @PostMapping("/saveOrUpdatePjimportant")
    @RepeatSubmit(interval = 1000, message = "请勿重复提交")
    public R<ProjectImportant> saveOrUpdatePjimportant(@Validated @RequestBody ProjectImportantVo entity) throws JsonProcessingException {
        ProjectImportantVo projectImportantVo = null;
        if (entity.getRelId() != null) {
             projectImportantVo=projectImportantService.getImportProjDetail(entity.getRelId());
            if(entity.getProgressIssue()!=null){
                projectImportantVo.setProgressIssue(entity.getProgressIssue());
            }
            if(entity.getYearGoal()!=null){
                projectImportantVo.setYearGoal(entity.getYearGoal());
            }
        }
        // 判断 某月月度实施管理是否有数据
        if(entity.getProjectImportantExceVo()!=null){
            //如果有，则取该月的月份
            Integer targetMonth = entity.getProjectImportantExceVo().getMonth();
             if (projectImportantVo.getMonthAction() != null) {
                 ObjectMapper objectMapper = new ObjectMapper();
                 //判断 projectImportantExceVos 是否有该月实施计划
                boolean exists = false;
                List<ProjectImportantExceVo> vos=objectMapper.readValue(projectImportantVo.getMonthAction(), new TypeReference<List<ProjectImportantExceVo>>() {});
                exists = vos.stream().anyMatch(vo -> vo.getMonth().equals(targetMonth));
                if (exists) {
                    // 移除相同月份的子对象
                    vos.removeIf(vo -> vo.getMonth().equals(targetMonth));
                }
                 // 添加新的子对象
                 vos.add(entity.getProjectImportantExceVo());
                 Collections.sort(vos, (o1, o2) -> o1.getMonth().compareTo(o2.getMonth()));
                 entity.setProjectImportantExceVos(vos);
                 //将vos转换成json字符串
                 ObjectMapper objectMapper1 = new ObjectMapper();
                 String json = objectMapper1.writeValueAsString(vos);
                 projectImportantVo.setMonthAction(json);
            }
        }
        ProjectImportant projectImportant = BeanUtil.copyProperties(projectImportantVo, ProjectImportant.class);
        projectImportantService.saveOrUpdate(projectImportant);
        return R.ok(projectImportant);
    }

    /**
     * 重点项目整理-->按部门筛选  部门清单
     */
    @ApiOperation("重点项目整理 部门清单")
    @GetMapping("/deptList")
    public R<List<ProjectImpVo>> deptList(ProjectImportantConditionVo vo) {
        return R.ok(projectImportantService.getDeptList(vo));
    }
    @ApiOperation("重点项目-》项目管理 录入清单")
    @GetMapping("/piList")
    public R<List<ProjectImpInputVo>> piList(ProjectImportantConditionVo vo) {
        return R.ok(projectImportantService.getPiList(vo));
    }

    @ApiOperation("重点项目-》计划管理 录入清单")
    @GetMapping("/jhList")
    public R<List<ProjectImpInputVo>> jhList(ProjectImportantConditionVo vo) {
        return R.ok(projectImportantService.getJhList(vo));
    }

    /**
     * 重点项目整理-->按按阶段筛选
     */
    @ApiOperation("重点项目整理 部门清单")
    @GetMapping("/stepList")
    public R<List<ProjectImpStepVo>> stepList(ProjectImportantConditionVo vo) {
        return R.ok(projectImportantService.geStepList(vo));
    }
    /**
     * 保存项目整理录入清单
     */
    @ApiOperation("保存项目整理")
    @PostMapping("/saveOrUpdatePiput")
    @RepeatSubmit(interval = 1000, message = "请勿重复提交")
    public R<Void> saveOrUpdatePiput(@RequestBody List<ProjectImpInputVo>  projectImpInputVos) {
        return projectImportantService.saveOrUpdatePinput(projectImpInputVos) ? R.ok() : R.fail();
    }

    /**
     * 保存计划管理录入清单
     */
    @ApiOperation("保存计划管理")
    @PostMapping("/saveOrUpdateJhput")
    @RepeatSubmit(interval = 1000, message = "请勿重复提交")
    public R<Void> saveOrUpdateJhput(@RequestBody List<ProjectImpInputVo>  projectImpInputVos) throws JsonProcessingException {
        return projectImportantService.saveOrUpdateJhinput(projectImpInputVos) ? R.ok() : R.fail();
    }

    /**
     * 获取 year 年代
     */
    @ApiOperation("获取 year 年代")
    @GetMapping("/getYear")
    public R<List<Integer>> getYear() {
        return R.ok(projectImportantService.getYear());
    }
    /**
     * (test) 测试
     */
    @ApiOperation("测试")
    @GetMapping("/normalList")
    public R<List<ProjectImportantVo>> normalList(ProjectImportantConditionVo vo) {
        return R.ok(projectImportantService.getNormalList(vo));
    }

}
