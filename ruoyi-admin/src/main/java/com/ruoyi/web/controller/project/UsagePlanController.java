package com.ruoyi.web.controller.project;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.constant.CommonConstant;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.project.ProjectPlan;
import com.ruoyi.system.domain.project.UsagePlan;
import com.ruoyi.system.domain.project.UsagePlanItem;
import com.ruoyi.system.domain.project.UsagePlanTableColumn;
import com.ruoyi.system.domain.vo.project.ProjectInfoVo;
import com.ruoyi.system.service.project.ProjectInfoService;
import com.ruoyi.system.service.project.UsagePlanItemService;
import com.ruoyi.system.service.project.UsagePlanService;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 用款计划控制器
 *
 * <AUTHOR>
 * @date 2025/06/25 14:05
 */
@RestController("myUsagePlanController")
@RequestMapping("/project/usagePlan")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class UsagePlanController {

    private final UsagePlanService usagePlanService;
    private final UsagePlanItemService usagePlanItemService;
    private final ProjectInfoService projectInfoService;
    private final static List<String> LEFT_COLUMN_PROPS = Arrays.asList("projName", "beginDate", "endDate", "remark1", "remark2");
    private final static List<String> CENTER_COLUMN_PROPS = Arrays.asList("basketName", "sbOrgname", "assessOrgname", "natureName");

    private final static List<String> RIGHT_COLUMN_PROPS = Arrays.asList("estAmount", "libAmount", "checkAmount", "contractAmount", "prevAmount", "prevAdjust", "prevPayed", "currAmount", "currEarly", "currFormal", "currAdjust", "currPayed");


    @ApiOperation("下载excel")
    @PostMapping("/download")
    public void exportExcel(@NotNull(message = "计划表id不能为空") String id, HttpServletResponse response) throws IOException {

        UsagePlan usagePlan = usagePlanService.lambdaQuery()
                .eq(UsagePlan::getId, id)
                .one();
        int year = Integer.parseInt(usagePlan.getYear());
        String title = usagePlan.getTitle();
        String encodedFilename = URLEncoder.encode(title + ".xlsx", StandardCharsets.UTF_8);
        // 设置响应头
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + encodedFilename + "\"");

        String columns = usagePlan.getColumns();
        List<UsagePlanTableColumn> tableColumns = JSON.parseArray(columns, UsagePlanTableColumn.class);

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("用款计划表");

        // 表头字段
        ArrayList<String> headName = new ArrayList<>();
        ArrayList<String> columnProps = new ArrayList<>();
        headName.add("序号");
        for (UsagePlanTableColumn column : tableColumns) {
            if (column.isVisible()) {
                headName.add(column.getLabel());
                columnProps.add(column.getProp());
            }
        }


        Row titleRow = sheet.createRow(0);
        //设置行高
        titleRow.setHeightInPoints(50);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(title);
        //设置单元格样式
        CellStyle titleStyle = getCellStyle(workbook, "黑体", 18, true, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, false);
        titleCell.setCellStyle(titleStyle);

        Row twoRow = sheet.createRow(1);
        Cell twoRowLastCell = twoRow.createCell(0);
//        style.setWrapText(true);  // 开启换行
        twoRowLastCell.setCellValue(usagePlan.getUnit());
        CellStyle twoRowStyle = getCellStyle(workbook, "黑体", 10, false, HorizontalAlignment.RIGHT, VerticalAlignment.CENTER, false);

        twoRowLastCell.setCellStyle(twoRowStyle);

        // 合并指定行的指定列
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headName.size() - 1));
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, headName.size() - 1));

        // 创建表头行（第3行，索引从0开始）
        Row headerRow = sheet.createRow(2);
        //设置行高
        headerRow.setHeightInPoints(50);
        CellStyle headerStyle = getCellStyle(workbook, "黑体", 12, true, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, true);

        // 填充表头
        for (int i = 0; i < headName.size(); i++) {
            Cell cell = headerRow.createCell(i);
            String name = headName.get(i);
            if (name.contains("1")) {
                name = name.replace("1", "");
            } else if (name.contains("2")) {
                name = name.replace("2", "");
            } else if (name.contains("上年")) {
                name = name.replace("上年", String.valueOf(year - 1) + "年");
            } else if (name.contains("当年")) {
                name = name.replace("当年", String.valueOf(year) + "年");
            }
            cell.setCellValue(name);
            cell.setCellStyle(headerStyle);
//            sheet.autoSizeColumn(i);
            // 设置每列宽度为 25 个字符（适配中文）
            if (i == 0) {
                sheet.setColumnWidth(i, 5 * 256);
            } else {
                sheet.setColumnWidth(i, CommonConstant.UsagePlanColumnType.getWidthByName(columnProps.get(i - 1)) * 256);
            }
        }


        List<UsagePlanItem> usagePlanItems = usagePlanItemService.lambdaQuery()
                .eq(UsagePlanItem::getFundId, usagePlan.getId())
                .list();
        List<UsagePlanItem> itemList = handleData(usagePlanItems, year);

        AtomicInteger projectNum = new AtomicInteger(0);
        AtomicInteger rowIndex = new AtomicInteger(3);

        //添加总合计
        UsagePlanItem planItem = computeTotal(itemList);
        insertExcelRow(workbook, sheet, rowIndex, projectNum, columnProps, planItem);

        for (UsagePlanItem usagePlanItem : itemList) {
            insertExcelRow(workbook, sheet, rowIndex, projectNum, columnProps, usagePlanItem);
        }

        // 导出文件
        workbook.write(response.getOutputStream());
        workbook.close();
        System.out.println("Excel 文件生成成功！");

    }

    public void insertExcelRow(Workbook workbook, Sheet sheet, AtomicInteger rowIndex, AtomicInteger projectNum, List<String> columnProps, UsagePlanItem usagePlanItem) {
        Row row = sheet.createRow(rowIndex.get());
        row.setHeightInPoints(30);
        List<UsagePlanItem> children = usagePlanItem.getChild();

        // 大类
        if (usagePlanItem.getItemType() == 0) {
            // 创建序号单位格
            CellStyle indexCellStyle = getCellStyle(workbook, "黑体", 10, false, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, true);
            Cell indexCell = row.createCell(0);
            indexCell.setCellValue("");
            indexCell.setCellStyle(indexCellStyle);
            for (int i = 0; i < columnProps.size(); i++) {
                Cell cell = row.createCell(i + 1);
                CellStyle cellStyle;
                if (CommonConstant.UsagePlanColumnType.COLUMN_1.value.equals(columnProps.get(i))) {
                    cellStyle = getCellStyle(workbook, "黑体", 10, true, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, true);
                } else {
                    cellStyle = getCellStyle(workbook, "黑体", 10, false, HorizontalAlignment.RIGHT, VerticalAlignment.CENTER, true);
                }
                cell.setCellValue(getCellValue(columnProps.get(i), usagePlanItem));
                cell.setCellStyle(cellStyle);

            }
        } else if (usagePlanItem.getItemType() == 2) {
            // 创建序号单位格
            CellStyle indexCellStyle = getCellStyle(workbook, "黑体", 10, false, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, true);
            Cell indexCell = row.createCell(0);
            int index = projectNum.addAndGet(1);
            indexCell.setCellValue(index);
            indexCell.setCellStyle(indexCellStyle);
            for (int i = 0; i < columnProps.size(); i++) {
                Cell cell = row.createCell(i + 1);
                CellStyle cellStyle;
                if (CommonConstant.UsagePlanColumnType.COLUMN_1.value.equals(columnProps.get(i))) {
                    cellStyle = getCellStyle(workbook, "黑体", 10, false, HorizontalAlignment.LEFT, VerticalAlignment.CENTER, true);
                } else {
                    cellStyle = getCellStyle(workbook, "黑体", 10, false, HorizontalAlignment.RIGHT, VerticalAlignment.CENTER, true);
                }
                cell.setCellValue(getCellValue(columnProps.get(i), usagePlanItem));
                cell.setCellStyle(cellStyle);
            }
        } else {
            // 创建序号单位格
            CellStyle indexCellStyle = getCellStyle(workbook, "黑体", 10, false, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, true);
            Cell indexCell = row.createCell(0);
            if (projectNum != null) {
                int index = projectNum.addAndGet(1);
                indexCell.setCellValue(index);
                indexCell.setCellStyle(indexCellStyle);
            } else {
                indexCell.setCellValue("");
                indexCell.setCellStyle(indexCellStyle);
            }
            for (int i = 0; i < columnProps.size(); i++) {
                Cell cell = row.createCell(i + 1);
                CellStyle cellStyle;
                if (RIGHT_COLUMN_PROPS.contains(columnProps.get(i))) {
                    cellStyle = getCellStyle(workbook, "黑体", 10, false, HorizontalAlignment.RIGHT, VerticalAlignment.CENTER, true);

                } else if (CENTER_COLUMN_PROPS.contains(columnProps.get(i))) {
                    cellStyle = getCellStyle(workbook, "黑体", 10, false, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, true);
                } else {
                    cellStyle = getCellStyle(workbook, "黑体", 10, false, HorizontalAlignment.LEFT, VerticalAlignment.CENTER, true);
                }
                cell.setCellValue(getCellValue(columnProps.get(i), usagePlanItem));
                cell.setCellStyle(cellStyle);

            }
        }

        rowIndex.addAndGet(1);

        if (children != null) {
            for (UsagePlanItem child : children) {
                if (usagePlanItem.getItemType() == 2) {
                    insertExcelRow(workbook, sheet, rowIndex, null, columnProps, child);
                } else {
                    insertExcelRow(workbook, sheet, rowIndex, projectNum, columnProps, child);
                }
            }
        }
    }

    private List<UsagePlanItem> handleData(List<UsagePlanItem> usagePlanItems, Integer year) {
        List<ProjectInfoVo> projectDetailForUsagePlan = projectInfoService.getProjectDetailForUsagePlan(year);
        Map<String, ProjectInfoVo> projectInfoMap = projectDetailForUsagePlan.stream().collect(Collectors.toMap(ProjectInfoVo::getId, p -> p));
        for (UsagePlanItem usagePlanItem : usagePlanItems) {
            String projId = usagePlanItem.getProjId();
            if (StringUtils.isNotBlank(projId)) {
                ProjectInfoVo infoVo = projectInfoMap.get(projId);
                //将项目信息绑定到对应用款明细中
                usagePlanItem.setProjectInfo(infoVo);
                //用款明细金额和项目信息金额合并
                if (Objects.isNull(usagePlanItem.getEstAmount())) {
                    usagePlanItem.setEstAmount(infoVo.getEstAmount());
                }
                if (Objects.isNull(usagePlanItem.getLibAmount())) {
                    usagePlanItem.setLibAmount(infoVo.getLibAmount());
                }
                if (Objects.isNull(usagePlanItem.getCheckAmount())) {
                    usagePlanItem.setCheckAmount(infoVo.getCheckAmount());
                }
                if (Objects.isNull(usagePlanItem.getContractAmount())) {
                    usagePlanItem.setContractAmount(infoVo.getContractAmount());
                }
                ProjectPlan prevFundPlan = infoVo.getPrevFundPlan();
                if (Objects.nonNull(prevFundPlan)) {
                    if (Objects.isNull(usagePlanItem.getPrevAdjust())) {
                        //todo 待完善 至上年底累计执行
                        usagePlanItem.setPrevAmount(null);
                    }
                    if (Objects.isNull(usagePlanItem.getPrevAdjust())) {
                        usagePlanItem.setPrevAdjust(prevFundPlan.getAdjustAmount());
                    }
                    if (Objects.isNull(usagePlanItem.getPrevPayed())) {
                        //todo 待完善 上年实际执行
                        usagePlanItem.setPrevPayed(null);
                    }
                }
                ProjectPlan fundPlan = infoVo.getFundPlan();
                if (Objects.nonNull(fundPlan)) {
                    if (Objects.isNull(usagePlanItem.getCurrAmount())) {
                        usagePlanItem.setCurrAmount(fundPlan.getDeclareAmount());
                    }
                    if (Objects.isNull(usagePlanItem.getCurrEarly())) {
                        //todo 待完善  当年初步使用计划 当年正式计划
                        //todo 待完善  由后台一个配置项进行配置，不同的配置会设置不同字段的金额
                        usagePlanItem.setCurrEarly(null);
                    }
                    if (Objects.isNull(usagePlanItem.getCurrFormal())) {
                        usagePlanItem.setCurrFormal(fundPlan.getFormalAmount());
                    }
                    if (Objects.isNull(usagePlanItem.getCurrAdjust())) {
                        if (CommonConstant.ApplyStateType.CONFIRM.value.equals(fundPlan.getApplyState())) {
                            usagePlanItem.setCurrAdjust(fundPlan.getAdjustAmount());
                        } else {
                            usagePlanItem.setCurrAdjust(fundPlan.getFormalAmount());
                        }
                    }
                    if (Objects.isNull(usagePlanItem.getCurrPayed())) {
                        //todo 待完善  当年已执行
                        usagePlanItem.setCurrPayed(null);
                    }

                }
            }
            usagePlanItem.setChild(usagePlanItems.stream().filter(e -> e.getParentId().equals(usagePlanItem.getId())).toList());
        }
        List<UsagePlanItem> list = usagePlanItems.stream()
                .filter(e -> "0".equals(e.getParentId()))
                .sorted(Comparator.comparingInt(UsagePlanItem::getSeqNo))
                .toList();
        list.forEach(this::recursionData);
        return list;
    }

    /**
     * 递归求和：返回该节点及其所有子孙叶子的属性和
     */
    private UsagePlanItem recursionData(UsagePlanItem node) {
        if (node.getChild().isEmpty()) {
            return node;
        }
        UsagePlanItem total = new UsagePlanItem();
        for (UsagePlanItem child : node.getChild()) {
            UsagePlanItem childSum = recursionData(child);
            total.add(childSum);
        }
        node.setEstAmount(total.getEstAmount());
        node.setLibAmount(total.getLibAmount());
        node.setCheckAmount(total.getCheckAmount());
        node.setContractAmount(total.getContractAmount());
        node.setPrevAmount(total.getPrevAmount());
        node.setPrevAdjust(total.getPrevAdjust());
        node.setPrevPayed(total.getPrevPayed());
        node.setCurrAmount(total.getCurrAmount());
        node.setCurrEarly(total.getCurrEarly());
        node.setCurrFormal(total.getCurrFormal());
        node.setCurrAdjust(total.getCurrAdjust());
        node.setCurrPayed(total.getCurrPayed());
        return total;
    }

    private String getCellValue(String columnProps, UsagePlanItem usagePlanItem) {
        ProjectInfoVo projectInfo = usagePlanItem.getProjectInfo();
        if (CommonConstant.UsagePlanColumnType.COLUMN_1.value.equals(columnProps)) {
            String projName = usagePlanItem.getProjName();
            if (projectInfo != null && projectInfo.getProjectSn() != null) {
                String formalSn = projectInfo.getProjectSn().getFormalSn();
                if (StringUtils.isBlank(formalSn)) {
                    formalSn = projectInfo.getProjectSn().getTempSn();
                }

                return projName + "\n" + formalSn;
            }
            return projName;
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_2.value.equals(columnProps)) {
            if (projectInfo == null) {
                return null;
            }
            return projectInfo.getBasketName();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_3.value.equals(columnProps)) {
            if (projectInfo == null) {
                return null;
            }
            return projectInfo.getSubmitOrgname();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_4.value.equals(columnProps)) {
            if (projectInfo == null) {
                return null;
            }
            return projectInfo.getAssessOrgname();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_5.value.equals(columnProps)) {
            if (projectInfo == null) {
                return null;
            }
            String natureCode = projectInfo.getNatureCode();
            if ("JC".equals(natureCode)) {
                return "经常性";
            } else {
                String natureName = "阶段性";
                if (projectInfo.getCurrentYearRelation() == null) {
                    return natureName;
                }
                if (projectInfo.getCurrentYearRelation().getProjType() == 1) {
                    natureName += "-结转";
                }
                if (projectInfo.getCurrentYearRelation().getProjType() == 3) {
                    natureName += "-新增";
                }
                return natureName;
            }
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_6.value.equals(columnProps)) {
            return usagePlanItem.getEstAmount() == null ? null : usagePlanItem.getEstAmount().stripTrailingZeros().toPlainString();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_7.value.equals(columnProps)) {
            return usagePlanItem.getLibAmount() == null ? null : usagePlanItem.getLibAmount().stripTrailingZeros().toPlainString();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_8.value.equals(columnProps)) {
            return usagePlanItem.getCheckAmount() == null ? null : usagePlanItem.getCheckAmount().stripTrailingZeros().toPlainString();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_9.value.equals(columnProps)) {
            return usagePlanItem.getContractAmount() == null ? null : usagePlanItem.getContractAmount().stripTrailingZeros().toPlainString();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_10.value.equals(columnProps)) {
            return usagePlanItem.getPrevAmount() == null ? null : usagePlanItem.getPrevAmount().stripTrailingZeros().toPlainString();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_11.value.equals(columnProps)) {
            return usagePlanItem.getPrevAdjust() == null ? null : usagePlanItem.getPrevAdjust().stripTrailingZeros().toPlainString();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_12.value.equals(columnProps)) {
            return usagePlanItem.getPrevPayed() == null ? null : usagePlanItem.getPrevPayed().stripTrailingZeros().toPlainString();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_13.value.equals(columnProps)) {
            return usagePlanItem.getCurrAmount() == null ? null : usagePlanItem.getCurrAmount().stripTrailingZeros().toPlainString();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_14.value.equals(columnProps)) {
            return usagePlanItem.getCurrEarly() == null ? null : usagePlanItem.getCurrEarly().stripTrailingZeros().toPlainString();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_15.value.equals(columnProps)) {
            return usagePlanItem.getCurrFormal() == null ? null : usagePlanItem.getCurrFormal().stripTrailingZeros().toPlainString();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_16.value.equals(columnProps)) {
            return usagePlanItem.getCurrAdjust() == null ? null : usagePlanItem.getCurrAdjust().stripTrailingZeros().toPlainString();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_17.value.equals(columnProps)) {
            return usagePlanItem.getCurrPayed() == null ? null : usagePlanItem.getCurrPayed().stripTrailingZeros().toPlainString();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_18.value.equals(columnProps)) {
            return usagePlanItem.getBeginDate();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_19.value.equals(columnProps)) {
            return usagePlanItem.getEndDate();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_20.value.equals(columnProps)) {
            return usagePlanItem.getRemark1();
        } else if (CommonConstant.UsagePlanColumnType.COLUMN_21.value.equals(columnProps)) {
            return usagePlanItem.getRemark2();
        }
        return null;
    }

    private UsagePlanItem computeTotal(List<UsagePlanItem> usagePlanItems) {
        UsagePlanItem usagePlanItem = new UsagePlanItem();
        usagePlanItem.setProjName("合计");
        usagePlanItem.setItemType(0);
        usagePlanItem.setEstAmount(usagePlanItems.stream().map(item -> item.getEstAmount() == null ? BigDecimal.ZERO : item.getEstAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));
        usagePlanItem.setLibAmount(usagePlanItems.stream().map(item -> item.getLibAmount() == null ? BigDecimal.ZERO : item.getLibAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));
        usagePlanItem.setCheckAmount(usagePlanItems.stream().map(item -> item.getCheckAmount() == null ? BigDecimal.ZERO : item.getCheckAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));
        usagePlanItem.setContractAmount(usagePlanItems.stream().map(item -> item.getContractAmount() == null ? BigDecimal.ZERO : item.getContractAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));
        usagePlanItem.setPrevAmount(usagePlanItems.stream().map(item -> item.getPrevAmount() == null ? BigDecimal.ZERO : item.getPrevAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));
        usagePlanItem.setPrevAdjust(usagePlanItems.stream().map(item -> item.getPrevAdjust() == null ? BigDecimal.ZERO : item.getPrevAdjust()).reduce(BigDecimal.ZERO, BigDecimal::add));
        usagePlanItem.setPrevPayed(usagePlanItems.stream().map(item -> item.getPrevPayed() == null ? BigDecimal.ZERO : item.getPrevPayed()).reduce(BigDecimal.ZERO, BigDecimal::add));
        usagePlanItem.setCurrAmount(usagePlanItems.stream().map(item -> item.getCurrAmount() == null ? BigDecimal.ZERO : item.getCurrAmount()).reduce(BigDecimal.ZERO, BigDecimal::add));
        usagePlanItem.setCurrEarly(usagePlanItems.stream().map(item -> item.getCurrEarly() == null ? BigDecimal.ZERO : item.getCurrEarly()).reduce(BigDecimal.ZERO, BigDecimal::add));
        usagePlanItem.setCurrFormal(usagePlanItems.stream().map(item -> item.getCurrFormal() == null ? BigDecimal.ZERO : item.getCurrFormal()).reduce(BigDecimal.ZERO, BigDecimal::add));
        usagePlanItem.setCurrAdjust(usagePlanItems.stream().map(item -> item.getCurrAdjust() == null ? BigDecimal.ZERO : item.getCurrAdjust()).reduce(BigDecimal.ZERO, BigDecimal::add));
        usagePlanItem.setCurrPayed(usagePlanItems.stream().map(item -> item.getCurrPayed() == null ? BigDecimal.ZERO : item.getCurrPayed()).reduce(BigDecimal.ZERO, BigDecimal::add));
        return usagePlanItem;
    }

    private CellStyle getCellStyle(Workbook workbook,
                                   String fontName,
                                   Integer fontSize,
                                   boolean isBold,
                                   HorizontalAlignment horizontalAlignment,
                                   VerticalAlignment verticalAlignment,
                                   boolean isBorder
    ) {
        Font font = workbook.createFont();
        if (StringUtils.isNotBlank(fontName)) {
            font.setFontName(fontName);
        }
        if (fontSize != null) {
            int fontSize1 = fontSize;
            font.setFontHeightInPoints((short) fontSize1);
        }
        if (isBold) {
            font.setBold(true);
        }
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font);
        //开启换行
        cellStyle.setWrapText(true);
        if (horizontalAlignment != null) {
            cellStyle.setAlignment(horizontalAlignment);
        }
        if (verticalAlignment != null) {
            cellStyle.setVerticalAlignment(verticalAlignment);
        }
        // 设置四边边框
        if (isBorder) {
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);
        }

        return cellStyle;
    }
}
