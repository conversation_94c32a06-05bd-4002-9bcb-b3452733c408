package com.ruoyi.web.controller.project;

import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.CommonConstant;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.project.ProjectPlan;
import com.ruoyi.system.service.project.ProjectPlanService;
import io.swagger.annotations.ApiOperation;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/5 15:18
 */
@RestController
@RequestMapping("/project/plan")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ProjectPlanController {

    private final ProjectPlanService projectPlanService;

    @ApiOperation("获取资金计划表信息")
    @GetMapping("/{pid}/{year}")
    public R<ProjectPlan> getInfo(@NotNull(message = "项目id不能为空") @PathVariable("pid") String pid, @NotNull(message = "年不能为空") @PathVariable("year") Integer year) {
        return R.ok(projectPlanService.getPlanInfo(pid,  year));
    }

    @ApiOperation("新增或修改资金计划表数据")
    @PostMapping()
    @RepeatSubmit(interval = 1000, message = "请勿重复提交")
    public R<ProjectPlan> saveOrUpdatePlan(@Validated @RequestBody ProjectPlan entity) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setPlanState(CommonConstant.StateType.DRAFT.value);
        }
        projectPlanService.saveOrUpdate(entity);
        return R.ok(entity);
    }

    @ApiOperation("计划上报")
    @PutMapping("/reportPlan")
    public R<Void> reportPlan(@NotNull(message = "项目id不能为空") String pid, @NotNull(message = "年不能为空") Integer year) {
        return projectPlanService.reportPlan(pid, year) ? R.ok() : R.fail();
    }

    @ApiOperation("撤销上报")
    @PutMapping("/withdrawPlan")
    public R<Void> withdrawPlan(@NotNull(message = "项目id不能为空") String pid, @NotNull(message = "年不能为空") Integer year) {
        return projectPlanService.withdrawPlan(pid, year) ? R.ok() : R.fail();
    }

    @ApiOperation("修改年初计划金额")
    @PutMapping("/updateEarlyAmount")
    public R<Void> updateEarlyAmount(@NotNull(message = "项目id不能为空") String pid,
                                     @NotNull(message = "年不能为空") Integer year,
                                     @NotNull(message = "金额不能为空") BigDecimal earlyAmount) {
        return projectPlanService.updateEarlyAmount(pid, year, earlyAmount) ? R.ok() : R.fail();
    }
}
