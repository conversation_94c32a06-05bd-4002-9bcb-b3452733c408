package com.ruoyi.web.controller.project;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.service.impl.project.FundPlanBreakDownService;
import io.swagger.annotations.ApiOperation;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 资金计划分解表 控制层
 *
 * <AUTHOR>
 * @date 2025/7/1 13:54
 */
@RestController
@RequestMapping("/project/breakDown")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FundPlanBreakDownController {

    private final FundPlanBreakDownService projectImportantService;

    @ApiOperation("查询项目分解表列表")
    @GetMapping("/getList")
    public R<Map<String, List<Map<String, Object>>>> deptList(@NotNull(message = "年不能为空") Integer year) {
        return R.ok(projectImportantService.getFundPlanBreakDownList(year));
    }

}
