{"name": "proj-mgr", "version": "1.0.0", "description": "上海化工区专项发展资金管理平台", "author": "上海美华系统有限公司", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.11.0", "axios": "0.28.1", "bignumber.js": "^9.3.0", "clipboard": "2.0.11", "echarts": "5.5.1", "element-plus": "2.9.7", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-beautify": "1.14.11", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "lodash": "^4.17.21", "nprogress": "0.2.0", "pinia": "2.1.7", "sortablejs": "^1.15.6", "splitpanes": "3.1.5", "tree-lodash": "^0.4.0", "uuid": "^11.1.0", "vue": "3.4.31", "vue-cropper": "1.1.1", "vue-router": "4.4.0", "vue3-print-nb": "^0.1.4", "vuedraggable": "4.1.0", "vxe-pc-ui": "^4.6.33", "vxe-table": "4.13.32"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.17", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-vue": "5.0.5", "@vitejs/plugin-vue-jsx": "4.0.0", "@vue/cli-plugin-typescript": "^5.0.8", "less": "^4.3.0", "sass": "1.77.5", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "unocss": "^66.1.2", "unplugin-auto-import": "0.17.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-lazy-import": "^1.0.7", "vite-plugin-svg-icons": "2.0.1"}}