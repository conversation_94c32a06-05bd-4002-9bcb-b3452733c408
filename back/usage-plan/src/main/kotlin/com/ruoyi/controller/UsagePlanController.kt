package com.ruoyi.controller

import cn.hutool.core.date.DateUtil
import com.ruoyi.common.constant.Constants
import com.ruoyi.service.UsagePlanService
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.utils.DateUtils
import com.ruoyi.common.utils.StringUtils
import com.ruoyi.domain.UsagePlanItem
import com.ruoyi.domain.dsl.UsagePlanItemEntity
import com.ruoyi.domain.dsl.UsagePlanItemObj
import com.ruoyi.domain.dsl.UsagePlanObj
import com.ruoyi.domain.vo.ResortVo
import com.ruoyi.domain.vo.SaveItemVo
import com.ruoyi.service.UsagePlanItemService
import com.ruoyi.domain.vo.SaveVo
import com.ruoyi.domain.vo.SearchVo
import io.swagger.annotations.ApiOperation
import jakarta.validation.Valid
import org.ktorm.dsl.*
import org.ktorm.schema.Column
import org.ktorm.schema.year
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import java.math.BigDecimal
import java.util.*

@RestController
@RequestMapping("/table/usagePlan")
@Transactional(rollbackFor = [Exception::class])
open class UsagePlanController(
    private var usagePlanService: UsagePlanService,
    private var usagePlanItemService: UsagePlanItemService
) {

    @GetMapping("list")
    open fun list(): AjaxResult {
        return AjaxResult.success(usagePlanService.findList())
    }

    @GetMapping("listSnap/{year}")
    open fun listSnap(@PathVariable("year") year: Int): AjaxResult {
        return AjaxResult.success(usagePlanService.findSnapList(year))
    }

    @GetMapping("detail/{year}")
    open fun detail(@PathVariable("year") year: Int, @ModelAttribute search: SearchVo): AjaxResult {
        return AjaxResult.success(usagePlanService.findDetail(year, search))
    }

    @GetMapping("detailById/{id}")
    open fun detailById(@PathVariable("id") id: String): AjaxResult {
        return AjaxResult.success(usagePlanService.findDetailById(id))
    }

    /**
     * 保存
     */
    @ApiOperation("保存表格")
    @PostMapping("save")
    open fun save(@RequestBody @Valid data: SaveVo): AjaxResult {
        val plan = usagePlanService.getByYear(data.year!!)
        if (plan?.isClosed == Constants.ONE) {
            return AjaxResult.error("表格已关闭")
        }
        if (plan?.isSnap == Constants.ONE) {
            return AjaxResult.error("快照不允许编辑")
        }
        return AjaxResult.success(usagePlanService.save(data))
    }

    /**
     * 创建快照
     */
    @ApiOperation("创建快照")
    @PostMapping("createSnap")
    open fun createSnap(
        @RequestParam("year") year: Int,
        @RequestParam("title") title: String,
        @RequestBody dataList: List<UsagePlanItem>
    ): AjaxResult {
        val plan = usagePlanService.getByYear(year)
        if (plan?.isClosed == Constants.ONE) {
            return AjaxResult.error("表格已关闭")
        }
        if (plan?.isSnap == Constants.ONE) {
            return AjaxResult.error("快照不允许编辑")
        }
        return AjaxResult.success(usagePlanService.createSnap(plan!!, title, dataList))
    }

    @ApiOperation("保存表格数据")
    @PostMapping("saveItem")
    open fun saveItem(@RequestBody @Valid data: SaveItemVo): AjaxResult {
        val plan = usagePlanService.getById(data.id!!)
        if (plan?.isClosed == Constants.ONE) {
            return AjaxResult.error("表格已关闭")
        }
        if (plan?.isSnap == Constants.ONE) {
            return AjaxResult.error("快照不允许编辑")
        }
        return AjaxResult.success(usagePlanService.saveItem(data))
    }

    @ApiOperation("更新表格标题")
    @PutMapping("updateTableTitle")
    open fun updateTableTitle(@RequestParam("year") year: Int, @RequestParam("title") title: String): AjaxResult {
        val plan = usagePlanService.getByYear(year)
        if (plan?.isClosed == Constants.ONE) {
            return AjaxResult.error("表格已关闭")
        }
        if (plan?.isSnap == Constants.ONE) {
            return AjaxResult.error("快照不允许编辑")
        }
        return AjaxResult.success(usagePlanService.usagePlan.database.update(UsagePlanObj) {
            set(it.title, title)
            where {
                it.year eq year
                it.isSnap eq Constants.ZERO
            }
        })
    }


    @ApiOperation("更新表格单位")
    @PutMapping("updateTableUnit")
    open fun updateTableUnit(@RequestParam("year") year: Int, @RequestParam("unit") unit: String): AjaxResult {
        val plan = usagePlanService.getByYear(year)
        if (plan?.isClosed == Constants.ONE) {
            return AjaxResult.error("表格已关闭")
        }
        if (plan?.isSnap == Constants.ONE) {
            return AjaxResult.error("快照不允许编辑")
        }
        return AjaxResult.success(usagePlanService.usagePlan.database.update(UsagePlanObj) {
            set(it.unit, unit)
            where {
                it.year eq year
                it.isSnap eq Constants.ZERO
            }
        })
    }

    @ApiOperation("更新表格关闭状态")
    @PutMapping("updateTableClose")
    open fun updateTableClose(@RequestParam("id") id: String): AjaxResult {
        val plan = usagePlanService.getById(id)
        if (plan?.isSnap == Constants.ONE) {
            return AjaxResult.error("快照不允许编辑")
        }
        return AjaxResult.success(usagePlanService.usagePlan.database.update(UsagePlanObj) {
            set(it.isClosed, if (plan?.isClosed == Constants.ONE) Constants.ZERO else Constants.ONE)
            where {
                it.id eq id
            }
        })
    }

    /**
     * 更新表格数据
     */
    @ApiOperation("更新表格数据")
    @PutMapping("updateTableValue")
    open fun updateTableValue(
        @RequestParam("id") id: String,
        @RequestParam("year") year: Int,
        @RequestParam("prop") prop: String,
        @RequestParam("value") value: String
    ): AjaxResult {
        val plan = usagePlanService.getByYear(year)
        if (plan?.isClosed != Constants.ZERO) {
            return AjaxResult.error("表格已关闭")
        }
        if (plan.isSnap == Constants.ONE) {
            return AjaxResult.error("快照不允许编辑")
        }

        if (listOf("estAmount", "libAmount", "checkAmount", "contractAmount", "prevAmount", "prevAdjust", "prevPayed",
            "currAmount", "currEarly", "currFormal", "currAdjust", "currPayed").contains(prop)) {

            usagePlanService.usagePlanItem.database.update(UsagePlanItemObj) {
                set(UsagePlanItemObj.getColumnByFieldName(prop) as Column<Any>, if (StringUtils.isEmpty(value)) null else BigDecimal(value))
                where {  it.id eq id }
            }
        } else {
            usagePlanService.usagePlanItem.database.update(UsagePlanItemObj) {
                set(UsagePlanItemObj.getColumnByFieldName(prop) as Column<Any>, value)
                where {  it.id eq id }
            }
        }

        return AjaxResult.success()
    }

    /**
     * 保存快照
     */
//    @PostMapping("saveSnap")
//    open fun saveSnap(): AjaxResult {
//
//    }

    @ApiOperation("删除表格")
    @DeleteMapping("{year}")
    open fun delete(@PathVariable("year") year: Int): AjaxResult {
        return AjaxResult.success(usagePlanService.deleteUsagePlan(year))
    }

    @ApiOperation("删除表格数据")
    @DeleteMapping("{year}/item")
    open fun deleteItem(@PathVariable("year") year: Int, @RequestBody ids: List<String>): AjaxResult {
        val plan = usagePlanService.getByYear(year)
        if (plan?.isClosed == Constants.ONE) {
            return AjaxResult.error("表格已关闭")
        }
        if (plan?.isSnap == Constants.ONE) {
            return AjaxResult.error("快照不允许编辑")
        }
        usagePlanService.usagePlanItem.database.delete(UsagePlanItemObj) {
            it.id inList ids and
                    (it.fundId eq plan!!.id)
        }
        return AjaxResult.success()
    }

    @ApiOperation("表格拖拽重排")
    @PutMapping("{year}/resort")
    open fun resort(@PathVariable("year") year: Int, @RequestBody dataList: List<ResortVo>): AjaxResult {
        val plan = usagePlanService.getByYear(year)
        if (plan?.isClosed == Constants.ONE) {
            return AjaxResult.error("表格已关闭")
        }
        if (plan?.isSnap == Constants.ONE) {
            return AjaxResult.error("快照不允许编辑")
        }
        usagePlanService.usagePlanItem.database.batchUpdate(UsagePlanItemObj) {
            dataList.forEach {t ->
                item {
                    set(it.parentId, t.parentId)
                    set(it.seqNo, t.seqNo)
                    where {
                        it.id eq t.id!!
                    }
                }
            }
        }

        return AjaxResult.success()
    }
}