package com.ruoyi.service

import com.ruoyi.domain.dsl.UsagePlanObj
import com.ruoyi.common.constant.Constants
import com.ruoyi.common.utils.SecurityUtils
import com.ruoyi.common.utils.StringUtils
import com.ruoyi.common.utils.uuid.IdUtils
import com.ruoyi.domain.UsagePlan
import com.ruoyi.domain.UsagePlanItem
import com.ruoyi.domain.dsl.UsagePlanEntity
import com.ruoyi.domain.dsl.UsagePlanItemEntity
import com.ruoyi.domain.dsl.UsagePlanItemObj
import com.ruoyi.domain.dto.UsagePlanDetailDto
import com.ruoyi.domain.dto.UsagePlanListDto
import com.ruoyi.domain.vo.SaveItemVo
import com.ruoyi.domain.vo.SaveVo
import com.ruoyi.domain.vo.SearchVo
import com.ruoyi.system.service.impl.project.ProjectInfoServiceImpl
import org.ktorm.database.Database
import org.ktorm.dsl.*
import org.ktorm.entity.*
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.LocalDateTime

@Service
@Transactional(rollbackFor = [Throwable::class])
open class UsagePlanService(
    private var database: Database,
    private var usagePlanItemService: UsagePlanItemService,
    private var projectInfoServiceImpl: ProjectInfoServiceImpl
) {
    val Database.usagePlan get() = this.sequenceOf(UsagePlanObj)
    val Database.usagePlanItem get() = this.sequenceOf(UsagePlanItemObj)

    open val usagePlan get() = database.usagePlan

    open val usagePlanItem get() = database.usagePlanItem

    open fun getById(id: String): UsagePlanEntity? {
        return usagePlan.find {
            it.id eq id and
                (it.delFlag eq Constants.ZERO)
        }
    }

    open fun getByYear(year: Int): UsagePlanEntity? {
        return usagePlan.find { it.year eq year  and
                (it.delFlag eq Constants.ZERO) and
                (it.isSnap eq Constants.ZERO)
        }
    }


    open fun findList(): List<UsagePlanListDto> {
        return database.usagePlan
            .filter {
                it.isSnap eq Constants.ZERO and
                        (it.delFlag eq Constants.ZERO)

            }
            .sortedByDescending {
                it.year
            }
            .map { plan ->
                // 获取项目数量
                val projectCount = this.database.usagePlanItem
                    .filter { (UsagePlanItemObj.fundId eq plan.id) and
                            (UsagePlanItemObj.itemType eq 1) }
                    .count()
                // 获取快照数量
                val snapCount = this.database.usagePlan
                    .filter { (it.year eq plan.year) and
                            (it.delFlag eq Constants.ZERO) and
                            (it.isSnap eq Constants.ONE)}
                    .count()

                UsagePlanListDto(
                    id = plan.id,
                    year = plan.year,
                    title = plan.title,
                    projectCount = projectCount,
                    snapCount = snapCount,
                    updateTime = plan.updateTime,
                    closed = plan.isClosed,
                )
            }
    }

    open fun findSnapList(year: Int): List<UsagePlan> {
        return database.usagePlan
            .filter {
                it.year eq year and
                (it.isSnap eq Constants.ONE) and
                (it.delFlag eq Constants.ZERO)
            }
            .sortedBy {
                it.createTime
            }
            .map {
                UsagePlan(
                    id = it.id,
                    year = it.year,
                    title = it.title,
                    snapName = it.snapName,
                    unit = it.unit,
                    createTime = it.createTime,
                )
            }
    }

    open fun deleteUsagePlan(year: Int): Int? {
        return database.usagePlan.find { it.year eq year }?.apply {
            delFlag = Constants.ONE
        }?.flushChanges()
    }

    open fun save(data: SaveVo): Boolean {
        val old = database.usagePlan.find {
            (it.year eq data.year!!) and
                    (it.isSnap eq Constants.ZERO) and
                    (it.delFlag eq Constants.ZERO)
        }
        var planId = old?.id
        if (old != null) {
            old.title = data.title.toString()
            old.unit = data.unit.toString()
            old.tableColumns = data.tableColumns.toString()
            old.updateBy = SecurityUtils.getUserId().toString()
            old.updateTime = LocalDateTime.now()
            database.usagePlan.update(old)
        } else {
            planId = IdUtils.fastSimpleUUID()
            database.usagePlan.add(UsagePlanEntity {
                id = planId
                year = data.year!!
                title = data.title.toString()
                unit = data.unit.toString()
                tableColumns = data.tableColumns.toString()
                createBy = SecurityUtils.getUserId().toString()
                createTime = LocalDateTime.now()
            })
        }

//        usagePlanItemService.save(planId!!, data.dataList)


        return true
    }

    open fun createSnap(entity: UsagePlanEntity, snapNameStr: String, dataList: List<UsagePlanItem>): Boolean {

        val newEntity = UsagePlanEntity {
            id = IdUtils.fastSimpleUUID()
            year = entity.year
            title = entity.title
            unit = entity.unit
            tableColumns = entity.tableColumns
            snapName = snapNameStr
            isSnap = Constants.ONE
            isClosed = Constants.ONE
            createBy = SecurityUtils.getUsername()
            createTime = LocalDateTime.now()
        }
        database.usagePlan.add(newEntity)

        val map = HashMap<String, String>()
        dataList.forEach{ map[it.id!!] = IdUtils.fastSimpleUUID() }

        dataList.forEach {
            val newItem = usagePlanItemService.copyToEntity(it)

            newItem.id = map.get(newItem.id)!!
            newItem.parentId = if (newItem.parentId == "0") "0" else map.get(newItem.parentId)
            newItem.fundId = newEntity.id
            newItem.createBy = SecurityUtils.getUsername()
            newItem.createTime = LocalDate.now()

            database.usagePlanItem.add(newItem)
        }


        return true
    }

    open fun saveItem(data: SaveItemVo): Boolean {
        usagePlanItemService.saveItem(data.id!!, data.dataList)
        return true
    }

    open fun findDetail(year1: Int, search: SearchVo): UsagePlanDetailDto? {
        val projectInfoMap = projectInfoServiceImpl.getProjectDetailForUsagePlan(year1).associateBy { it.id }

        val plan = database.usagePlan.find { it.year eq year1 }
        if (plan != null) {
            return UsagePlanDetailDto(
                id = plan.id,
                year = plan.year,
                title = plan.title,
                unit = plan.unit,
                tableColumns = plan.tableColumns,
                dataList = usagePlanItemService.findList(plan.id, search).map { planItem ->
                    if (StringUtils.isNotEmpty(planItem.projId)) {
                        planItem.projectInfo = projectInfoMap.get(planItem.projId)
                    }
                    planItem
                }
            )
        }
        val entity = UsagePlanEntity {
            id = IdUtils.fastSimpleUUID()
            year = year1
            title = "${year}年度化工区专项发展资金用款计划表"
            unit = "单位：万元"
            tableColumns = "[]"
            createBy = SecurityUtils.getUsername()
            createTime = LocalDateTime.now()
        }
        database.usagePlan.add(entity)
        return UsagePlanDetailDto(
            id = entity.id,
            year = entity.year,
            title = entity.title,
            unit = entity.unit,
            tableColumns = "",
            dataList = listOf()
        )
    }

    open fun findDetailById(id: String): UsagePlanDetailDto? {
        val plan = getById(id)
        if (plan != null) {
            return UsagePlanDetailDto(
                id = plan.id,
                year = plan.year,
                title = plan.title,
                unit = plan.unit,
                tableColumns = plan.tableColumns,
                dataList = usagePlanItemService.findList(plan.id)
            )
        }
        return null
    }

}