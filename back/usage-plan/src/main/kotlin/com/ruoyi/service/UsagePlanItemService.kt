package com.ruoyi.service

import com.ruoyi.common.utils.SecurityUtils
import com.ruoyi.common.utils.StringUtils
import com.ruoyi.domain.UsagePlanItem
import com.ruoyi.domain.dsl.UsagePlanItemEntity
import com.ruoyi.domain.dsl.UsagePlanItemObj
import com.ruoyi.domain.vo.SearchVo
import org.ktorm.database.Database
import org.springframework.stereotype.Service
import org.ktorm.dsl.*
import org.ktorm.entity.*
import org.ktorm.schema.ColumnDeclaring
import java.time.LocalDate
import java.time.LocalDateTime

@Service
open class UsagePlanItemService(
    private var database: Database
) {

    val Database.usagePlanItem get() = this.sequenceOf(UsagePlanItemObj)

    open fun save(usagePlanId: String, list: List<UsagePlanItem>?) {
        // 先删除相关的数据
//        database.usagePlanItem.removeIf { it.fundId eq usagePlanId }
        list?.forEach { item ->
            item.fundId = usagePlanId
            item.createBy = SecurityUtils.getUserId().toString()
            item.createTime = LocalDate.now()
            database.usagePlanItem.add(copyToEntity(item))

        }
    }

    open fun saveItem(usagePlanId: String, list: List<UsagePlanItem>?) {
        list?.forEach { item ->
            val old = database.usagePlanItem.find { it.id eq item.id!! }
            if (old != null) {
                database.update(UsagePlanItemObj) {
                    set(it.assessOrgid, item.assessOrgid)
                    set(it.assessOrgname, item.assessOrgname)
                    set(it.isImp, item.isImp)
                    set(it.impName, item.impName)
                    set(it.projName, item.projName)
                    set(it.sbOrgid, item.sbOrgid)
                    set(it.sbOrgname, item.sbOrgname)
                    set(it.seqNo, item.seqNo)
                    set(it.updateBy, SecurityUtils.getUserId().toString())
                    set(it.updateTime, LocalDate.now())
                    where { it.id eq item.id!! }
                }
            } else {
                item.fundId = usagePlanId
                item.createBy = SecurityUtils.getUserId().toString()
                item.createTime = LocalDate.now()
                database.usagePlanItem.add(copyToEntity(item))
            }
        }
    }

    open fun findList(fundId1: String, search: SearchVo): List<UsagePlanItem> {
        return database.usagePlanItem.filter {
            val conditions = ArrayList<ColumnDeclaring<Boolean>>()
            conditions += UsagePlanItemObj.fundId eq fundId1
            if (search.sbOrgid != null) {
                conditions += UsagePlanItemObj.sbOrgid eq  search.sbOrgid!!
            }
            if (search.basketId != null) {
                conditions += UsagePlanItemObj.basketId eq  search.basketId!!
            }
            if (search.natureName != null) {
                conditions += UsagePlanItemObj.natureName eq  search.natureName!!
            }
            if (search.assessOrgid != null) {
                conditions += UsagePlanItemObj.assessOrgid eq  search.assessOrgid!!
            }
            conditions.reduce { a, b -> a and b }
        }
            .sortedBy { it.seqNo }
            .map { item ->
                UsagePlanItem() .apply {
                    id = item.id
                    fundId = item.fundId
                    itemType = item.itemType
                    sumMode = item.sumMode
                    parentId = item.parentId
                    seqNo = item.seqNo
                    projId = item.projId
                    projName = item.projName
                    sbOrgid = item.sbOrgid
                    sbOrgname = item.sbOrgname
                    assessOrgid = item.assessOrgid
                    assessOrgname = item.assessOrgname
                    isImp = item.isImp
                    impName = item.impName
                    basketId = item.basketId
                    basketName = item.basketName
                    natureName = item.natureName
                    estAmount = item.estAmount
                    libAmount = item.libAmount
                    checkAmount = item.checkAmount
                    contractAmount = item.contractAmount
                    prevAmount = item.prevAmount
                    prevAdjust = item.prevAdjust
                    prevPayed = item.prevPayed
                    currAmount = item.currAmount
                    currEarly = item.currEarly
                    currFormal = item.currFormal
                    currAdjust = item.currAdjust
                    currPayed = item.currPayed
                    beginDate = item.beginDate
                    endDate = item.endDate
                    remark1 = item.remark1
                    remark2 = item.remark2
                }

        }
    }

    open fun findList(fundId1: String): List<UsagePlanItem> {
        return database.usagePlanItem.filter {
            val conditions = ArrayList<ColumnDeclaring<Boolean>>()
            conditions += UsagePlanItemObj.fundId eq fundId1
            conditions.reduce { a, b -> a and b }
        }
            .sortedBy { it.seqNo }
            .map { item ->
                UsagePlanItem() .apply {
                    id = item.id
                    fundId = item.fundId
                    itemType = item.itemType
                    sumMode = item.sumMode
                    parentId = item.parentId
                    seqNo = item.seqNo
                    projId = item.projId
                    projName = item.projName
                    sbOrgid = item.sbOrgid
                    sbOrgname = item.sbOrgname
                    assessOrgid = item.assessOrgid
                    assessOrgname = item.assessOrgname
                    isImp = item.isImp
                    impName = item.impName
                    basketId = item.basketId
                    basketName = item.basketName
                    natureName = item.natureName
                    estAmount = item.estAmount
                    libAmount = item.libAmount
                    checkAmount = item.checkAmount
                    contractAmount = item.contractAmount
                    prevAmount = item.prevAmount
                    prevAdjust = item.prevAdjust
                    prevPayed = item.prevPayed
                    currAmount = item.currAmount
                    currEarly = item.currEarly
                    currFormal = item.currFormal
                    currAdjust = item.currAdjust
                    currPayed = item.currPayed
                    beginDate = item.beginDate
                    endDate = item.endDate
                    remark1 = item.remark1
                    remark2 = item.remark2
                }

            }
    }

    fun copyToEntity(t: UsagePlanItem): UsagePlanItemEntity {
        return UsagePlanItemEntity {
            id = t.id.toString()
            fundId = t.fundId.toString()
            itemType = t.itemType!!
            sumMode = t.sumMode
            parentId = t.parentId
            seqNo = t.seqNo
            projId = t.projId
            projName = t.projName.toString()
            sbOrgid = t.sbOrgid
            sbOrgname = t.sbOrgname
            assessOrgid = t.assessOrgid
            assessOrgname = t.assessOrgname
            isImp = t.isImp
            impName = t.impName
            basketId = t.basketId
            basketName = t.basketName
            natureName = t.natureName
            estAmount = t.estAmount
            libAmount = t.libAmount
            checkAmount = t.checkAmount
            contractAmount = t.contractAmount
            prevAmount = t.prevAmount
            prevAdjust = t.prevAdjust
            prevPayed = t.prevPayed
            currAmount = t.currAmount
            currEarly = t.currEarly
            currFormal = t.currFormal
            currAdjust = t.currAdjust
            currPayed = t.currPayed
            beginDate = t.beginDate
            endDate = t.endDate
            remark1 = t.remark1
            remark2 = t.remark2
            createBy = t.createBy
            createTime = t.createTime
            updateBy = t.updateBy
            updateTime = t.updateTime

        }
    }
}