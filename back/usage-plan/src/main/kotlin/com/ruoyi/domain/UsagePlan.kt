package com.ruoyi.domain

import java.time.LocalDate
import java.time.LocalDateTime

data class UsagePlan(
    /**
     * 主键
     */
    var id: String? = null,

    /**
     * 年度
     */
    var year: Int? = null,

    /**
     * 标题
     */
    var title: String? = null,

    /**
     * 表格单位
     */
    var unit: String? = null,

    /**
     * 列定义，使用json存储
     */
    var tableColumns: String? = null,

    /**
     * 是否为快照（0否，1是）
     */
    var isSnap: String? = null,

    /**
     * 快照名称
     */
    var snapName: String? = null,

    /**
     * 是否关闭（0开放、1关闭），如关闭状态，则不允许再编辑
     */
    var isClosed: String? = null,

    /**
     * 删除标记（0否，1是）
     */
    var delFlag: String? = null,

    /**
     * 创建人，存储用户名
     */
    var createBy: String? = null,

    /**
     * 创建时间
     */
    var createTime: LocalDateTime? = null,

    /**
     * 更新人，存储用户名
     */
    var updateBy: String? = null,

    /**
     * 更新时间
     */
    var updateTime: LocalDateTime? = null
)
