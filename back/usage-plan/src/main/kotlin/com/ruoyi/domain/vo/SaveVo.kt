package com.ruoyi.domain.vo

import com.ruoyi.domain.UsagePlanItem
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.NotNull

data class SaveVo(

    /**
     * 年度
     */
    @field:NotNull(message = "年度不允许为空")
    var year: Int? = null,

    /**
     * 标题
     */
    @field:NotEmpty(message = "标题不允许为空")
    var title: String? = null,

    /**
     * 表格单位
     */
    @field:NotEmpty(message = "表格单位不允许为空")
    var unit: String? = null,

    /**
     * 列定义，使用json存储
     */
    @field:NotEmpty(message = "列定义不允许为空")
    var tableColumns: String? = null,

    /**
     * 快照名称
     */
    var snapName: String? = null,

    /**
     * 表格数据
     */
//    @field:NotEmpty(message = "表格数据不允许为空")
//    var dataList: List<UsagePlanItem>? = null,
)
