package com.ruoyi.domain.dsl

import org.ktorm.dsl.isNotNull
import org.ktorm.schema.*
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible

object UsagePlanItemObj: Table<UsagePlanItemEntity>("usage_plan_item") {
    /**
     * 主键
     */
    val id = varchar("id").primaryKey().bindTo { it.id }
    /**
     * 用款计划表ID
     */
    val fundId = varchar("fund_id").bindTo { it.fundId }
    /**
     * 数据项类型，0类型 1项目，2组
     */
    val itemType = int("item_type").bindTo { it.itemType }
    /**
     * 组汇总方式，1自动累加，2自定义
     */
    val sumMode = int("sum_mode").bindTo { it.sumMode }
    /**
     * 父项ID
     */
    val parentId = varchar("parent_id").bindTo { it.parentId }
    /**
     * 排序号，默认1
     */
    val seqNo = int("seq_no").bindTo { it.seqNo }
    /**
     * 项目ID
     */
    val projId = varchar("proj_id").bindTo { it.projId }
    /**
     * 项目ID
     */
    val projName = varchar("proj_name").bindTo { it.projName }
    /**
     * 上报单位ID
     */
    val sbOrgid = long("sb_orgid").bindTo { it.sbOrgid }
    /**
     * 上报单位名称
     */
    val sbOrgname = varchar("sb_orgname").bindTo { it.sbOrgname }
    /**
     * 考核主体ID
     */
    val assessOrgid = long("assess_orgid").bindTo { it.assessOrgid }
    /**
     * 考核主体名称
     */
    val assessOrgname = varchar("assess_orgname").bindTo { it.assessOrgname }
    /**
     * 是否重点项目（Y是，N否）
     */
    val isImp = varchar("is_imp").bindTo { it.isImp }
    /**
     * 重点项目类型
     */
    val impName = varchar("imp_name").bindTo { it.impName }
    /**
     * 项目篮子ID
     */
    val basketId = varchar("basket_id").bindTo { it.basketId }
    /**
     * 项目篮子名称
     */
    val basketName = varchar("basket_name").bindTo { it.basketName }
    /**
     * 项目性质（阶段性-新增，阶段性-结转，经常性）
     */
    val natureName = varchar("nature_name").bindTo { it.natureName }
    /**
     * 项目估算
     */
    val estAmount = decimal("est_amount").bindTo { it.estAmount }
    /**
     * 入库金额
     */
    val libAmount = decimal("lib_amount").bindTo { it.libAmount }
    /**
     * 核定金额
     */
    val checkAmount = decimal("check_amount").bindTo { it.checkAmount }
    /**
     * 合同金额
     */
    val contractAmount = decimal("contract_amount").bindTo { it.contractAmount }
    /**
     * 至上年底累计执行
     */
    val prevAmount = decimal("prev_amount").bindTo { it.prevAmount }
    /**
     * 上年调整计划
     */
    val prevAdjust = decimal("prev_adjust").bindTo { it.prevAdjust }
    /**
     * 上年实际执行
     */
    val prevPayed = decimal("prev_payed").bindTo { it.prevPayed }
    /**
     * 当年资金需求
     */
    val currAmount = decimal("curr_amount").bindTo { it.currAmount }
    /**
     * 当年初步使用计划
     */
    val currEarly = decimal("curr_early").bindTo { it.currEarly }
    /**
     * 当年正式计划
     */
    val currFormal = decimal("curr_formal").bindTo { it.currFormal }
    /**
     * 当年调整计划
     */
    val currAdjust = decimal("curr_adjust").bindTo { it.currAdjust }
    /**
     * 当年已执行
     */
    val currPayed = decimal("curr_payed").bindTo { it.currPayed }
    /**
     * 开工(采购)时间
     */
    val beginDate = varchar("begin_date").bindTo { it.beginDate }
    /**
     * 竣工(完成)时间
     */
    val endDate = varchar("end_date").bindTo { it.endDate }
    /**
     * 备注1
     */
    val remark1 = varchar("remark1").bindTo { it.remark1 }
    /**
     * 备注2
     */
    val remark2 = varchar("remark2").bindTo { it.remark2 }
    /**
     * 创建人，存储用户名
     */
    val createBy = varchar("create_by").bindTo { it.createBy }.isNotNull()
    /**
     * 创建时间
     */
    val createTime = date("create_time").bindTo { it.createTime }.isNotNull()
    /**
     * 更新人，存储用户名
     */
    val updateBy = varchar("update_by").bindTo { it.updateBy }
    /**
     * 更新时间
     */
    val updateTime = date("update_time").bindTo { it.updateTime }

    /**
     * 根据字段名称获取字段
     */
    fun getColumnByFieldName(fieldName: String): Column<*>? {
        return UsagePlanItemObj::class.memberProperties
            .firstOrNull { it.name == fieldName }
            ?.apply { isAccessible = true }
            ?.getter
            ?.call(UsagePlanItemObj) as? Column<*>
    }
}