package com.ruoyi.domain.dsl

import org.ktorm.entity.Entity
import org.ktorm.schema.long
import java.math.BigDecimal
import java.time.LocalDate

interface UsagePlanItemEntity: Entity<UsagePlanItemEntity> {

    companion object : Entity.Factory<UsagePlanItemEntity>()

    /**
     * 主键
     */
    var id: String
    /**
     * 用款计划表ID
     */
    var fundId: String
    /**
     * 数据项类型，0类型 1项目，2组
     */
    var itemType: Int
    /**
     * 组汇总方式，1自动累加，2自定义
     */
    var sumMode: Int?
    /**
     * 父项ID
     */
    var parentId: String?
    /**
     * 排序号，默认1
     */
    var seqNo: Int?
    /**
     * 项目ID
     */
    var projId: String?
    /**
     * 项目ID
     */
    var projName: String
    /**
     * 上报单位ID
     */
    var sbOrgid: Long?
    /**
     * 上报单位名称
     */
    var sbOrgname: String?
    /**
     * 考核主体ID
     */
    var assessOrgid: Long?
    /**
     * 考核主体名称
     */
    var assessOrgname: String?
    /**
     * 是否重点项目（Y是，N否）
     */
    var isImp: String?
    /**
     * 重点项目类型
     */
    var impName: String?
    /**
     * 项目篮子ID
     */
    var basketId: String?
    /**
     * 项目篮子名称
     */
    var basketName: String?
    /**
     * 项目性质（阶段性-新增，阶段性-结转，经常性）
     */
    var natureName: String?
    /**
     * 项目估算
     */
    var estAmount: BigDecimal?
    /**
     * 入库金额
     */
    var libAmount: BigDecimal?
    /**
     * 核定金额
     */
    var checkAmount: BigDecimal?
    /**
     * 合同金额
     */
    var contractAmount: BigDecimal?
    /**
     * 至上年底累计执行
     */
    var prevAmount: BigDecimal?
    /**
     * 上年调整计划
     */
    var prevAdjust: BigDecimal?
    /**
     * 上年实际执行
     */
    var prevPayed: BigDecimal?
    /**
     * 当年资金需求
     */
    var currAmount: BigDecimal?
    /**
     * 当年初步使用计划
     */
    var currEarly: BigDecimal?
    /**
     * 当年正式计划
     */
    var currFormal: BigDecimal?
    /**
     * 当年调整计划
     */
    var currAdjust: BigDecimal?
    /**
     * 当年已执行
     */
    var currPayed: BigDecimal?
    /**
     * 开工(采购)时间
     */
    var beginDate: String?
    /**
     * 竣工(完成)时间
     */
    var endDate: String?
    /**
     * 备注1
     */
    var remark1: String?
    /**
     * 备注2
     */
    var remark2: String?
    /**
     * 创建人，存储用户名
     */
    var createBy: String?
    /**
     * 创建时间
     */
    var createTime: LocalDate?
    /**
     * 更新人，存储用户名
     */
    var updateBy: String?
    /**
     * 更新时间
     */
    var updateTime: LocalDate?
}