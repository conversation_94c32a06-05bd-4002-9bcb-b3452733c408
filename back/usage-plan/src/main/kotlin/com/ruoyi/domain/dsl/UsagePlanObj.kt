package com.ruoyi.domain.dsl

import org.ktorm.schema.*

object UsagePlanObj: Table<UsagePlanEntity>("usage_plan") {
    /**
     * 主键
     */
    val id = varchar("id").primaryKey().bindTo { it.id }
    /**
     * 年度
     */
    val year = int("year").bindTo { it.year }
    /**
     * 标题
     */
    val title = varchar("title").bindTo { it.title }
    /**
     * 表格单位
     */
    val unit = varchar("unit").bindTo { it.unit }
    /**
     * 列定义，使用json存储，定义列的显示和表头名称，[{"sort":1,"code":"xmmc","title":"项目名称","display":true},{"sort":2,"code":"xmlz","title":"项目篮子","display":true},{"sort":3,"code":"khzt","title":"考核主体","display":true}]
     */
    val tableColumns = varchar("columns").bindTo { it.tableColumns }
    /**
     * 是否为快照（0否，1是）
     */
    val isSnap = varchar("is_snap").bindTo { it.isSnap }
    /**
     * 快照名称
     */
    var snapName = varchar("snap_name").bindTo { it.snapName }

    /**
     * 是否关闭（0开放、1关闭），如关闭状态，则不允许再编辑
     */
    val isClosed = varchar("is_closed").bindTo { it.isClosed }
    /**
     * 删除标记（0否，1是）
     */
    val delFlag = varchar("del_flag").bindTo { it.delFlag }
    /**
     * 创建人，存储用户名
     */
    val createBy = varchar("create_by").bindTo { it.createBy }
    /**
     * 创建时间
     */
    val createTime = datetime("create_time").bindTo { it.createTime }
    /**
     * 更新人，存储用户名
     */
    val updateBy = varchar("update_by").bindTo { it.updateBy }
    /**
     * 更新时间
     */
    val updateTime = datetime("update_time").bindTo { it.updateTime }
}