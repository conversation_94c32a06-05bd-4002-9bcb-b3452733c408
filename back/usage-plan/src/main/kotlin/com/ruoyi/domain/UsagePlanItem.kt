package com.ruoyi.domain

import com.fasterxml.jackson.annotation.JsonProperty
import com.ruoyi.system.domain.vo.project.ProjectInfoVo
import java.math.BigDecimal
import java.time.LocalDate

data class UsagePlanItem(
    /**
     * 主键
     */
    var id: String? = null,
    /**
     * 用款计划表ID
     */
    var fundId: String? = null,
    /**
     * 数据项类型，0类型 1项目，2组
     */
    var itemType: Int? = null,
    /**
     * 组汇总方式，1自动累加，2自定义
     */
    var sumMode: Int? = null,
    /**
     * 父项ID
     */
    var parentId: String? = null,
    /**
     * 排序号，默认1
     */
    var seqNo: Int? = null,
    /**
     * 项目ID
     */
    var projId: String? = null,
    /**
     * 项目ID
     */
    var projName: String? = null,
    /**
     * 上报单位ID
     */
    var sbOrgid: Long? = null,
    /**
     * 上报单位名称
     */
    var sbOrgname: String? = null,
    /**
     * 考核主体ID
     */
    var assessOrgid: Long? = null,
    /**
     * 考核主体名称
     */
    var assessOrgname: String? = null,
    /**
     * 是否重点项目（Y是，N否）
     */
    @get:JsonProperty("isImp")
    var isImp: String? = null,
    /**
     * 重点项目类型
     */
    var impName: String? = null,
    /**
     * 项目篮子ID
     */
    var basketId: String? = null,
    /**
     * 项目篮子名称
     */
    var basketName: String? = null,
    /**
     * 项目性质（阶段性-新增，阶段性-结转，经常性）
     */
    var natureName: String? = null,
    /**
     * 项目估算
     */
    var estAmount: BigDecimal? = null,
    /**
     * 入库金额
     */
    var libAmount: BigDecimal? = null,
    /**
     * 核定金额
     */
    var checkAmount: BigDecimal? = null,
    /**
     * 合同金额
     */
    var contractAmount: BigDecimal? = null,
    /**
     * 至上年底累计执行
     */
    var prevAmount: BigDecimal? = null,
    /**
     * 上年调整计划
     */
    var prevAdjust: BigDecimal? = null,
    /**
     * 上年实际执行
     */
    var prevPayed: BigDecimal? = null,
    /**
     * 当年资金需求
     */
    var currAmount: BigDecimal? = null,
    /**
     * 当年初步使用计划
     */
    var currEarly: BigDecimal? = null,
    /**
     * 当年正式计划
     */
    var currFormal: BigDecimal? = null,
    /**
     * 当年调整计划
     */
    var currAdjust: BigDecimal? = null,
    /**
     * 当年已执行
     */
    var currPayed: BigDecimal? = null,
    /**
     * 开工(采购)时间
     */
    var beginDate: String? = null,
    /**
     * 竣工(完成)时间
     */
    var endDate: String? = null,
    /**
     * 备注1
     */
    var remark1: String? = null,
    /**
     * 备注2
     */
    var remark2: String? = null,
    /**
     * 创建人，存储用户名
     */
    var createBy: String? = null,
    /**
     * 创建时间
     */
    var createTime: LocalDate? = null,
    /**
     * 更新人，存储用户名
     */
    var updateBy: String? = null,
    /**
     * 更新时间
     */
    var updateTime: LocalDate? = null,

    /**
     * 项目信息
     */
    var projectInfo: ProjectInfoVo? =null
)
