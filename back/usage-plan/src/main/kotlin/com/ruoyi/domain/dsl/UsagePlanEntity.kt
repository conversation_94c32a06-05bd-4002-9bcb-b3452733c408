package com.ruoyi.domain.dsl

import org.ktorm.entity.Entity
import java.time.LocalDate
import java.time.LocalDateTime

interface UsagePlanEntity: Entity<UsagePlanEntity> {

    companion object : Entity.Factory<UsagePlanEntity>()

    /**
     * 主键
     */
    var id: String

    /**
     * 年度
     */
    var year: Int

    /**
     * 标题
     */
    var title: String

    /**
     * 表格单位
     */
    var unit: String

    /**
     * 列定义，使用json存储
     */
    var tableColumns: String

    /**
     * 是否为快照（0否，1是）
     */
    var isSnap: String

    /**
     * 快照名称
     */
    var snapName: String

    /**
     * 是否关闭（0开放、1关闭），如关闭状态，则不允许再编辑
     */
    var isClosed: String

    /**
     * 删除标记（0否，1是）
     */
    var delFlag: String

    /**
     * 创建人，存储用户名
     */
    var createBy: String

    /**
     * 创建时间
     */
    var createTime: LocalDateTime

    /**
     * 更新人，存储用户名
     */
    var updateBy: String?

    /**
     * 更新时间
     */
    var updateTime: LocalDateTime?

    fun getData(): Map<String, Any?> {
        return mapOf(
            "id" to id,
            "year" to year,
            "title" to title,
            "unit" to unit,
            "tableColumns" to tableColumns,
            "isSnap" to isSnap,
            "snapName" to snapName,
            "isClosed" to isClosed,
            "delFlag" to delFlag,
            "createBy" to createBy,
            "createTime" to createTime,
            "updateBy" to updateBy,
            "updateTime" to updateTime
        )
    }
}