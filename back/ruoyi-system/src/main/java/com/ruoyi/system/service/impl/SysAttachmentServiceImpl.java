package com.ruoyi.system.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.system.domain.SysAttachment;
import com.ruoyi.system.service.SysAttachmentService;
import com.ruoyi.system.mapper.SysAttachmentMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【sys_file(附件表)】的数据库操作Service实现
 * @date 2025-06-04 13:47:20
 */
@Service
public class SysAttachmentServiceImpl extends ServiceImpl<SysAttachmentMapper, SysAttachment> implements SysAttachmentService {


    @Override
    public List<SysAttachment> getListBySourceId(String sourceId){
        return getListBySourceId(sourceId, null);
    }

    @Override
    public List<SysAttachment> getListBySourceId(String sourceId, String primaryType){
        return this.lambdaQuery()
                .eq(SysAttachment::getSourceId, sourceId)
                .eq(StringUtils.isNotBlank(primaryType), SysAttachment::getPrimaryType, primaryType)
                .orderByDesc(SysAttachment::getPrimaryType)
                .list();
    }

    @Override
    public SysAttachment uploadFile(MultipartFile file, SysAttachment extraMessage) {
        String fileName = file.getOriginalFilename();
        assert fileName != null;
        String uploadPath = RuoYiConfig.getUploadPath();
        String filePath = "";
        try {
            // 上传并返回新文件名称
            filePath = FileUploadUtils.upload(uploadPath, file);
        } catch (IOException e) {
            throw new ServiceException("上传失败");
        }

        extraMessage.setFileName(fileName);
        extraMessage.setFilePath(filePath);
        extraMessage.setFileExt(FileUtil.getSuffix(fileName));
        extraMessage.setFileSize(Convert.toInt(file.getSize()));
        extraMessage.setCreatedBy(SecurityUtils.getUsername());
        extraMessage.setCreatedTime(new Date());
        this.lambdaQuery()
                .eq(SysAttachment::getSourceId, extraMessage.getSourceId())
                .list()
                .stream()
                .map(SysAttachment::getSeqNo)
                .max(Integer::compareTo)
                .ifPresentOrElse(integer -> extraMessage.setSeqNo(integer + 1), () -> extraMessage.setSeqNo(1));
        this.save(extraMessage);
        return extraMessage;
    }

}




