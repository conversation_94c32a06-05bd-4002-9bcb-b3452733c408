package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 附件表
 * @TableName sys_file
 */
@TableName(value ="sys_file")
@Data
public class SysAttachment {
    /**
     * 主键;主键，无意义，确保有序
     */
    @TableId(value = "id")
    private String id;

    /**
     * 源数据ID;源数据ID
     */
    @NotNull(message = "源数据ID不能为空")
    private String sourceId;

    /**
     * 源数据类型;源数据类型（如：项目、资金计划、拨款单、合同等）
     */
    @NotNull(message = "源数据类型不能为空")
    private String sourceType;

    /**
     * 附件大类;附件大类
     */
    private String primaryType;

    /**
     * 附件小类;附件小类
     */
    private String subType;

    /**
     * 名称;名称
     */
    private String fileName;

    /**
     * 存储路径;存储路径
     */
    private String filePath;

    /**
     * 类型（后缀名）;类型（后缀名，如pdf|.pdf）
     */
    private String fileExt;

    /**
     * 大小;大小，单位：字节（B），类型应用bigint
     */
    private Integer fileSize;

    /**
     * 排序号;排序号，默认1
     */
    private Integer seqNo;

    /**
     * 上传时间;上传时间，默认当前时间
     */
    private Date createdTime;

    /**
     * 上传人;上传人，存储用户名
     */
    private String createdBy;
}