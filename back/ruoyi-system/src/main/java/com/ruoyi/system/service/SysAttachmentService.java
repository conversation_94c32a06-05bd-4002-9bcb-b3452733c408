package com.ruoyi.system.service;

import com.ruoyi.system.domain.SysAttachment;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【sys_file(附件表)】的数据库操作Service
 * @date 2025-06-04 13:47:20
 */
public interface SysAttachmentService extends IService<SysAttachment> {

    /**
     * 根据业务id查询附件
     *
     * @param sourceId 业务id
     * @return 结果
     */
    List<SysAttachment> getListBySourceId(String sourceId);

    /**
     * 根据业务id查询附件
     *
     * @param sourceId 业务id
     * @param primaryType 业务附件类型
     * @return 结果
     */
    List<SysAttachment> getListBySourceId(String sourceId, String primaryType);

    /**
     * 上传文件
     *
     * @param file 文件
     * @return 文件信息
     */
    SysAttachment uploadFile(MultipartFile file,  SysAttachment extraMessage);

}
