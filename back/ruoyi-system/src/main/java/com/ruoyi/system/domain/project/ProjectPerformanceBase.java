package com.ruoyi.system.domain.project;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 项目绩效指标库对象
 *
 * <AUTHOR>
 * @date 2025-06-09 15:50:05
 */
@Data
@TableName("project_performance_base")
public class ProjectPerformanceBase {

    /**
     * 主键;主键，无意义，自增
     */
    @TableId(value = "id")
    private Integer id;

    /**
     * 一级指标;一级指标
     */
    private String indexLevel1;

    /**
     * 二级指标;二级指标
     */
    private String indexLevel2;

    /**
     * 三级指标;三级指标
     */
    private String indexLevel3;

    /**
     * 指标性质;指标性质
     */
    private String natureName;

    /**
     * 方向;方向
     */
    private String compareArrow;

    /**
     * 计量单位;计量单位
     */
    private String measureUnit;

    /**
     * 指标值说明;指标值说明
     */
    private String remark;

    /**
     * 删除标记;删除标记（0否，1是）
     */
    @TableLogic
    private String delFlag;

    /**
     * 排序号;排序号
     */
    private Integer indexSort;


}
