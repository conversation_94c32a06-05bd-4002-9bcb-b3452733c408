package com.ruoyi.system.service.fund;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.yulichang.base.MPJBaseService;
import com.ruoyi.system.domain.fund.FundContractInfo;
import com.ruoyi.system.domain.vo.fund.FundContractInfoVo;

public interface FundContractInfoService extends MPJBaseService<FundContractInfo> {

    /**
     * 获得合同信息
     *
     * @param id id
     * @return 结果
     */
    FundContractInfoVo getInfo(String id);
}
