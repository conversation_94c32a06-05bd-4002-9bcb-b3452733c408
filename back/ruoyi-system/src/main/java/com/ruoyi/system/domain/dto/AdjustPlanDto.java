package com.ruoyi.system.domain.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 调整计划dto
 *
 * <AUTHOR>
 * @date 2025/6/18 14:43
 */
@Data
public class AdjustPlanDto {

    /**
     * 项目ID
     */
    @NotNull(message = "项目id不能为空")
    private String projId;

    /**
     * 年度
     */
    @NotNull(message = "年度不能为空")
    private Integer year;

    /**
     * 调整金额
     */
    @NotNull(message = "调整金额不能为空")
    private BigDecimal applyAdjust;

    /**
     * 意见
     */
    private String applyReason;
}
