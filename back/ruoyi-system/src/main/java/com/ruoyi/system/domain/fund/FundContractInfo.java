package com.ruoyi.system.domain.fund;


import com.baomidou.mybatisplus.annotation.*;
import com.github.yulichang.annotation.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同信息实体
 */
@Data
@TableName("fund_contract_info")
public class FundContractInfo {

    /** 主键;主键 */
    @TableId
    private String id ;

    /** 项目ID;项目ID */
    private String projId ;

    /** 子项目ID;子项目ID */
    private String childId ;

    /** 合同编号;合同编号 */
    private String code ;

    /** 合同名称;合同名称 */
    private String name ;

    /** 甲方;甲方 */
    private String parta ;

    /** 乙方;乙方 */
    private String partb ;

    /** 合同金额;合同金额 */
    private BigDecimal amount ;

    /** 签订日期;签订日期 */
    private Date signDate ;

    /** 起始日期;起始日期 */
    private Date validatyBegin ;
    /** 截止日期;截止日期 */

    private Date validatyEnd ;

    /** 合同主要内容;合同主要内容 */
    private String content ;

    /** 排序号;排序号，默认1 */
    private Integer seqNo ;

    /** 2019年底前累计支付;2019年底前累计支付 */
    private BigDecimal amount2019 ;

    /** 创建人;创建人，存储用户名 */
    @TableField(fill = FieldFill.INSERT)
    private String createBy ;

    /** 创建时间;创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime ;

    /** 更新人;更新人，存储用户名 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy ;

    /** 更新时间;更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 删除标志（0代表存在 1代表删除） */
    @TableLogic
    private String delFlag;
}
