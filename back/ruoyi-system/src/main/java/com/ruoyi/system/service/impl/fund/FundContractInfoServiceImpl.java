package com.ruoyi.system.service.impl.fund;

import cn.hutool.core.bean.BeanUtil;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.SysAttachment;
import com.ruoyi.system.domain.fund.FundContractInfo;
import com.ruoyi.system.domain.project.ProjectInfo;
import com.ruoyi.system.domain.vo.fund.FundContractInfoVo;
import com.ruoyi.system.mapper.fund.FundContractInfoMapper;
import com.ruoyi.system.service.SysAttachmentService;
import com.ruoyi.system.service.fund.FundContractInfoService;
import com.ruoyi.system.service.project.ProjectInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FundContractInfoServiceImpl extends MPJBaseServiceImpl<FundContractInfoMapper, FundContractInfo> implements FundContractInfoService {

    private final ProjectInfoService projectInfoService;

    private final SysAttachmentService attachmentService;

    @Override
    public FundContractInfoVo getInfo(String id) {
        FundContractInfo contract = this.getById(id);
        if(contract == null){
            throw new ServiceException("合同信息不存在");
        }
        FundContractInfoVo vo = BeanUtil.copyProperties(contract, FundContractInfoVo.class);
        ProjectInfo project = projectInfoService.getById(vo.getProjId());
        List<SysAttachment> files = attachmentService.getListBySourceId(contract.getId());
        vo.setProjName(project.getName());
        vo.setFiles(files);
        return vo;
    }
}
