# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.7
  # 版权年份
  copyrightYear: 2023
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /Users/<USER>/Downloads
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8199
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 开启虚拟线程
  threads:
    virtual:
      enabled: true
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: dev
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  data:
    redis:
      # 地址
      host: localhost
      # 端口，默认为6379
      port: 6379
      # 数据库索引
      database: 0
      # 密码
      password:
      # 连接超时时间
      timeout: 10s
      lettuce:
        pool:
          # 连接池中的最小空闲连接
          min-idle: 0
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池的最大数据库连接数
          max-active: 8
          # #连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

## MyBatis配置
#mybatis:
#  # 搜索指定包别名
#  typeAliasesPackage: com.ruoyi.**.domain
#  # 配置mapper的扫描，找到所有的mapper.xml映射文件
#  mapperLocations: classpath*:mapper/**/*Mapper.xml
#  # 加载全局的配置文件
#  configLocation: classpath:mybatis/mybatis-config.xml

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

sms:
  service:
    username: HQDP
    password: cc9c61dc2ed2506bf6f7a4924992c72f
    url: https://sso.scip.gov.cn/scip-security/restapi/sms/created.do
  template:
    addBasketMessage: 您申报的项目“%s”已归入%s，入库金额%s万元。有任何疑惑请及时联系经发处。
    libMessage: 【项目库】您申报的项目“%s”已归入库，入库金额%s万元。有任何疑惑请及时联系经发处。
    checkMessage: 【项目库】项目“%s”已由“%s”审核通过，核定金额%s万元。有任何疑惑请及时联系经发处。
    updateInfoMessage: 【项目库】项目“%s”已由“%s”补正信息。
    reportProjectMessage: 【项目库】项目“%s”已由 “%s” 上报，请执行后续操作。
    reportPlanMessage: 【项目库】项目“%s”%s年度资金计划已由“%s”上报，请执行后续操作。

  # CAS 相关配置
cas:
  server:
    host:
      # CAS 服务地址
      url: http://demo.metinform.cn/scip-cas
      ticket_validator_url: http://cas-server-url:port/cas
      # CAS 服务登录地址
      login_url: ${cas.server.host.url}/login
      # CAS 服务登出地址
      logout_url: ${cas.server.host.url}/logout?service=${app.server.host.url}
# 应用访问地址
app:
  # 是否启用 CAS
  casEnable: false
  server:
    host:
      # 应用地址
      url: http://localhost:${server.port}
  # 应用登录地址
  login_url: /
  # 应用登出地址
  logout_url: /logout
  # 前端登录地址
  web_url: http://localhost:3003/login?redirect=/index
