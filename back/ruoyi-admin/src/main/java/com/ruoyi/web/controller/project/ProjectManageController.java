package com.ruoyi.web.controller.project;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.FillObjectHolder;
import com.ruoyi.system.domain.dto.ProjectAmountDto;
import com.ruoyi.system.domain.dto.ProjectBasketDto;
import com.ruoyi.system.domain.excel.ProjectInfoExcel;
import com.ruoyi.system.domain.excel.ProjectInfoMainExcel;
import com.ruoyi.system.domain.excel.ProjectInfoTotalExcel;
import com.ruoyi.system.domain.project.ProjectInfo;
import com.ruoyi.system.domain.vo.project.ProjectInfoVo;
import com.ruoyi.system.domain.vo.project.ProjectManageConditionVo;
import com.ruoyi.system.service.project.ProjectManageService;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目管理控制器
 *
 * <AUTHOR>
 * @date 2025/6/10 13:20
 */
@RestController
@RequestMapping("/project/manage")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ProjectManageController {

    private final ProjectManageService projectManageService;

    @ApiOperation("查询项目管理列表")
    @GetMapping("/list")
    public R<List<ProjectInfoVo>> normalList(ProjectManageConditionVo vo) {
        return R.ok(projectManageService.getProjectManageList(vo));
    }

    @ApiOperation("获取项目基本信息")
    @GetMapping("/{pid}")
    public R<ProjectInfo> getBasicInfo(@PathVariable("pid") String pid) {
        return R.ok(projectManageService.getProjectBasicInfo(pid));
    }

    @ApiOperation("添加重点项目")
    @PutMapping("/addImportantProject")
    public R<Void> addImportantProject(@NotNull(message = "项目id不能为空") String pid, @NotNull(message = "年不能为空") Integer year) {
        return projectManageService.addImportantProject(pid, year) ? R.ok() : R.fail();
    }

    @ApiOperation("取消重点项目")
    @PutMapping("/cancelImportantProject")
    public R<Void> cancelImportantProject(@NotNull(message = "项目id不能为空") String pid, @NotNull(message = "年不能为空") Integer year) {
        return projectManageService.cancelImportantProject(pid, year) ? R.ok() : R.fail();
    }

    @ApiOperation("添加项目篮子")
    @PutMapping("/addProjectBasket")
    public R<Void> addProjectBasket(@RequestBody ProjectBasketDto dto) {
        return projectManageService.addProjectBasket(dto) ? R.ok() : R.fail();
    }

    @ApiOperation("更新入库信息")
    @PutMapping("/updateLibInfo")
    public R<Void> updateLibInfo(@RequestBody ProjectAmountDto dto) {
        return projectManageService.updateLibInfo(dto) ? R.ok() : R.fail();
    }

    @ApiOperation("取消入库")
    @PutMapping("/cancelLib")
    public R<Void> cancelLib(@NotNull(message = "项目id不能为空") String pid) {
        return projectManageService.cancelLib(pid) ? R.ok() : R.fail();
    }

    @ApiOperation("更新核定信息")
    @PutMapping("/updateCheckInfo")
    public R<Void> updateCheckInfo(@RequestBody ProjectAmountDto dto) {
        return projectManageService.updateCheckInfo(dto) ? R.ok() : R.fail();
    }

    @ApiOperation("取消核定")
    @PutMapping("/cancelCheck")
    public R<Void> cancelCheck(@NotNull(message = "项目id不能为空") String pid) {
        return projectManageService.cancelCheck(pid) ? R.ok() : R.fail();
    }

    @ApiOperation("下达计划")
    @PutMapping("/releasePlan")
    public R<Void> releasePlan(@NotNull(message = "项目id不能为空") String pid,
                               @NotNull(message = "年不能为空") Integer year,
                               @NotNull(message = "金额不能为空") BigDecimal amount) {
        return projectManageService.releasePlan(pid, year, amount) ? R.ok() : R.fail();
    }

    @ApiOperation("双击下达计划")
    @PutMapping("/doubleClickRelease")
    public R<Void> doubleClickRelease(@NotNull(message = "项目id不能为空") String pid,
                                      @NotNull(message = "年不能为空") Integer year,
                                      @NotNull(message = "金额不能为空") BigDecimal amount) {
        return projectManageService.doubleClickRelease(pid, year, amount) ? R.ok() : R.fail(202, "未查询到更新数据");
    }

    @ApiOperation("取消下达计划")
    @PutMapping("/cancelRelease")
    public R<Void> cancelRelease(@NotNull(message = "项目id不能为空") String pid,
                                 @NotNull(message = "年不能为空") Integer year) {
        return projectManageService.cancelRelease(pid, year) ? R.ok() : R.fail();
    }

    @ApiOperation("调整计划")
    @PutMapping("/adjustPlan")
    public R<Void> adjustPlan(@NotNull(message = "项目id不能为空") String pid,
                              @NotNull(message = "年不能为空") Integer year,
                              @NotNull(message = "金额不能为空") BigDecimal amount) {
        return projectManageService.adjustPlan(pid, year, amount) ? R.ok() : R.fail();
    }

    @ApiOperation("双击调整计划")
    @PutMapping("/doubleClickAdjust")
    public R<Void> doubleClickAdjust(@NotNull(message = "项目id不能为空") String pid,
                                     @NotNull(message = "年不能为空") Integer year,
                                     @NotNull(message = "金额不能为空") BigDecimal amount) {
        return projectManageService.doubleClickAdjust(pid, year, amount) ? R.ok() : R.fail(202, "未查询到更新数据");
    }

    @ApiOperation("取消调整计划")
    @PutMapping("/cancelAdjust")
    public R<Void> cancelAdjust(@NotNull(message = "项目id不能为空") String pid,
                                @NotNull(message = "年不能为空") Integer year) {
        return projectManageService.cancelAdjust(pid, year) ? R.ok() : R.fail();
    }

    @ApiOperation("下载excel")
    @PostMapping("/download")
    public void exportExcel(ProjectManageConditionVo vo, HttpServletResponse response) throws IOException {

        ProjectInfoMainExcel mainExcel = projectManageService.getProjectInfoMainExcel(vo);
        List<ProjectInfoExcel> jzList = mainExcel.getJzList();
        ProjectInfoTotalExcel jzTotal = mainExcel.getJzTotal();
        List<ProjectInfoExcel> jcList = mainExcel.getJcList();
        ProjectInfoTotalExcel jcTotal = mainExcel.getJcTotal();
        List<ProjectInfoExcel> xzList = mainExcel.getXzList();
        ProjectInfoTotalExcel xzTotal = mainExcel.getXzTotal();
        ProjectInfoTotalExcel total = mainExcel.getTotal();

        String encodedFilename = URLEncoder.encode("项目(计划)管理.xlsx", StandardCharsets.UTF_8);
        // 设置响应头
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + encodedFilename + "\"");
        // 定义模板位置
        InputStream template = getClass().getClassLoader().getResourceAsStream("excel/管理模板.xlsx");
        assert template != null;
        Workbook workbook = WorkbookFactory.create(template);
        Sheet sheet = workbook.getSheetAt(0);

        // Excel索引从0开始
        if (xzList.isEmpty() && sheet.getRow(10) != null) {
            removeRow(sheet, 10);
        }
        if (jcList.isEmpty() && sheet.getRow(7) != null) {
            removeRow(sheet, 7);
        }
        if (jzList.isEmpty() && sheet.getRow(4) != null) {
            removeRow(sheet, 4);
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        template.close();
        workbook.close();
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(outputStream.toByteArray());
        // 使用EasyExcel写出
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerWriteHandler(new CellWriteHandler() {
                    @Override
                    public void afterCellDispose(CellWriteHandlerContext context) {
                        Object currentObject = FillObjectHolder.get();
                        Map<Integer, ProjectInfoExcel> map = null;
                        if (currentObject instanceof List<?> objectList) {
                            if (!objectList.isEmpty() && objectList.getFirst() instanceof ProjectInfoExcel) {
                                List<ProjectInfoExcel> projectInfoExcelList = (List<ProjectInfoExcel>) objectList;
                                // 使用 projectInfoExcelList 进行后续操作
                                map = projectInfoExcelList.stream()
                                        .filter(e -> Objects.nonNull(e.getIsFormalFlag()) || Objects.nonNull(e.getIsApplyAdjustFlag()) || Objects.nonNull(e.getIsAdjustAmountFlag()))
                                        .collect(Collectors.toMap(ProjectInfoExcel::getIndex, e -> e));
                            }
                        }
                        // 获取填充的值
                        Object cellValue = context.getOriginalValue();
                        WriteCellData<?> cellData = context.getFirstCellData();
                        WriteCellStyle cellStyle = cellData.getOrCreateStyle();
                        int columnIndex = context.getColumnIndex();

                        // 示例2：“未上报”列为红色字体
                        if (columnIndex == 12 && cellValue instanceof String val) {
                            if (val.contains("未上报")) {
                                WriteFont font = new WriteFont();
                                font.setColor(IndexedColors.RED.getIndex());
                                cellStyle.setWriteFont(font);
                            }
                        }
                        Cell cell = context.getRow().getCell(0);
                        if (cell != null && map != null && cell.getCellType() == CellType.NUMERIC){
                            double numericCellValue = cell.getNumericCellValue();
                            Set<Integer> integers = map.keySet();
                            boolean b = integers.stream().anyMatch(index -> index.doubleValue() == numericCellValue);
                            if (b) {
                                int index = (int) numericCellValue;
                                ProjectInfoExcel projectInfoExcel = map.get(index);
                                String isFormalFlag = projectInfoExcel.getIsFormalFlag();
                                String isApplyAdjustFlag = projectInfoExcel.getIsApplyAdjustFlag();
                                String isAdjustAmountFlag = projectInfoExcel.getIsAdjustAmountFlag();
                                if ("1".equals(isFormalFlag) && columnIndex == 13){
                                    WriteFont font = new WriteFont();
                                    font.setColor(IndexedColors.RED.getIndex());
                                    cellStyle.setWriteFont(font);
                                }
                                if ("2".equals(isApplyAdjustFlag) && columnIndex == 14){
                                    WriteFont font = new WriteFont();
                                    font.setColor(IndexedColors.GREY_25_PERCENT.getIndex());
                                    cellStyle.setWriteFont(font);
                                }

                                if ("1".equals(isAdjustAmountFlag) && columnIndex == 15){
                                    WriteFont font = new WriteFont();
                                    font.setColor(IndexedColors.RED.getIndex());
                                    cellStyle.setWriteFont(font);
                                } else if ("2".equals(isAdjustAmountFlag) && columnIndex == 15){
                                    WriteFont font = new WriteFont();
                                    font.setColor(IndexedColors.GREY_25_PERCENT.getIndex());
                                    cellStyle.setWriteFont(font);
                                }
                            }
                        }
                        cellData.setWriteCellStyle(cellStyle);
                    }
                })
                .withTemplate(byteArrayInputStream)
                .build();

        byteArrayInputStream.close();
        WriteSheet writeSheet = EasyExcel.writerSheet()
                .build();

        FillConfig fillConfig = FillConfig.builder().forceNewRow(true).build();
        // 填充数据
        excelWriter.fill(mainExcel, writeSheet);

        if (!xzList.isEmpty()) {
            FillObjectHolder.set(xzList);
            excelWriter.fill(new FillWrapper("xzList", xzList), fillConfig, writeSheet);
        }
        if (!jzList.isEmpty()) {
            FillObjectHolder.set(jzList);
            excelWriter.fill(new FillWrapper("jzList", jzList), fillConfig, writeSheet);
        }
        if (!jcList.isEmpty()) {
            FillObjectHolder.set(jcList);
            excelWriter.fill(new FillWrapper("jcList", jcList), fillConfig, writeSheet);
        }
        ArrayList<ProjectInfoTotalExcel> arr1 = new ArrayList<>();
        arr1.add(jzTotal);
        ArrayList<ProjectInfoTotalExcel> arr2 = new ArrayList<>();
        arr2.add(jcTotal);
        ArrayList<ProjectInfoTotalExcel> arr3 = new ArrayList<>();
        arr3.add(xzTotal);
        ArrayList<ProjectInfoTotalExcel> arr4 = new ArrayList<>();
        arr4.add(total);
        excelWriter.fill(new FillWrapper("jzTotal", arr1), fillConfig, writeSheet);
        excelWriter.fill(new FillWrapper("jcTotal", arr2), fillConfig, writeSheet);
        excelWriter.fill(new FillWrapper("xzTotal", arr3), fillConfig, writeSheet);
        excelWriter.fill(new FillWrapper("total", arr4), fillConfig, writeSheet);
        excelWriter.finish();

    }

    // 删除指定行并上移后续行
    private static void removeRow(Sheet sheet, int rowIndex) {
        int lastRowNum = sheet.getLastRowNum();
        if (rowIndex >= 0 && rowIndex < lastRowNum) {
            sheet.shiftRows(rowIndex + 1, lastRowNum, -1);
        } else if (rowIndex == lastRowNum) {
            Row removingRow = sheet.getRow(rowIndex);
            if (removingRow != null) {
                sheet.removeRow(removingRow);
            }
        }
    }

}
