package com.ruoyi.web.controller.system;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.resource.NoResourceException;
import cn.hutool.core.io.resource.ResourceUtil;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.file.MyFileUtils;
import com.ruoyi.system.domain.SysAttachment;
import com.ruoyi.system.service.SysAttachmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/5 13:52
 */
@Api(value = "附件控制器", tags = {"附件管理"})
@RestController
@RequestMapping("/resource/file")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class SysAttachmentController {

    private final SysAttachmentService sysAttachmentService;
    /**
     * 资源文件映射
     */
    public final Map<String, String> resoruceFileMap = new HashMap<String, String>(8) {
        {
//            put("key", "template/xxxx.xlsx");
        }
    };


    /**
     * 获取资源文件
     *
     * @param key      资源文件key
     * @param response 响应
     */
    @PostMapping("/get/{key}")
    @ApiOperation("获取资源文件")
    public void get(@ApiParam("资源key") @NotEmpty(message = "参数不能为空") @PathVariable String key, HttpServletResponse response) throws Exception {
        String resourceFileUrl = resoruceFileMap.get(key);
        if (resourceFileUrl == null) {
            throw new ServiceException("资源文件不存在");
        }
        InputStream inputStream = null;
        try {
            inputStream = ResourceUtil.getStream(resourceFileUrl);
        } catch (NoResourceException e) {
            throw new ServiceException("资源文件不存在");
        }
        String fileName = FileUtil.getName(resourceFileUrl);
        MyFileUtils.setAttachmentResponseHeader(response, fileName);
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE + "; charset=UTF-8");
        long data = IoUtil.copy(inputStream, response.getOutputStream());
        response.setContentLength(Convert.toInt(data));
    }

    @ApiOperation("上传附件")
    @PostMapping("/upload")
    public R<SysAttachment> uploadFile(@RequestParam("file") MultipartFile file, SysAttachment extraMessage) {
        return R.ok(sysAttachmentService.uploadFile(file, extraMessage));
    }

    @ApiOperation("根据源数据id查询附件列表")
    @GetMapping("/list")
    public R<List<SysAttachment>> getInfo(@NotEmpty(message = "源数据id不能为空") @RequestParam String sourceId, @RequestParam(required = false) String primaryType) {
        List<SysAttachment> list = sysAttachmentService.getListBySourceId(sourceId, primaryType);
        return R.ok(list);
    }

    @ApiOperation("删除附件")
    @DeleteMapping("/{id}")
    public R<Void> remove(@NotEmpty(message = "id不能为空") @PathVariable String id) {
        return sysAttachmentService.removeById(id) ? R.ok() : R.fail();
    }

    @ApiOperation("下载附件")
    @GetMapping("/download/{id}")
    public ResponseEntity<Resource> downloadFile(@NotEmpty(message = "id不能为空") @PathVariable String id, HttpServletResponse response) {
        try {
            SysAttachment sysAttachment = sysAttachmentService.getById(id);
            String fileName = sysAttachment.getFileName();
            String encodedFilename = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
            String filePath = sysAttachment.getFilePath();
            if (!FileUtils.checkAllowDownload(filePath)) {
                throw new ServiceException(StringUtils.format("资源文件({})非法，不允许下载。 ", filePath));
            }
            // 本地资源路径
            String localPath = RuoYiConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + StringUtils.substringAfter(filePath, Constants.RESOURCE_PREFIX);
            Path path = Paths.get(downloadPath);
            Resource resource = new UrlResource(path.toUri());
            if (resource.exists() || resource.isReadable()) {
                return ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + encodedFilename + "\"")
                        .body(resource);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(500).body(null);
        }
    }
}
