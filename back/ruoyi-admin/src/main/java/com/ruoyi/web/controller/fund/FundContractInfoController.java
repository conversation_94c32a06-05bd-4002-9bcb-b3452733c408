package com.ruoyi.web.controller.fund;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.web.DynamicQueryHelper;
import com.ruoyi.common.web.RequestHelper;
import com.ruoyi.system.domain.fund.FundContractInfo;
import com.ruoyi.system.service.fund.FundContractInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@Api(value = "合同表控制器", tags = {"合同表对象功能接口"})
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@RequestMapping("/fund/contractInfo")
public class FundContractInfoController {

    private final FundContractInfoService fundContractInfoService;

    /**
     * 查询合同管理列表
     */
    @ApiOperation("查询合同管理列表")
    @GetMapping("/list")
    public R<List<FundContractInfo>> list(RequestHelper requestHelper) {
        LambdaQueryWrapper<FundContractInfo> wrapper = DynamicQueryHelper.lambdaQueryWrapper(requestHelper);
        return R.ok(fundContractInfoService.list(wrapper));
    }
    /**
     * 获取合同管理详细信息
     */
    @ApiOperation("获取合同管理详细信息")
    @GetMapping("/{id}")
    public R<FundContractInfo> getInfo(@ApiParam("主键")
                                             @NotNull(message = "主键不能为空")
                                             @PathVariable("id") Integer id) {
        return R.ok(fundContractInfoService.getById(id));
    }

    /**
     * 新增合同表记录
     */
    @ApiOperation("新增合同表记录")
    @PostMapping()
    public R<Void> add(@RequestBody FundContractInfo entity) {
        return fundContractInfoService.save(entity) ? R.ok() : R.fail();
    }


    /**
     * 修改项目绩效指标库
     */
    @ApiOperation("修改合同表记录")
    @PutMapping()
    public R<Void> edit(@RequestBody FundContractInfo entity) {
        return fundContractInfoService.updateById(entity) ? R.ok() : R.fail();
    }

    /**
     * 删除项目绩效指标库
     */
    @ApiOperation("删除合同表记录")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Integer[] ids) {
        return fundContractInfoService.removeBatchByIds(Arrays.asList(ids)) ? R.ok() : R.fail();
    }
}
