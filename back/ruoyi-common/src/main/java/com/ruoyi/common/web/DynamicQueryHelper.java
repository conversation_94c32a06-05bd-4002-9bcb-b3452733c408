package com.ruoyi.common.web;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlInjectionUtils;
import com.ruoyi.common.constant.SearchSplitConstants;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * <AUTHOR>
 */
public class DynamicQueryHelper {
    private DynamicQueryHelper() {
    }

    public static <T> QueryWrapper<T> queryWrapper(RequestHelper requestHelper) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        return queryWrapper(requestHelper, wrapper, null);
    }

    public static <T> QueryWrapper<T> queryWrapper(RequestHelper requestHelper, String alias) {
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        return queryWrapper(requestHelper, wrapper, alias);
    }

    @SuppressWarnings("unchecked")
    public static <T> LambdaQueryWrapper<T> lambdaQueryWrapper(RequestHelper requestHelper, String alias) {
        return (LambdaQueryWrapper<T>) queryWrapper(requestHelper, alias).lambda();
    }

    @SuppressWarnings("unchecked")
    public static <T> LambdaQueryWrapper<T> lambdaQueryWrapper(RequestHelper requestHelper) {
        return (LambdaQueryWrapper<T>) queryWrapper(requestHelper).lambda();
    }


    public static <T> QueryWrapper<T> queryWrapper(RequestHelper requestHelper, QueryWrapper<T> wrapper) {
        return queryWrapper(requestHelper, wrapper, null);
    }

    public static <T> QueryWrapper<T> queryWrapper(RequestHelper requestHelper, QueryWrapper<T> wrapper, String alias) {

        if (ObjectUtil.isNotEmpty(requestHelper.getParams())) {
            return queryWrapper(requestHelper.getParams(), alias, wrapper);
        }
        return wrapper;
    }

    @SuppressWarnings("unchecked")
    public static <T> QueryWrapper<T> queryWrapper(Map<String, Object> map, String alias, QueryWrapper<T> wrapper) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            if (StringUtils.isNotBlank(alias)) {
                if (!key.contains(".")) {
                    key = alias + "." + key;
                }
            }
            Object value = entry.getValue();
            // sql注入过滤
            if (!key.matches("^[a-zA-Z._0-9]+$")) {
                throw new ServiceException("查询参数异常");
            }
            if (SqlInjectionUtils.check(key)) {
                throw new ServiceException("查询参数异常");
            }
            if (entry.getValue() instanceof String) {
                if (StringUtils.isBlank((String) value)) {
                    continue;
                }
                if (SqlInjectionUtils.check((String) value)) {
                    throw new ServiceException("查询参数异常");
                }
            }
            // 处理 右Like 模糊查询
            if (key.endsWith(SearchSplitConstants.SPLIT_RIGHT_LIKE.value)) {
                wrapper.likeRight(toUnderAndRemoveSuffix(key, SearchSplitConstants.SPLIT_RIGHT_LIKE), value);
            }
            // 处理 Like 模糊查询
            else if (key.endsWith(SearchSplitConstants.SPLIT_LIKE.value)) {
                wrapper.like(toUnderAndRemoveSuffix(key, SearchSplitConstants.SPLIT_LIKE), value);
            }
            // 处理 Like 模糊查询
            else if (key.endsWith(SearchSplitConstants.SPLIT_NE.value)) {
                wrapper.ne(toUnderAndRemoveSuffix(key, SearchSplitConstants.SPLIT_NE), value);
            }
            // 处理日期range 查询
            else if (key.endsWith(SearchSplitConstants.SPLIT_BETWEEN.value)) {
                String split = SearchSplitConstants.SPLIT_BETWEEN.value;
                String column = key.substring(0, key.length() - split.length());
                String[] date = new String[0];
                if (value instanceof String) {
                    date = ((String) value).split(",");

                } else if (value instanceof Collection) {
                    Collection<String> collection = (Collection<String>) value;
                    date = collection.toArray(new String[0]);
                }
                if (date.length == 2) {
                    wrapper.between(StrUtil.toUnderlineCase(column), date[0], date[1]);
                }

            }
            // 处理特殊情况下 日期 range 查询
            else if (key.endsWith(SearchSplitConstants.SPLIT_BETWEEN_STRING.value)) {
                String split = SearchSplitConstants.SPLIT_BETWEEN_STRING.value;
                String column = key.substring(0, key.length() - split.length());
                String[] date = new String[0];
                if (value instanceof String) {
                    date = ((String) value).split(",");
                } else if (value instanceof Collection) {
                    Collection<String> collection = (Collection<String>) value;
                    date = collection.toArray(new String[0]);
                }
                if (date.length == 2) {
                    wrapper.between("DATE_FORMAT(" + StrUtil.toUnderlineCase(column) + ",'%Y-%m-%d')", date[0], date[1]);
                }
            }
            // 处理 日期(特殊)
            else if (key.endsWith(SearchSplitConstants.CREATE_TIME_DATE.value)) {
                String column = key.replace(SearchSplitConstants.CREATE_TIME_DATE.value, "");
                if (null != value && !"".equals(value)) {
                    wrapper.eq("DATE_FORMAT(" + StrUtil.toUnderlineCase(column) + ",'%Y-%m-%d')", value);
                }
            }
            //小于
            else if (key.endsWith(SearchSplitConstants.SPLIT_LT.value)) {
                wrapper.lt(toUnderAndRemoveSuffix(key, SearchSplitConstants.SPLIT_LT), value);
            }
            //大于
            else if (key.endsWith(SearchSplitConstants.SPLIT_GT.value)) {
                wrapper.gt(toUnderAndRemoveSuffix(key, SearchSplitConstants.SPLIT_GT), value);
            }
            //大于等于
            else if (key.endsWith(SearchSplitConstants.SPLIT_GE.value)) {
                wrapper.ge(toUnderAndRemoveSuffix(key, SearchSplitConstants.SPLIT_GE), value);
            }
            //小于等于
            else if (key.endsWith(SearchSplitConstants.SPLIT_LE.value)) {
                wrapper.le(toUnderAndRemoveSuffix(key, SearchSplitConstants.SPLIT_LE), value);
            }
            // 处理 In
            else if (key.endsWith(SearchSplitConstants.WITH_IN.value)) {
                String split = SearchSplitConstants.WITH_IN.value;
                String column = key.substring(0, key.length() - split.length());
                if (null != value && !"".equals(value)) {
                    wrapper.in(StrUtil.toUnderlineCase(column), (Object) value.toString().split(","));
                }
            } else {
                if (null != value && !"".equals(value)) {
                    wrapper.eq(StrUtil.toUnderlineCase(key), value);
                }
            }
        }
        return wrapper;

    }

    public static String toUnderAndRemoveSuffix(String prams, SearchSplitConstants split) {
        return StrUtil.toUnderlineCase(prams.substring(0, prams.length() - split.value.length()));
    }
}
