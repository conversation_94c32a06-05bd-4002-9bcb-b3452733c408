package com.ruoyi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 常量 类
 * <AUTHOR>
 */
public class CommonConstant {

    /**
     * 项目或计划
     */
    @AllArgsConstructor
    @Getter
    public enum ProjectOrPlan {
        PROJECT("项目", "0"),
        PLAN("计划", "1");
        public final String name;
        public final String value;
    }

    /**
     * 操作类型
     */
    @AllArgsConstructor
    @Getter
    public enum OperateType {
        SUBMIT("提交", "0"),
        ACCEPT("受理", "1"),
        DISTRIBUTED("分送", "2"),
        APPROVAL("审批", "3");
        public final String name;
        public final String value;
    }


    /**
     * 源类型
     */
    @AllArgsConstructor
    @Getter
    public enum SourceType {
        PROJECT("项目", "0"),
        FUND_PLAN("资金计划", "1"),
        GRANT_SLIPS("拨款单", "2"),
        CONTRACT("合同", "3");
        public final String name;
        public final String value;
    }

    /**
     * 是否
     */
    @AllArgsConstructor
    @Getter
    public enum ZeroOrOne {
        ONE("1"),
        ZERO("0");
        public final String value;
    }

    /**
     * 项目类型
     */
    @AllArgsConstructor
    @Getter
    public enum ProjectType {
        CARRYOVER_PROJECT("结转项目",1),
        RECURRENT_PROJECT("经常性项目",2),
        NEW_PROJECT("新增项目", 3);
        public final String name;
        public final Integer value;
    }

    /**
     * 状态状态 （-1退回申报人、0草稿、1已上报、2已受理、3已分送、4已审批、9已发布）
     */
    @AllArgsConstructor
    @Getter
    public enum StateType {
        BACK("退回申报人",-1),
        DRAFT("草稿",0),
        ESCALATED("已上报",1),
        ACCEPTED("已受理",2),
        DISPATCHED("已分送",3),
        APPROVED("已审批",4),
        RELEASED("已发布",9);
        public final String name;
        public final Integer value;
    }

    @AllArgsConstructor
    @Getter
    public enum ApplyStateType {
        BACK("退回",-1),
        DRAFT("草稿",0),
        ESCALATED("已上报",1),
        CONFIRM("确认调整",2);

        public final String name;
        public final Integer value;
    }
    
    @AllArgsConstructor
    @Getter
    public enum UsagePlanColumnType {
        COLUMN_1("项目名称", "projName",50),
        COLUMN_2("项目篮子", "basketName",10),
        COLUMN_3("上报单位", "sbOrgname",15),
        COLUMN_4("考核主体", "assessOrgname", 15),
        COLUMN_5("项目性质", "natureName",10),
        COLUMN_6("项目估算", "estAmount",12),
        COLUMN_7("入库金额", "libAmount",12),
        COLUMN_8("核定金额", "checkAmount",12),
        COLUMN_9("合同金额", "contractAmount",12),
        COLUMN_10("至上年底累计执行", "prevAmount",12),
        COLUMN_11("上年调整计划", "prevAdjust",12),
        COLUMN_12("上年实际执行", "prevPayed",12),
        COLUMN_13("当年资金需求", "currAmount",12),
        COLUMN_14("当年初步使用计划", "currEarly",12),
        COLUMN_15("当年正式计划", "currFormal",12),
        COLUMN_16("当年调整计划", "currAdjust",12),
        COLUMN_17("当年已执行", "currPayed",12),
        COLUMN_18("开工(采购)时间", "beginDate",15),
        COLUMN_19("竣工(完成)时间", "endDate",15),
        COLUMN_20("备注1", "remark1",30),
        COLUMN_21("备注2", "remark2",30);
        public final String name;
        public final String value;
        public final int width;

        public static int getWidthByName(String  value) {
            for (UsagePlanColumnType columnType : UsagePlanColumnType.values()){
                if (columnType.value.equals(value)){
                    return columnType.width;
                }
            }
            return 10;
        }
    }

}
