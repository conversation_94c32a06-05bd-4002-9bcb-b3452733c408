package com.ruoyi.common.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/3 17:37
 */
@Getter
public enum SearchSplitConstants {
    SPLIT_LIKE("参数 Like 的 分割符", "Like"),
    SPLIT_RIGHT_LIKE("参数 RLike 的 分割符", "RightLike"),
    SPLIT_BETWEEN("参数 between 分隔符", "Between"),
    SPLIT_BETWEEN_STRING("参数 between 分隔符(特殊)", "BetweenS"),
    CREATE_TIME_DATE("参数 date 分割符", "SDate"),
    //小于
    SPLIT_LT("参数 lt 分割符","Lt"),
    //大于
    SPLIT_GT("参数 gt 分割符","Gt"),
    //小于 等于
    SPLIT_LE("参数 le 分割符","Le"),
    //大于 等于
    SPLIT_GE("参数 ge 分割符","Ge"),
    //不等于
    SPLIT_NE("参数 ne 分割符","Ne"),
    WITH_IN("参数In 分隔符", "InWith");

    public final String name;
    public final String value;

    SearchSplitConstants(String name, String inWith) {
        this.name = name;
        this.value = inWith;
    }
}
