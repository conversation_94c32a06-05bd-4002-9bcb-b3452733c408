package com.ruoyi.common.core.handler;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;

import java.util.Date;

/**
 * mybatis-plus 自动填充配置
 *
 * <AUTHOR>
 * @Date 2025/06/03 15:44:40
 */
@Slf4j
public class MybatisAutoFillHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        try {
            LoginUser loginUser = getLoginUser();
            if(loginUser != null){
                this.strictInsertFill(metaObject, "createBy", String.class, loginUser.getUsername());
                this.strictInsertFill(metaObject, "updateBy", String.class, loginUser.getUsername());
            }
            this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
            this.strictInsertFill(metaObject, "updateTime", Date.class, new Date());
        } catch (Exception e) {
            throw new ServiceException("自动注入异常 => " + e.getMessage());
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {

        try {
            LoginUser user = getLoginUser();

            if (ObjectUtil.isNotNull(metaObject)) {
                // 更新时间填充(不管为不为空)
                this.strictInsertFill(metaObject, "updateTime", Date.class, new Date());
                // 当前已登录 更新人填充(不管为不为空)
                if( user != null){
                    this.strictInsertFill(metaObject, "updateBy", String.class, user.getUsername());
                }
            }
        } catch (Exception e) {
            throw new ServiceException("自动注入异常 => " + e.getMessage());
        }
    }

    /**
     * 获取登录用户名
     */
    private LoginUser getLoginUser() {
        LoginUser loginUser;
        try {
            loginUser = SecurityUtils.getLoginUser();
        } catch (Exception e) {
            log.warn("自动注入警告 => 用户未登录");
            return null;
        }
        return loginUser;
    }

}
