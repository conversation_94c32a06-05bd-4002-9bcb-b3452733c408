package com.ruoyi.common.web;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sql.SqlUtil;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;

import java.io.Serializable;
import java.util.*;

/**
 * 请求帮助类
 */
@Data
public class RequestHelper implements Serializable {

    /**
     * 排序的字段名
     */
    private String sortField;

    /**
     * 排序方式
     */
    private Boolean isAsc;

    /**
     * 列表 页
     */
    private Integer pageNum;

    /**
     * 列表页size
     */
    private Integer pageSize;

    /**
     * 参数
     */
    private Map<String, Object> params;

    /**
     * 排序的字段名
     * "createTime:desc"
     */
    private List<String> sortFields;

    /**
     * 构建分页参数
     */
    public <T> Page<T> build() {
        Integer pageNum = ObjectUtil.defaultIfNull(getPageNum(), 1);
        Integer pageSize = ObjectUtil.defaultIfNull(getPageSize(), Integer.MAX_VALUE);

        if (pageNum <= 0) {
            pageNum = 1;
        }
        Page<T> page = new Page<>(pageNum, pageSize);
        OrderItem orderItem = buildOrderItem(sortField, isAsc);
        if (ObjectUtil.isNotNull(orderItem)) {
            page.addOrder(orderItem);
        }

        List<OrderItem> orderItems = buildOrderList();
        if (ObjectUtil.isNotEmpty(orderItems)) {
            page.addOrder(orderItems);
        }

        return page;
    }

    /**
     * 生成排序条件
     */
    private OrderItem buildOrderItem(String sortField, Boolean isAsc) {
        if (StringUtils.isBlank(sortField)) {
            return null;
        }

        String orderBy = SqlUtil.escapeOrderBySql(sortField);
        orderBy = StringUtils.toUnderScoreCase(orderBy);
        return BooleanUtils.isNotFalse(isAsc) ? OrderItem.asc(orderBy) : OrderItem.desc(orderBy);
    }

    private List<OrderItem> buildOrderList() {
        if (ObjectUtil.isEmpty(sortFields)) {
            return null;
        }
        List<OrderItem> list = new ArrayList<>();
        sortFields.forEach(string -> {
            String[] split = string.split(":");
            boolean isDesc = false;
            if (split.length > 1) {
                isDesc = "desc".equals(split[1].trim()) || !BooleanUtils.toBoolean(split[1].trim());
            }
            list.add(buildOrderItem(split[0].trim(), !isDesc));
        });
        return list;
    }

    /**
     * 添加查询参数
     */
    public void addParams(String key, Object value) {
        if (ObjectUtils.isEmpty(params)) {
            this.params = new HashMap<>(8);
        }
        this.params.put(key, value);
    }

    /**
     * 删除查询参数
     */
    public void removeParams(String... keys) {
        if (ObjectUtils.isNotEmpty(this.params)) {
            for (String key : keys) {
                this.params.remove(key);
            }
        }

    }

    @SuppressWarnings("unchecked")
    public <T> T getParam(String key) {
        if (ObjectUtil.isEmpty(this.params)) {
            return null;
        }
        Optional<Object> obj = Optional.ofNullable(this.params.get(key));
        return (T) obj.orElse(null);
    }

    @SuppressWarnings("unchecked")
    public <T> T pop(String key) {
        if (ObjectUtil.isEmpty(this.params)) {
            return null;
        }
        Optional<Object> obj = Optional.ofNullable(this.params.get(key));
        if (obj.isPresent()) {
            this.params.remove(key);
            return (T) obj.get();
        }

        return null;
    }
}
